#!/usr/bin/env python3
"""
Exemplo prático de uso do OSF Scraper

Este script demonstra como usar a função de web scraping do OSF.io
de forma simples e prática.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from osf_scraper import search_osf

def exemplo_basico():
    """Exemplo básico de uso"""
    print("=== Exemplo Básico ===")
    print("Buscando por 'data science' no OSF.io...\n")
    
    # Realizar busca
    resultados = search_osf("data science", max_results=3)
    
    # Exibir resultados
    if resultados:
        print(f"Encontrados {len(resultados)} resultados:\n")
        
        for i, resultado in enumerate(resultados, 1):
            titulo = resultado.get('title', 'Sem título')
            url = resultado.get('url', 'N/A')
            
            print(f"{i}. {titulo}")
            print(f"   URL: {url}")
            print()
    else:
        print("Nenhum resultado encontrado.")

def exemplo_com_detalhes():
    """Exemplo mostrando mais detalhes dos resultados"""
    print("=== Exemplo com Detalhes ===")
    print("Buscando por 'psychology' no OSF.io...\n")
    
    # Realizar busca
    resultados = search_osf("psychology", max_results=2)
    
    # Exibir resultados com mais detalhes
    if resultados:
        print(f"Encontrados {len(resultados)} resultados:\n")
        
        for i, resultado in enumerate(resultados, 1):
            print(f"--- Resultado {i} ---")
            print(f"Título: {resultado.get('title', 'Sem título')}")
            print(f"URL: {resultado.get('url', 'N/A')}")
            
            if resultado.get('authors'):
                print(f"Autores: {resultado['authors']}")
            
            if resultado.get('type'):
                print(f"Tipo: {resultado['type']}")
            
            if resultado.get('date'):
                print(f"Data: {resultado['date']}")
            
            if resultado.get('description'):
                desc = resultado['description']
                if len(desc) > 100:
                    desc = desc[:100] + "..."
                print(f"Descrição: {desc}")
            
            print()
    else:
        print("Nenhum resultado encontrado.")

def exemplo_multiplas_buscas():
    """Exemplo com múltiplas buscas"""
    print("=== Exemplo com Múltiplas Buscas ===")
    
    termos = ["python", "neuroscience", "education"]
    
    for termo in termos:
        print(f"Buscando por '{termo}'...")
        
        try:
            resultados = search_osf(termo, max_results=2)
            
            if resultados:
                print(f"  ✓ {len(resultados)} resultados encontrados")
                for resultado in resultados:
                    titulo = resultado.get('title', 'Sem título')
                    if len(titulo) > 50:
                        titulo = titulo[:50] + "..."
                    print(f"    - {titulo}")
            else:
                print("  ✗ Nenhum resultado encontrado")
                
        except Exception as e:
            print(f"  ✗ Erro: {e}")
        
        print()

def exemplo_interativo():
    """Exemplo interativo onde o usuário pode inserir termos"""
    print("=== Exemplo Interativo ===")
    print("Digite um termo de busca (ou 'sair' para terminar):")
    
    while True:
        termo = input("\nTermo de busca: ").strip()
        
        if termo.lower() in ['sair', 'exit', 'quit']:
            print("Saindo...")
            break
        
        if not termo:
            print("Por favor, digite um termo válido.")
            continue
        
        print(f"Buscando por '{termo}'...")
        
        try:
            resultados = search_osf(termo, max_results=3)
            
            if resultados:
                print(f"\nEncontrados {len(resultados)} resultados:")
                
                for i, resultado in enumerate(resultados, 1):
                    titulo = resultado.get('title', 'Sem título')
                    url = resultado.get('url', 'N/A')
                    
                    if len(titulo) > 60:
                        titulo = titulo[:60] + "..."
                    
                    print(f"{i}. {titulo}")
                    print(f"   {url}")
            else:
                print("Nenhum resultado encontrado.")
                
        except Exception as e:
            print(f"Erro durante a busca: {e}")

def main():
    """Função principal"""
    print("OSF.io Web Scraper - Exemplos de Uso")
    print("=" * 50)
    
    # Executar exemplos
    exemplo_basico()
    print("\n" + "="*50 + "\n")
    
    exemplo_com_detalhes()
    print("\n" + "="*50 + "\n")
    
    exemplo_multiplas_buscas()
    print("\n" + "="*50 + "\n")
    
    # Perguntar se quer exemplo interativo
    resposta = input("Deseja testar o exemplo interativo? (s/n): ").strip().lower()
    if resposta in ['s', 'sim', 'y', 'yes']:
        exemplo_interativo()
    
    print("\nExemplos concluídos!")

if __name__ == "__main__":
    main()
