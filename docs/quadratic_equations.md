# 📐 Resolvedor de Equações Quadráticas

Este módulo fornece uma implementação completa para resolver equações de segundo grau (quadráticas) da forma **ax² + bx + c = 0**.

## 🚀 Funcionalidades

### ✅ Tipos de Equações Suportadas

1. **Equações Quadráticas** (a ≠ 0)
   - Duas raízes reais distintas (Δ > 0)
   - Uma raiz real dupla (Δ = 0)
   - <PERSON>as raízes complexas conjugadas (Δ < 0)

2. **Equações Lineares** (a = 0, b ≠ 0)
   - Uma solução única

3. **Casos Especiais**
   - Equação indeterminada (a = 0, b = 0, c = 0)
   - Equação impossível (a = 0, b = 0, c ≠ 0)

### 📊 Análise Completa

Para cada equação, o sistema fornece:

- **Discriminante (Δ)**: b² - 4ac
- **Vértice da parábola**: (-b/2a, f(-b/2a))
- **Eixo de simetria**: x = -b/2a
- **Concavidade**: Para cima (a > 0) ou para baixo (a < 0)
- **Interceptações**: Com eixos x e y
- **Forma fatorada**: Quando aplicável
- **Gráfico detalhado**: Com zoom nas raízes e análise da derivada

## 🛠️ Como Usar

### Importação

```python
from src.quadratic_solver import solve_quadratic, format_solution, plot_quadratic
```

### Função Principal

```python
def solve_quadratic(a: float, b: float, c: float) -> Dict:
    """
    Resolve uma equação quadrática ax² + bx + c = 0.
    
    Args:
        a: Coeficiente de x²
        b: Coeficiente de x
        c: Termo independente
    
    Returns:
        Dicionário com informações completas sobre a solução
    """
```

### Exemplo de Uso

```python
# Resolver x² - 5x + 6 = 0
solution = solve_quadratic(1, -5, 6)

# Formatar resultado para exibição
formatted_text = format_solution(solution)
print(formatted_text)

# Criar gráfico
fig = plot_quadratic(1, -5, 6, solution)
fig.show()
```

## 📈 Exemplos Práticos

### 1. Duas Raízes Reais Distintas
```python
# x² - 5x + 6 = 0
# Raízes: x₁ = 3, x₂ = 2
solve_quadratic(1, -5, 6)
```

### 2. Raiz Dupla
```python
# x² - 4x + 4 = 0
# Raiz: x = 2 (dupla)
solve_quadratic(1, -4, 4)
```

### 3. Raízes Complexas
```python
# x² + x + 1 = 0
# Raízes: x₁ = -0.5 + 0.866i, x₂ = -0.5 - 0.866i
solve_quadratic(1, 1, 1)
```

### 4. Equação Linear
```python
# 2x - 4 = 0
# Solução: x = 2
solve_quadratic(0, 2, -4)
```

### 5. Parábola para Baixo
```python
# -x² + 2x + 3 = 0
# Raízes: x₁ = -1, x₂ = 3
solve_quadratic(-1, 2, 3)
```

## 🎯 Estrutura do Resultado

O dicionário retornado pela função `solve_quadratic()` contém:

```python
{
    'coefficients': {'a': float, 'b': float, 'c': float},
    'equation_type': str,           # 'quadratic', 'linear', 'indeterminate', 'impossible'
    'discriminant': float,          # Apenas para equações quadráticas
    'roots': list,                  # Lista das raízes
    'roots_type': str,              # Descrição do tipo de raízes
    'vertex': tuple,                # (x, y) do vértice
    'axis_of_symmetry': float,      # x do eixo de simetria
    'y_intercept': float,           # Interceptação com eixo y
    'concavity': str,               # 'para cima' ou 'para baixo'
    'factored_form': str,           # Forma fatorada (quando aplicável)
    'has_real_roots': bool          # Se possui raízes reais
}
```

## 🎨 Visualização

O módulo gera gráficos detalhados com:

1. **Gráfico Principal**: Função completa com marcação de raízes e vértice
2. **Zoom nas Raízes**: Visualização detalhada da região das raízes
3. **Informações da Função**: Análise textual das propriedades
4. **Derivada**: Gráfico da derivada f'(x) = 2ax + b

## 🧪 Testes

Execute o módulo diretamente para ver exemplos:

```bash
python src/quadratic_solver.py
```

## 🌐 Interface Web

A função está integrada à aplicação Gradio principal, disponível na aba "📐 Equações Quadráticas" com:

- Interface intuitiva para entrada de coeficientes
- Exemplos pré-definidos
- Visualização em tempo real
- Análise completa da equação

## 📚 Teoria Matemática

### Discriminante (Δ)
- **Δ = b² - 4ac**
- Δ > 0: Duas raízes reais distintas
- Δ = 0: Uma raiz real (dupla)
- Δ < 0: Duas raízes complexas conjugadas

### Fórmula de Bhaskara
- **x = (-b ± √Δ) / 2a**

### Vértice da Parábola
- **x_v = -b / 2a**
- **y_v = f(x_v) = a(x_v)² + b(x_v) + c**

### Forma Fatorada
- **f(x) = a(x - x₁)(x - x₂)** (duas raízes distintas)
- **f(x) = a(x - x₀)²** (raiz dupla)

## 🔧 Dependências

- **NumPy**: Cálculos numéricos
- **Matplotlib**: Visualização de gráficos
- **Python 3.8+**: Suporte a type hints

## 📝 Notas

- O módulo trata todos os casos especiais automaticamente
- Suporta números complexos para raízes imaginárias
- Gera visualizações detalhadas para análise
- Código bem documentado e testado
- Interface amigável através do Gradio
