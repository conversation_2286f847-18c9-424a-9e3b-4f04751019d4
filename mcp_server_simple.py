#!/usr/bin/env python3
"""
Servidor MCP Simples para testar funcionalidade básica.
"""

import gradio as gr
import json


def solve_quadratic_simple(a: str, b: str, c: str) -> str:
    """
    Resolve uma equação quadrática ax² + bx + c = 0.
    
    Args:
        a (str): Coeficiente de x²
        b (str): Coeficiente de x  
        c (str): Termo independente
    
    Returns:
        str: Resultado em formato JSON
    """
    try:
        a_val = float(a)
        b_val = float(b) 
        c_val = float(c)
        
        if a_val == 0:
            if b_val == 0:
                result = {"error": "Não é uma equação válida"}
            else:
                x = -c_val / b_val
                result = {"type": "linear", "root": x}
        else:
            discriminant = b_val**2 - 4*a_val*c_val
            
            if discriminant > 0:
                x1 = (-b_val + (discriminant**0.5)) / (2*a_val)
                x2 = (-b_val - (discriminant**0.5)) / (2*a_val)
                result = {"type": "quadratic", "roots": [x1, x2], "discriminant": discriminant}
            elif discriminant == 0:
                x = -b_val / (2*a_val)
                result = {"type": "quadratic", "root": x, "discriminant": discriminant}
            else:
                real = -b_val / (2*a_val)
                imag = (abs(discriminant)**0.5) / (2*a_val)
                result = {"type": "quadratic", "complex_roots": [f"{real}+{imag}i", f"{real}-{imag}i"], "discriminant": discriminant}
        
        return json.dumps(result, indent=2)
        
    except Exception as e:
        return json.dumps({"error": str(e)}, indent=2)


def create_simple_interface():
    """Cria interface simples para teste."""
    
    with gr.Blocks(title="Servidor MCP Simples") as demo:
        gr.Markdown("# 🧮 Servidor MCP - Teste Simples")
        gr.Markdown("Servidor MCP básico para resolver equações quadráticas")
        
        with gr.Row():
            with gr.Column():
                a_input = gr.Textbox(label="a", value="1")
                b_input = gr.Textbox(label="b", value="-5") 
                c_input = gr.Textbox(label="c", value="6")
                solve_btn = gr.Button("Resolver")
            
            with gr.Column():
                output = gr.JSON(label="Resultado")
        
        solve_btn.click(
            solve_quadratic_simple,
            inputs=[a_input, b_input, c_input],
            outputs=[output]
        )
        
        gr.Markdown("""
        ### 📡 Configuração MCP
        **URL:** `http://localhost:7862/gradio_api/mcp/sse`
        
        **Configuração para Claude:**
        ```json
        {
          "mcpServers": {
            "quadratic-simple": {
              "url": "http://localhost:7862/gradio_api/mcp/sse"
            }
          }
        }
        ```
        """)
    
    return demo


def main():
    print("🚀 Iniciando Servidor MCP Simples...")
    print("📡 URL: http://localhost:7862/gradio_api/mcp/sse")
    print("🌐 Interface: http://localhost:7862")
    
    demo = create_simple_interface()
    demo.launch(
        server_name="0.0.0.0",
        server_port=7862,
        mcp_server=True,
        share=False,
        show_error=True
    )


if __name__ == "__main__":
    main()
