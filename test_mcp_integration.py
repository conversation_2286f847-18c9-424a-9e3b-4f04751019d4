#!/usr/bin/env python3
"""
Teste da integração OSF no servidor MCP
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from main import osf_search_projects, osf_download_files, osf_analyze_data, osf_pipeline_complete

def test_osf_search():
    """Testa a função de busca"""
    print("🔍 Testando busca OSF...")
    
    result = osf_search_projects("data", 3)
    print("Resultado da busca:")
    print(result)
    print("-" * 50)

def test_osf_download():
    """Testa a função de download"""
    print("📥 Testando download OSF...")
    
    result = osf_download_files("https://osf.io/j4bv6/", "test_mcp_downloads")
    print("Resultado do download:")
    print(result)
    print("-" * 50)

def test_osf_analysis():
    """Testa a função de análise"""
    print("📊 Testando análise OSF...")
    
    result_text, plot = osf_analyze_data("test_mcp_downloads")
    print("Resultado da análise:")
    print(result_text)
    
    if plot:
        print("✅ Gráfico gerado com sucesso")
    else:
        print("⚠️ Nenhum gráfico gerado")
    
    print("-" * 50)

def test_osf_pipeline():
    """Testa o pipeline completo"""
    print("🚀 Testando pipeline completo...")
    
    result_text, plot = osf_pipeline_complete("psychology", 2)
    print("Resultado do pipeline:")
    print(result_text)
    
    if plot:
        print("✅ Gráfico do pipeline gerado com sucesso")
    else:
        print("⚠️ Nenhum gráfico do pipeline gerado")
    
    print("-" * 50)

def main():
    """Função principal de teste"""
    print("TESTE DE INTEGRAÇÃO MCP + OSF")
    print("=" * 60)
    
    try:
        # Teste 1: Busca
        test_osf_search()
        
        # Teste 2: Download
        test_osf_download()
        
        # Teste 3: Análise
        test_osf_analysis()
        
        # Teste 4: Pipeline
        test_osf_pipeline()
        
        print("✅ TODOS OS TESTES CONCLUÍDOS!")
        print("\n🌐 Servidor MCP disponível em: http://localhost:7860")
        print("📂 Nova aba: '🔬 OSF.io Research Data'")
        
    except Exception as e:
        print(f"❌ Erro durante os testes: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
