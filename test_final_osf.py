#!/usr/bin/env python3
"""
Teste final do OSF Scraper
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from osf_scraper import search_osf

def test_multiple_searches():
    """Testa múltiplas buscas"""
    
    search_terms = [
        "python",
        "psychology", 
        "data science",
        "neuroscience"
    ]
    
    print("=== Teste Final do OSF Scraper ===\n")
    
    for term in search_terms:
        print(f"Buscando por: '{term}'")
        print("-" * 40)
        
        try:
            results = search_osf(term, max_results=3, use_selenium=True)
            
            if results:
                print(f"✓ Encontrados {len(results)} resultados:")
                
                for i, result in enumerate(results, 1):
                    title = result.get('title', 'Sem título')
                    url = result.get('url', 'N/A')
                    
                    # Limitar título para exibição
                    if len(title) > 60:
                        title = title[:60] + "..."
                    
                    print(f"  {i}. {title}")
                    print(f"     URL: {url}")
                    
                    if result.get('authors'):
                        print(f"     Autores: {result['authors']}")
                    
                    print()
            else:
                print("✗ Nenhum resultado encontrado")
                
        except Exception as e:
            print(f"✗ Erro: {e}")
        
        print("-" * 50)
        print()

if __name__ == "__main__":
    test_multiple_searches()
