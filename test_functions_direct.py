#!/usr/bin/env python3
"""
Teste direto das funções do servidor para verificar se estão funcionando.
"""

import sys
import os
import time

# Adicionar o diretório atual ao path
sys.path.append('.')

def test_search_function():
    """Testa a função de busca diretamente."""
    print("🔍 Testando função de busca...")
    
    try:
        # Importar e testar
        from gemini_mcp_server import gemini_server
        
        # Testar busca
        result = gemini_server.search_osf_with_gemini("machine learning", 3)
        
        print("✅ Função de busca executada!")
        print(f"📊 Resultado ({len(result)} caracteres):")
        print("-" * 50)
        print(result)
        print("-" * 50)
        
        return True
        
    except Exception as e:
        print(f"❌ Erro: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_download_function():
    """Testa a função de download diretamente."""
    print("\n📥 Testando função de download...")
    
    try:
        # Importar e testar
        from gemini_mcp_server import gemini_server
        
        # Testar download
        result = gemini_server.download_osf_files_with_gemini("https://osf.io/j4bv6/", "test_downloads")
        
        print("✅ Função de download executada!")
        print(f"📊 Resultado ({len(result)} caracteres):")
        print("-" * 50)
        print(result)
        print("-" * 50)
        
        return True
        
    except Exception as e:
        print(f"❌ Erro: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_process_request():
    """Testa a função de processamento."""
    print("\n🤖 Testando função de processamento...")
    
    try:
        # Importar e testar
        from gemini_mcp_server import process_user_request
        
        # Testar busca
        response, json_data = process_user_request("Buscar projetos sobre machine learning")
        
        print("✅ Função de processamento executada!")
        print(f"📊 Resposta ({len(response)} caracteres):")
        print("-" * 50)
        print(response)
        print("-" * 50)
        print(f"📈 JSON: {json_data}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_mcp_tools():
    """Testa as ferramentas MCP diretamente."""
    print("\n🔧 Testando ferramentas MCP...")
    
    try:
        # Importar e testar
        from gemini_mcp_server import search_osf_mcp_tool, download_osf_mcp_tool
        
        # Testar busca MCP
        print("🔍 Testando search_osf_mcp_tool...")
        search_result = search_osf_mcp_tool("machine learning", "3")
        print(f"✅ Busca MCP: {len(search_result)} caracteres")
        print(search_result[:200] + "..." if len(search_result) > 200 else search_result)
        
        # Testar download MCP
        print("\n📥 Testando download_osf_mcp_tool...")
        download_result = download_osf_mcp_tool("https://osf.io/j4bv6/", "test_mcp_downloads")
        print(f"✅ Download MCP: {len(download_result)} caracteres")
        print(download_result[:200] + "..." if len(download_result) > 200 else download_result)
        
        return True
        
    except Exception as e:
        print(f"❌ Erro: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Função principal."""
    print("🧪 TESTE DIRETO DAS FUNÇÕES")
    print("=" * 60)
    
    # Aguardar um pouco para o servidor inicializar
    print("⏳ Aguardando servidor inicializar...")
    time.sleep(2)
    
    tests = [
        ("Busca OSF", test_search_function),
        ("Download OSF", test_download_function),
        ("Processamento", test_process_request),
        ("Ferramentas MCP", test_mcp_tools)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"❌ Erro crítico em {test_name}: {e}")
            results.append((test_name, False))
    
    # Resumo
    print("\n" + "="*60)
    print("📊 RESUMO DOS TESTES")
    print("="*60)
    
    for test_name, success in results:
        status = "✅ PASSOU" if success else "❌ FALHOU"
        print(f"{test_name:20} {status}")
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    print(f"\n🎯 Resultado: {passed}/{total} testes passaram")
    
    if passed == total:
        print("🎉 Todas as funções estão funcionando!")
        print("\n💡 Agora teste na interface web:")
        print("   1. Acesse: http://localhost:7861")
        print("   2. Digite: 'Buscar machine learning'")
        print("   3. Clique em 'Processar com Gráficos e Gemini'")
    else:
        print("⚠️ Algumas funções falharam.")

if __name__ == "__main__":
    main()
