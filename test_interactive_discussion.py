#!/usr/bin/env python3
"""
Teste da funcionalidade de discussão interativa com Gemini.

Este script demonstra como usar a API do servidor interativo
para resolver equações e discutir soluções.
"""

import requests
import json
import time

def test_interactive_discussion():
    """Testa a discussão interativa."""
    
    print("🧪 Teste de Discussão Interativa com Gemini")
    print("=" * 50)
    
    base_url = "http://localhost:7861"
    
    # Verificar se servidor está rodando
    try:
        response = requests.get(base_url, timeout=5)
        print("✅ Servidor interativo está rodando")
    except:
        print("❌ Servidor não está rodando. Execute: python gemini_interactive_server.py")
        return
    
    # Teste 1: Resolver equação
    print("\n🔍 Teste 1: Resolvendo equação x² - 5x + 6 = 0")
    
    try:
        solve_response = requests.post(
            f"{base_url}/gradio_api/predict",
            json={
                "fn_index": 0,  # Função solve_equation
                "data": ["Resolva a equação x² - 5x + 6 = 0"]
            },
            timeout=30
        )
        
        if solve_response.status_code == 200:
            result = solve_response.json()
            if "data" in result and len(result["data"]) >= 2:
                explanation = result["data"][0]
                json_result = result["data"][1]
                
                print("✅ Equação resolvida com sucesso!")
                print(f"📝 Explicação: {explanation[:200]}...")
                print(f"📊 JSON: {json_result[:100]}...")
            else:
                print("⚠️ Formato de resposta inesperado")
        else:
            print(f"❌ Erro HTTP {solve_response.status_code}")
            
    except Exception as e:
        print(f"❌ Erro ao resolver equação: {e}")
        return
    
    time.sleep(2)
    
    # Teste 2: Discussão sobre a solução
    print("\n💬 Teste 2: Fazendo perguntas sobre a solução")
    
    questions = [
        "Por que o discriminante é 1?",
        "Como posso verificar se x = 3 é uma raiz?",
        "Qual é o significado geométrico desta equação?",
        "Existe outro método para resolver esta equação?"
    ]
    
    for i, question in enumerate(questions, 1):
        print(f"\n❓ Pergunta {i}: {question}")
        
        try:
            # Simular discussão (nota: isso requer interface web real)
            # Em um teste real, você usaria a interface web
            print("💭 (Simulação) Gemini responderia com explicação detalhada...")
            print("   Exemplo: 'O discriminante é 1 porque b² - 4ac = 25 - 24 = 1'")
            
        except Exception as e:
            print(f"❌ Erro na discussão: {e}")
        
        time.sleep(1)
    
    # Teste 3: Verificar schema MCP
    print("\n🔧 Teste 3: Verificando ferramentas MCP disponíveis")
    
    try:
        schema_response = requests.get(f"{base_url}/gradio_api/mcp/schema", timeout=5)
        if schema_response.status_code == 200:
            schema = schema_response.json()
            print(f"✅ Schema MCP disponível com {len(schema)} ferramentas:")
            for tool in schema:
                name = tool.get('name', 'unnamed')
                description = tool.get('description', 'No description')
                print(f"   • {name}: {description[:50]}...")
        else:
            print("⚠️ Schema MCP não disponível")
    except Exception as e:
        print(f"❌ Erro ao acessar schema: {e}")
    
    print("\n" + "=" * 50)
    print("🎯 Teste Concluído!")
    print("\n💡 Para testar a discussão interativa completa:")
    print("   1. Acesse: http://localhost:7861")
    print("   2. Resolva uma equação na coluna esquerda")
    print("   3. Faça perguntas na coluna direita")
    print("   4. Converse com o Gemini sobre a solução!")


def demo_discussion_examples():
    """Mostra exemplos de discussões possíveis."""
    
    print("\n🎓 Exemplos de Discussões Interativas")
    print("=" * 40)
    
    examples = [
        {
            "equation": "x² - 5x + 6 = 0",
            "questions": [
                "As raízes x₁=3 e x₂=2 estão corretas?",
                "Como verificar substituindo na equação?",
                "Por que o discriminante é positivo?",
                "Qual é a forma fatorada desta equação?"
            ]
        },
        {
            "equation": "x² + x + 1 = 0", 
            "questions": [
                "Por que não há raízes reais?",
                "O que são raízes complexas?",
                "Como interpretar geometricamente?",
                "A parábola intercepta o eixo x?"
            ]
        },
        {
            "equation": "2x² - 4x + 1 = 0",
            "questions": [
                "Como o coeficiente 'a=2' afeta a solução?",
                "Posso simplificar dividindo por 2?",
                "Qual é o vértice desta parábola?",
                "Como resolver por completar quadrado?"
            ]
        }
    ]
    
    for i, example in enumerate(examples, 1):
        print(f"\n📐 Exemplo {i}: {example['equation']}")
        print("💬 Perguntas possíveis:")
        for j, question in enumerate(example['questions'], 1):
            print(f"   {j}. {question}")
    
    print("\n💡 Dica: Use a interface web para ter conversas reais com o Gemini!")


if __name__ == "__main__":
    test_interactive_discussion()
    demo_discussion_examples()
