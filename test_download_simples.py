#!/usr/bin/env python3
"""
Teste simples de download
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_download_direto():
    """Teste direto da função de download"""
    print("=== Teste Direto de Download ===")
    
    from osf_scraper import download_osf_files
    
    project_url = "https://osf.io/2zfu4/"
    print(f"Testando download de: {project_url}")
    
    try:
        files = download_osf_files(project_url, "test_download_direto")
        
        print(f"Resultado: {len(files)} arquivos")
        
        for file_info in files:
            print(f"- {file_info['name']}")
            print(f"  URL original: {file_info.get('original_url', 'N/A')}")
            print(f"  Local: {file_info['local_path']}")
            print(f"  Bytes: {file_info['file_size_bytes']}")

            # Verificar se arquivo existe
            if os.path.exists(file_info['local_path']):
                print(f"  ✓ Arquivo salvo com sucesso!")
            else:
                print(f"  ✗ Arquivo não encontrado")
            
    except Exception as e:
        print(f"Erro: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_download_direto()
