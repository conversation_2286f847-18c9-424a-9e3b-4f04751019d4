#!/usr/bin/env python3
"""
Servidor MCP interativo com Google Gemini para discussão de soluções.

Este servidor permite discussão interativa com o Gemini sobre soluções de equações,
correções, explicações adicionais e exploração de conceitos matemáticos.
"""

import os
import json
import gradio as gr
import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, Tuple, List, Union, Optional

# Importar dotenv
try:
    from dotenv import load_dotenv
    load_dotenv()
    HAS_DOTENV = True
except ImportError:
    HAS_DOTENV = False

# Importar Google Gemini
try:
    import google.generativeai as genai
    HAS_GEMINI = True
except ImportError:
    HAS_GEMINI = False

# Importar módulo de equações quadráticas
try:
    from src.quadratic_solver import solve_quadratic, format_solution
    HAS_QUADRATIC_SOLVER = True
except ImportError:
    HAS_QUADRATIC_SOLVER = False
    
    # Implementação básica
    def solve_quadratic(a, b, c):
        result = {'coefficients': {'a': a, 'b': b, 'c': c}, 'equation_type': 'quadratic' if a != 0 else 'linear', 'discriminant': None, 'roots': [], 'roots_type': '', 'vertex': None, 'axis_of_symmetry': None, 'y_intercept': c, 'concavity': '', 'factored_form': '', 'has_real_roots': False}
        if a == 0:
            if b == 0:
                result['equation_type'] = 'invalid'
                result['roots_type'] = 'sem solução válida'
            else:
                result['equation_type'] = 'linear'
                root = -c / b
                result['roots'] = [root]
                result['roots_type'] = 'uma raiz real'
                result['has_real_roots'] = True
        else:
            discriminant = b**2 - 4*a*c
            result['discriminant'] = discriminant
            result['vertex'] = (-b/(2*a), a*(-b/(2*a))**2 + b*(-b/(2*a)) + c)
            result['axis_of_symmetry'] = -b/(2*a)
            result['concavity'] = 'para cima' if a > 0 else 'para baixo'
            if discriminant > 0:
                x1 = (-b + discriminant**0.5) / (2*a)
                x2 = (-b - discriminant**0.5) / (2*a)
                result['roots'] = [x1, x2]
                result['roots_type'] = 'duas raízes reais distintas'
                result['has_real_roots'] = True
                result['factored_form'] = f"{a}(x - {x1:.3f})(x - {x2:.3f})"
            elif discriminant == 0:
                x = -b / (2*a)
                result['roots'] = [x]
                result['roots_type'] = 'uma raiz real (raiz dupla)'
                result['has_real_roots'] = True
                result['factored_form'] = f"{a}(x - {x:.3f})²"
            else:
                real_part = -b / (2*a)
                imag_part = (-discriminant)**0.5 / (2*a)
                x1 = complex(real_part, imag_part)
                x2 = complex(real_part, -imag_part)
                result['roots'] = [x1, x2]
                result['roots_type'] = 'duas raízes complexas conjugadas'
                result['has_real_roots'] = False
        return result
    
    def format_solution(solution):
        a, b, c = solution['coefficients']['a'], solution['coefficients']['b'], solution['coefficients']['c']
        if solution['equation_type'] == 'invalid':
            return "❌ Equação inválida"
        elif solution['equation_type'] == 'linear':
            x = solution['roots'][0]
            return f"📐 Equação Linear: {b}x + {c} = 0\n✅ Solução: x = {x:.6f}"
        else:
            text = f"📐 Equação Quadrática: {a}x² + {b}x + {c} = 0\n"
            text += f"🔍 Discriminante: {solution['discriminant']:.6f}\n"
            text += f"✅ {solution['roots_type']}\n"
            if solution['has_real_roots']:
                for i, root in enumerate(solution['roots'], 1):
                    if isinstance(root, complex):
                        text += f"x{i} = {root.real:.6f} + {root.imag:.6f}i\n"
                    else:
                        text += f"x{i} = {root:.6f}\n"
            return text


class InteractiveGeminiMCP:
    """Servidor MCP interativo com Gemini."""
    
    def __init__(self):
        self.gemini_model = None
        self.conversation_history = []
        self.current_equation = None
        self.current_solution = None
        self.setup_gemini()
    
    def setup_gemini(self):
        """Configura o modelo Gemini."""
        if not HAS_GEMINI:
            return
        
        api_key = os.getenv("GOOGLE_API_KEY")
        if not api_key:
            return
        
        try:
            genai.configure(api_key=api_key)
            try:
                self.gemini_model = genai.GenerativeModel('gemini-1.5-flash')
                print("✅ Google Gemini configurado (gemini-1.5-flash)")
            except:
                try:
                    self.gemini_model = genai.GenerativeModel('gemini-1.5-pro')
                    print("✅ Google Gemini configurado (gemini-1.5-pro)")
                except:
                    self.gemini_model = genai.GenerativeModel('models/gemini-1.5-flash-latest')
                    print("✅ Google Gemini configurado (gemini-1.5-flash-latest)")
        except Exception as e:
            print(f"❌ Erro ao configurar Gemini: {e}")
    
    def extract_coefficients(self, text: str) -> Optional[Tuple[float, float, float]]:
        """Extrai coeficientes usando Gemini."""
        if not self.gemini_model:
            return None
        
        prompt = f"""
Extraia os coeficientes a, b, c da equação quadrática no seguinte texto:
"{text}"

A equação pode estar na forma:
- ax² + bx + c = 0
- f(x) = ax² + bx + c
- Problema contextual que resulta em equação quadrática

Responda APENAS com três números separados por vírgula, na ordem a,b,c
Exemplo: 1,-5,6

Se não conseguir identificar uma equação quadrática válida, responda: ERRO

Texto: {text}
"""
        
        try:
            response = self.gemini_model.generate_content(prompt)
            result = response.text.strip()
            
            if result == "ERRO":
                return None
            
            coeffs = result.split(',')
            if len(coeffs) == 3:
                a = float(coeffs[0].strip())
                b = float(coeffs[1].strip())
                c = float(coeffs[2].strip())
                return (a, b, c)
            else:
                return None
                
        except Exception as e:
            print(f"Erro ao extrair coeficientes: {e}")
            return None
    
    def solve_equation(self, user_input: str) -> Tuple[str, str]:
        """Resolve equação e inicia contexto para discussão."""
        if not user_input.strip():
            return "❌ Por favor, digite uma equação quadrática.", "{}"
        
        # Extrair coeficientes
        coeffs = self.extract_coefficients(user_input)
        
        if not coeffs:
            return "❌ Não consegui identificar os coeficientes da equação. Tente um formato como 'x² - 5x + 6 = 0'", "{}"
        
        a, b, c = coeffs
        
        # Resolver usando MCP
        try:
            mcp_result = solve_quadratic(a, b, c)
            
            # Salvar contexto atual
            self.current_equation = {"input": user_input, "coefficients": (a, b, c)}
            self.current_solution = mcp_result
            
            # Limpar histórico anterior
            self.conversation_history = []
            
            # Gerar explicação inicial
            if self.gemini_model:
                explanation = self._generate_initial_explanation(user_input, mcp_result)
            else:
                explanation = self._basic_explanation(mcp_result)
            
            # Adicionar ao histórico
            self.conversation_history.append({
                "type": "solution",
                "input": user_input,
                "explanation": explanation,
                "result": mcp_result
            })
            
            json_result = json.dumps(mcp_result, indent=2, ensure_ascii=False)
            
            return explanation, json_result
            
        except Exception as e:
            return f"❌ Erro no cálculo: {str(e)}", "{}"
    
    def discuss_solution(self, question: str, chat_history: List) -> Tuple[List, str]:
        """Permite discussão interativa sobre a solução."""
        if not question.strip():
            return chat_history, ""
        
        if not self.current_solution or not self.gemini_model:
            response = "❌ Primeiro resolva uma equação para poder discuti-la."
            chat_history.append([question, response])
            return chat_history, ""
        
        # Construir contexto da conversa
        context = self._build_conversation_context()
        
        # Prompt para discussão
        discussion_prompt = f"""
Você é um professor de matemática especializado em equações quadráticas. 

CONTEXTO DA EQUAÇÃO ATUAL:
{context}

HISTÓRICO DA CONVERSA:
{self._format_chat_history()}

PERGUNTA DO ESTUDANTE:
{question}

Responda de forma didática e educativa. Você pode:
1. Explicar conceitos matemáticos
2. Corrigir erros se houver
3. Mostrar métodos alternativos
4. Dar exemplos práticos
5. Verificar cálculos
6. Explorar aplicações

Seja paciente, claro e use exemplos quando necessário.
"""
        
        try:
            response = self.gemini_model.generate_content(discussion_prompt)
            answer = response.text
            
            # Adicionar ao histórico
            self.conversation_history.append({
                "type": "discussion",
                "question": question,
                "answer": answer
            })
            
            # Atualizar chat history
            chat_history.append([question, answer])
            
            return chat_history, ""
            
        except Exception as e:
            error_msg = f"❌ Erro na discussão: {str(e)}"
            chat_history.append([question, error_msg])
            return chat_history, ""
    
    def _generate_initial_explanation(self, user_input: str, mcp_result: Dict) -> str:
        """Gera explicação inicial detalhada."""
        prompt = f"""
Como professor de matemática, forneça uma explicação completa e didática para:

PROBLEMA ORIGINAL: {user_input}
RESULTADO DO CÁLCULO: {json.dumps(mcp_result, indent=2, ensure_ascii=False)}

Sua explicação deve incluir:
1. Identificação da equação na forma padrão
2. Explicação dos coeficientes e seu significado
3. Cálculo e interpretação do discriminante
4. Método de resolução (fórmula de Bhaskara)
5. Interpretação das raízes encontradas
6. Verificação das soluções
7. Aplicações práticas (se aplicável)

Seja didático, use emojis para tornar interessante, e explique cada passo claramente.
Termine convidando o estudante a fazer perguntas sobre a solução.
"""
        
        try:
            response = self.gemini_model.generate_content(prompt)
            return response.text + "\n\n💬 **Tem alguma dúvida sobre esta solução? Pergunte-me qualquer coisa!**"
        except Exception as e:
            return self._basic_explanation(mcp_result)
    
    def _basic_explanation(self, mcp_result: Dict) -> str:
        """Explicação básica quando Gemini não está disponível."""
        a, b, c = mcp_result['coefficients']['a'], mcp_result['coefficients']['b'], mcp_result['coefficients']['c']
        
        explanation = f"""
# 📐 Solução da Equação Quadrática

## Equação: {a}x² + {b}x + {c} = 0

## Coeficientes:
- a = {a}
- b = {b}
- c = {c}

## Resultado:
{format_solution(mcp_result)}

## Análise:
- Tipo: {mcp_result['equation_type']}
- Raízes: {mcp_result['roots_type']}
- Discriminante: {mcp_result.get('discriminant', 'N/A')}

💬 **Gemini não disponível para discussão interativa.**
"""
        return explanation
    
    def _build_conversation_context(self) -> str:
        """Constrói contexto da conversa atual."""
        if not self.current_equation or not self.current_solution:
            return "Nenhuma equação resolvida."
        
        a, b, c = self.current_equation['coefficients']
        return f"""
Equação Original: {self.current_equation['input']}
Forma Padrão: {a}x² + {b}x + {c} = 0
Coeficientes: a={a}, b={b}, c={c}
Tipo: {self.current_solution['equation_type']}
Raízes: {self.current_solution['roots']}
Discriminante: {self.current_solution.get('discriminant', 'N/A')}
"""
    
    def _format_chat_history(self) -> str:
        """Formata histórico da conversa."""
        if not self.conversation_history:
            return "Nenhuma conversa anterior."
        
        formatted = []
        for entry in self.conversation_history[-5:]:  # Últimas 5 interações
            if entry['type'] == 'discussion':
                formatted.append(f"Pergunta: {entry['question']}")
                formatted.append(f"Resposta: {entry['answer'][:200]}...")
        
        return "\n".join(formatted) if formatted else "Nenhuma discussão anterior."


# Instância global
interactive_server = InteractiveGeminiMCP()


def create_interactive_interface():
    """Cria interface interativa com discussão."""
    
    with gr.Blocks(
        title="Gemini Interativo - Equações Quadráticas",
        theme=gr.themes.Soft(),
        css="""
        .gradio-container { max-width: 1400px !important; }
        .gemini-header {
            background: linear-gradient(135deg, #4285f4 0%, #34a853 50%, #fbbc05 100%);
            color: white; padding: 20px; border-radius: 10px; margin: 10px 0; text-align: center;
        }
        .chat-container { height: 400px; overflow-y: auto; }
        """
    ) as demo:
        
        gr.HTML("""
        <div class="gemini-header">
            <h1>🤖💬 Gemini Interativo - Equações Quadráticas</h1>
            <p><strong>Resolva, Discuta e Aprenda com IA</strong></p>
            <p>Resolva equações e depois converse com o Gemini sobre a solução!</p>
        </div>
        """)
        
        with gr.Row():
            # Coluna esquerda: Resolução
            with gr.Column(scale=1):
                gr.Markdown("### 🔍 Resolver Equação")
                
                equation_input = gr.Textbox(
                    label="Digite sua equação quadrática",
                    placeholder="Ex: Resolva x² - 5x + 6 = 0",
                    lines=2
                )
                
                solve_btn = gr.Button("🚀 Resolver", variant="primary")
                
                gr.Markdown("#### 📚 Exemplos Rápidos")
                with gr.Row():
                    ex1_btn = gr.Button("x² - 5x + 6 = 0", size="sm")
                    ex2_btn = gr.Button("2x² - 4x + 1 = 0", size="sm")
                with gr.Row():
                    ex3_btn = gr.Button("x² + x + 1 = 0", size="sm")
                    ex4_btn = gr.Button("Área = 21, lados x+3 e x-1", size="sm")
                
                # Resultado JSON
                json_output = gr.JSON(label="Resultado Técnico", visible=False)
            
            # Coluna direita: Discussão
            with gr.Column(scale=2):
                gr.Markdown("### 💬 Discussão com Gemini")
                
                # Chat interface
                chatbot = gr.Chatbot(
                    label="Conversa sobre a Solução",
                    height=400,
                    placeholder="Resolva uma equação primeiro, depois faça perguntas sobre a solução!"
                )
                
                with gr.Row():
                    question_input = gr.Textbox(
                        label="Sua pergunta",
                        placeholder="Ex: Por que o discriminante é positivo? Como verificar a resposta?",
                        scale=4
                    )
                    ask_btn = gr.Button("💬 Perguntar", scale=1, variant="secondary")
                
                clear_chat_btn = gr.Button("🗑️ Limpar Conversa", size="sm")
        
        # Área de explicação principal
        explanation_output = gr.Markdown(
            label="Explicação Detalhada",
            value="👆 Digite uma equação acima para ver a explicação completa!"
        )
        
        # Eventos
        solve_btn.click(
            interactive_server.solve_equation,
            inputs=[equation_input],
            outputs=[explanation_output, json_output]
        ).then(
            lambda: [],  # Limpar chat ao resolver nova equação
            outputs=[chatbot]
        )
        
        ask_btn.click(
            interactive_server.discuss_solution,
            inputs=[question_input, chatbot],
            outputs=[chatbot, question_input]
        )
        
        question_input.submit(
            interactive_server.discuss_solution,
            inputs=[question_input, chatbot],
            outputs=[chatbot, question_input]
        )
        
        clear_chat_btn.click(
            lambda: [],
            outputs=[chatbot]
        )
        
        # Exemplos
        ex1_btn.click(lambda: "Resolva a equação x² - 5x + 6 = 0", outputs=[equation_input])
        ex2_btn.click(lambda: "Encontre as raízes de 2x² - 4x + 1 = 0", outputs=[equation_input])
        ex3_btn.click(lambda: "Analise a equação x² + x + 1 = 0", outputs=[equation_input])
        ex4_btn.click(lambda: "Um retângulo tem área 21. Um lado é x+3, outro é x-1. Qual o valor de x?", outputs=[equation_input])
        
        # Sugestões de perguntas
        gr.Markdown("""
        ### 💡 Sugestões de Perguntas para Discussão:
        - "Por que o discriminante determina o tipo de raízes?"
        - "Como posso verificar se as raízes estão corretas?"
        - "Qual é o significado geométrico desta equação?"
        - "Existe outro método para resolver esta equação?"
        - "Como aplicar isso em problemas do mundo real?"
        - "O que acontece se eu mudar o coeficiente 'a'?"
        """)
    
    return demo


def main():
    """Função principal."""
    print("🚀 Iniciando Servidor Gemini Interativo...")
    print("=" * 60)
    
    # Status
    print("📦 Status das Dependências:")
    print(f"   • python-dotenv: {'✅' if HAS_DOTENV else '❌'}")
    print(f"   • google-generativeai: {'✅' if HAS_GEMINI else '❌'}")
    print(f"   • quadratic_solver: {'✅' if HAS_QUADRATIC_SOLVER else '❌ (básico)'}")
    print(f"   • Gemini Model: {'✅' if interactive_server.gemini_model else '❌'}")
    
    # Configurações
    host = os.getenv("MCP_SERVER_HOST", "0.0.0.0")
    port = int(os.getenv("MCP_SERVER_PORT", "7861"))
    
    print(f"\n📡 Servidor:")
    print(f"   • URL: http://localhost:{port}")
    print(f"   • MCP: http://localhost:{port}/gradio_api/mcp/sse")
    
    # Lançar
    demo = create_interactive_interface()
    
    print(f"\n🎯 Acesse: http://localhost:{port}")
    print("💬 Resolva equações e discuta as soluções com o Gemini!")
    
    demo.launch(
        server_name=host,
        server_port=port,
        mcp_server=True,
        share=False,
        show_error=True
    )


if __name__ == "__main__":
    main()
