#!/usr/bin/env python3
"""
Demo do OSF Scraper

Script demonstrativo para mostrar como usar o web scraper do OSF.io
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from osf_scraper import search_osf


def demo_busca_interativa():
    """Demo interativo onde o usuário pode inserir termos de busca"""
    print("=== Demo Interativo do OSF Scraper ===")
    print("Digite termos de busca para encontrar projetos no OSF.io")
    print("Digite 'quit' para sair\n")
    
    while True:
        try:
            # Solicitar termo de busca
            query = input("Digite um termo de busca: ").strip()
            
            if query.lower() in ['quit', 'exit', 'sair', 'q']:
                print("Saindo...")
                break
                
            if not query:
                print("Por favor, digite um termo de busca válido.\n")
                continue
            
            print(f"\nBuscando por: '{query}'...")
            print("-" * 40)
            
            # Realizar busca
            results = search_osf(query, max_results=5, use_selenium=True)
            
            if results:
                print(f"Encontrados {len(results)} resultados:\n")
                
                for i, result in enumerate(results, 1):
                    title = result.get('title', 'Sem título')
                    url = result.get('url', 'N/A')
                    authors = result.get('authors', 'N/A')
                    
                    print(f"{i}. {title}")
                    print(f"   URL: {url}")
                    if authors != 'N/A':
                        print(f"   Autores: {authors}")
                    
                    if result.get('description'):
                        desc = result['description']
                        if len(desc) > 100:
                            desc = desc[:100] + "..."
                        print(f"   Descrição: {desc}")
                    
                    print()
            else:
                print("Nenhum resultado encontrado.")
                print("Nota: O OSF.io pode estar bloqueando requisições automatizadas.")
            
            print("-" * 40)
            print()
            
        except KeyboardInterrupt:
            print("\n\nInterrompido pelo usuário. Saindo...")
            break
        except Exception as e:
            print(f"Erro durante a busca: {e}")
            print("Tente novamente com um termo diferente.\n")


def demo_busca_predefinida():
    """Demo com buscas predefinidas"""
    print("=== Demo com Buscas Predefinidas ===\n")

    termos_busca = [
        "python",
        "machine learning",
        "psychology",
        "data science",
        "neuroscience"
    ]

    print("Executando buscas automáticas no OSF.io...")
    print("(Isso pode levar alguns segundos por busca)\n")

    for i, termo in enumerate(termos_busca, 1):
        print(f"Busca {i}/5: '{termo}'")
        print("-" * 40)

        try:
            results = search_osf(termo, max_results=3, use_selenium=True)

            if results:
                print(f"✓ Encontrados {len(results)} resultados:")

                for j, result in enumerate(results, 1):
                    title = result.get('title', 'Sem título')
                    url = result.get('url', 'N/A')

                    # Limitar título para exibição
                    if len(title) > 60:
                        title = title[:60] + "..."

                    print(f"  {j}. {title}")
                    print(f"     URL: {url}")

                    if result.get('type'):
                        print(f"     Tipo: {result['type']}")

                    print()
            else:
                print("  ✗ Nenhum resultado encontrado.")

        except Exception as e:
            print(f"  ✗ Erro: {e}")

        print("-" * 50)
        print()


def main():
    """Função principal"""
    print("OSF.io Web Scraper - Demonstração")
    print("=" * 50)
    
    while True:
        print("\nEscolha uma opção:")
        print("1. Demo interativo (você digita os termos)")
        print("2. Demo com buscas predefinidas")
        print("3. Sair")
        
        escolha = input("\nDigite sua escolha (1-3): ").strip()
        
        if escolha == '1':
            demo_busca_interativa()
        elif escolha == '2':
            demo_busca_predefinida()
        elif escolha == '3':
            print("Saindo...")
            break
        else:
            print("Opção inválida. Tente novamente.")


if __name__ == "__main__":
    main()
