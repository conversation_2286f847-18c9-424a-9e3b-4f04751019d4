#!/usr/bin/env python3
"""
Debug detalhado para entender a estrutura de arquivos do OSF.io
"""

import sys
import os
import time
from bs4 import BeautifulSoup

# Adicionar src ao path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def debug_files_detailed():
    """Debug detalhado da página de arquivos"""
    print("=== Debug Detalhado: Files OSF.io ===")
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.chrome.service import Service
        from webdriver_manager.chrome import ChromeDriverManager
        from selenium.webdriver.common.by import By
        from selenium.webdriver.support.ui import WebDriverWait
        from selenium.webdriver.support import expected_conditions as EC
        
        # Configurar Chrome
        chrome_options = Options()
        chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)
        
        try:
            # Acessar diretamente a página de files
            files_url = "https://osf.io/j4bv6/files/osfstorage"
            print(f"Acessando: {files_url}")
            
            driver.get(files_url)
            time.sleep(8)  # Aguardar mais tempo para carregar
            
            print(f"URL final: {driver.current_url}")
            
            # Analisar o HTML
            soup = BeautifulSoup(driver.page_source, 'html.parser')
            
            # Procurar por elementos específicos de arquivos
            print("\n=== Analisando estrutura de arquivos ===")
            
            # Procurar por links que contenham IDs de arquivo
            all_links = soup.find_all('a', href=True)
            file_links = []
            
            for link in all_links:
                href = link.get('href', '')
                text = link.get_text(strip=True)
                
                # Procurar por padrões de arquivo OSF
                if any(pattern in href for pattern in ['/files/osfstorage/', '/download', '?action=download']):
                    file_links.append({
                        'href': href,
                        'text': text,
                        'full_url': href if href.startswith('http') else f"https://osf.io{href}"
                    })
            
            print(f"Links de arquivo encontrados: {len(file_links)}")
            for i, link in enumerate(file_links):
                print(f"  {i+1}. '{link['text'][:60]}' -> {link['href']}")
            
            # Procurar por elementos que podem conter metadados de arquivo
            print("\n=== Procurando metadados de arquivos ===")
            
            # Procurar por elementos com classes relacionadas a arquivos
            file_elements = soup.find_all(attrs={'class': True})
            relevant_elements = []
            
            for elem in file_elements:
                classes = elem.get('class', [])
                class_str = ' '.join(classes).lower()
                
                if any(keyword in class_str for keyword in ['file', 'item', 'row', 'tree', 'browser']):
                    text = elem.get_text(strip=True)
                    if text and len(text) > 5 and len(text) < 200:  # Filtrar elementos relevantes
                        relevant_elements.append({
                            'classes': classes,
                            'text': text,
                            'tag': elem.name
                        })
            
            print(f"Elementos relevantes encontrados: {len(relevant_elements)}")
            for i, elem in enumerate(relevant_elements[:10]):  # Mostrar apenas os primeiros 10
                print(f"  {i+1}. {elem['tag']} ({elem['classes'][:2]}): {elem['text'][:50]}...")
            
            # Procurar por estruturas de dados JavaScript
            print("\n=== Procurando dados JavaScript ===")
            
            scripts = soup.find_all('script')
            for i, script in enumerate(scripts):
                script_content = script.get_text()
                if 'file' in script_content.lower() and ('download' in script_content.lower() or 'osfstorage' in script_content.lower()):
                    print(f"Script {i+1} contém dados de arquivo:")
                    # Mostrar apenas uma parte do script
                    lines = script_content.split('\n')
                    for line in lines[:5]:
                        if 'file' in line.lower() or 'download' in line.lower():
                            print(f"  {line.strip()[:100]}...")
                    print()
            
            # Tentar encontrar elementos específicos do OSF
            print("\n=== Procurando elementos específicos do OSF ===")
            
            # Procurar por elementos com data attributes
            data_elements = soup.find_all(attrs=lambda x: x and any(key.startswith('data-') for key in x.keys()))
            
            osf_data_elements = []
            for elem in data_elements:
                attrs = elem.attrs
                for key, value in attrs.items():
                    if key.startswith('data-') and ('file' in str(value).lower() or 'osf' in str(value).lower()):
                        osf_data_elements.append({
                            'tag': elem.name,
                            'attr': key,
                            'value': value,
                            'text': elem.get_text(strip=True)[:50]
                        })
            
            print(f"Elementos com data attributes OSF: {len(osf_data_elements)}")
            for elem in osf_data_elements[:5]:
                print(f"  {elem['tag']}[{elem['attr']}='{elem['value']}'] -> {elem['text']}...")
            
            # Verificar se há iframes ou conteúdo carregado dinamicamente
            print("\n=== Verificando conteúdo dinâmico ===")
            
            iframes = soup.find_all('iframe')
            print(f"iframes encontrados: {len(iframes)}")
            
            for iframe in iframes:
                src = iframe.get('src', '')
                if src:
                    print(f"  iframe src: {src}")
            
            # Aguardar mais tempo e verificar novamente
            print("\nAguardando carregamento adicional...")
            time.sleep(10)
            
            # Tentar clicar em elementos que podem expandir a lista de arquivos
            try:
                # Procurar por botões ou elementos clicáveis
                clickable_elements = driver.find_elements(By.CSS_SELECTOR, "button, [role='button'], .clickable")
                print(f"Elementos clicáveis encontrados: {len(clickable_elements)}")
                
                for elem in clickable_elements[:3]:
                    try:
                        text = elem.text.strip()
                        if text and ('file' in text.lower() or 'expand' in text.lower() or 'show' in text.lower()):
                            print(f"  Tentando clicar em: {text}")
                            elem.click()
                            time.sleep(2)
                    except:
                        pass
                        
            except Exception as e:
                print(f"Erro ao tentar clicar em elementos: {e}")
            
            # Analisar novamente após interações
            soup_final = BeautifulSoup(driver.page_source, 'html.parser')
            final_links = soup_final.find_all('a', href=True)
            
            print(f"\nLinks finais encontrados: {len(final_links)}")
            
            # Procurar especificamente por links de download
            download_links_final = []
            for link in final_links:
                href = link.get('href', '')
                text = link.get_text(strip=True)
                
                if ('download' in href.lower() or 
                    '/files/osfstorage/' in href or 
                    '?action=download' in href or
                    href.endswith('.pdf') or href.endswith('.csv') or href.endswith('.xlsx')):
                    
                    download_links_final.append({
                        'href': href,
                        'text': text,
                        'full_url': href if href.startswith('http') else f"https://osf.io{href}"
                    })
            
            print(f"Links de download finais: {len(download_links_final)}")
            for i, link in enumerate(download_links_final):
                print(f"  {i+1}. '{link['text'][:60]}' -> {link['href']}")
            
        finally:
            driver.quit()
            
    except Exception as e:
        print(f"Erro durante debug: {e}")

def main():
    """Função principal"""
    print("OSF.io Files Detailed Debug")
    print("=" * 50)
    
    debug_files_detailed()
    
    print("\n" + "=" * 50)
    print("Debug concluído!")

if __name__ == "__main__":
    main()
