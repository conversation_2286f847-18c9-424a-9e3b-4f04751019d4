#!/usr/bin/env python3
"""
Teste da relevância da busca OSF.
"""

import sys
import time

# Adicionar o diretório atual ao path
sys.path.append('.')

def test_search_relevance():
    """Testa se a busca retorna resultados relevantes."""
    print("🔍 Testando Relevância da Busca OSF")
    print("=" * 50)
    
    try:
        from gemini_mcp_server import gemini_server
        
        # Diferentes tipos de busca para testar
        test_queries = [
            "machine learning",
            "psychology",
            "neuroscience", 
            "data analysis",
            "medical research",
            "education",
            "climate change",
            "artificial intelligence",
            "cognitive science",
            "brain imaging"
        ]
        
        for query in test_queries:
            print(f"\n🔍 Testando busca: '{query}'")
            print("-" * 40)
            
            result = gemini_server.search_osf_with_gemini(query, 3)
            
            # Extrair títulos dos resultados
            lines = result.split('\n')
            titles = []
            for line in lines:
                if line.strip().startswith('**') and line.strip().endswith('**'):
                    title = line.strip().replace('**', '')
                    if title and not title.startswith('🤖'):
                        titles.append(title)
            
            print(f"📋 Resultados encontrados:")
            for i, title in enumerate(titles, 1):
                print(f"   {i}. {title}")
            
            # Verificar relevância básica
            query_words = query.lower().split()
            relevant_count = 0
            
            for title in titles:
                title_lower = title.lower()
                if any(word in title_lower for word in query_words):
                    relevant_count += 1
                # Verificar sinônimos básicos
                elif query.lower() in ['machine learning', 'ai'] and any(term in title_lower for term in ['ml', 'artificial', 'deep', 'neural']):
                    relevant_count += 1
                elif query.lower() == 'psychology' and any(term in title_lower for term in ['cognitive', 'behavior', 'emotion']):
                    relevant_count += 1
                elif query.lower() == 'neuroscience' and any(term in title_lower for term in ['brain', 'neural', 'fmri', 'eeg']):
                    relevant_count += 1
            
            relevance_score = (relevant_count / len(titles)) * 100 if titles else 0
            print(f"📊 Relevância: {relevant_count}/{len(titles)} ({relevance_score:.1f}%)")
            
            if relevance_score >= 50:
                print("✅ Busca relevante")
            else:
                print("⚠️ Busca pouco relevante")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro no teste: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_specific_searches():
    """Testa buscas específicas para verificar se retornam o esperado."""
    print("\n🎯 Testando Buscas Específicas")
    print("=" * 50)
    
    try:
        from gemini_mcp_server import gemini_server
        
        # Testes específicos
        specific_tests = [
            {
                "query": "machine learning",
                "expected_keywords": ["machine", "learning", "ml", "ai", "artificial", "neural"]
            },
            {
                "query": "psychology experiment",
                "expected_keywords": ["psychology", "cognitive", "behavior", "experiment", "mental"]
            },
            {
                "query": "brain imaging",
                "expected_keywords": ["brain", "fmri", "neural", "imaging", "neuroscience"]
            }
        ]
        
        for test in specific_tests:
            query = test["query"]
            expected = test["expected_keywords"]
            
            print(f"\n🔍 Teste: '{query}'")
            print(f"📝 Esperado: {', '.join(expected)}")
            
            result = gemini_server.search_osf_with_gemini(query, 3)
            
            # Verificar se pelo menos uma palavra esperada aparece nos resultados
            found_keywords = []
            for keyword in expected:
                if keyword.lower() in result.lower():
                    found_keywords.append(keyword)
            
            print(f"✅ Encontrado: {', '.join(found_keywords)}")
            
            if found_keywords:
                print("✅ Teste passou - palavras-chave relevantes encontradas")
            else:
                print("❌ Teste falhou - nenhuma palavra-chave relevante encontrada")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro no teste específico: {e}")
        return False

def main():
    """Função principal."""
    print("🚀 TESTE DE RELEVÂNCIA DA BUSCA OSF")
    print("=" * 60)
    
    # Aguardar servidor inicializar
    print("⏳ Aguardando servidor inicializar...")
    time.sleep(3)
    
    # Testar relevância geral
    success1 = test_search_relevance()
    
    # Testar buscas específicas
    success2 = test_specific_searches()
    
    # Resumo
    print("\n" + "="*60)
    print("📊 RESUMO DOS TESTES")
    print("="*60)
    
    print(f"Relevância Geral:       {'✅ PASSOU' if success1 else '❌ FALHOU'}")
    print(f"Buscas Específicas:     {'✅ PASSOU' if success2 else '❌ FALHOU'}")
    
    if success1 and success2:
        print("\n🎉 Busca está funcionando com boa relevância!")
        print("\n💡 Agora teste na interface:")
        print("1. Acesse: http://localhost:7861")
        print("2. Digite: 'Busque projetos sobre machine learning'")
        print("3. Observe que os resultados são relevantes ao termo buscado")
    else:
        print("\n⚠️ Alguns testes de relevância falharam.")
        print("Os resultados podem não estar totalmente relacionados à busca.")
    
    print("\n📋 Comandos para testar na interface:")
    print("- 'Busque projetos sobre machine learning'")
    print("- 'Procure estudos de psicologia'")
    print("- 'Encontre pesquisas sobre neurociência'")
    print("- 'Busque dados sobre mudanças climáticas'")

if __name__ == "__main__":
    main()
