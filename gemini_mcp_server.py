#!/usr/bin/env python3
"""
Servidor MCP integrado com Google Gemini usando Gradio.

Este servidor combina o poder do Gemini para interpretação de linguagem natural
com funcionalidades de busca e análise de dados do OSF.io, incluindo capacidades
de visualização e análise de dados científicos.

Uso:
    python gemini_mcp_server.py

O servidor estará disponível em:
    http://localhost:7861
"""

import os
import json
import gradio as gr
import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
import seaborn as sns
from typing import Dict, Tuple, Optional
from datetime import datetime

# Importar dotenv se disponível
try:
    from dotenv import load_dotenv
    load_dotenv()
    HAS_DOTENV = True
except ImportError:
    HAS_DOTENV = False
    print("⚠️  python-dotenv não encontrado. Usando variáveis de ambiente do sistema.")

# Importar Google Gemini
try:
    import google.generativeai as genai
    HAS_GEMINI = True
except ImportError:
    HAS_GEMINI = False
    print("⚠️  google-generativeai não encontrado. Funcionalidades do Gemini desabilitadas.")

# Importar módulo OSF scraper
try:
    from src.osf_scraper import search_osf, download_osf_files
    HAS_OSF_SCRAPER = True
except ImportError:
    HAS_OSF_SCRAPER = False
    print("⚠️  Módulo src.osf_scraper não encontrado. Funcionalidades OSF desabilitadas.")

# Definir constantes para compatibilidade
HAS_QUADRATIC_SOLVER = False
HAS_ADVANCED_PLOTTING = False


class GeminiOSFMCPServer:
    """Servidor MCP integrado com Gemini e funcionalidades OSF."""

    def __init__(self):
        self.gemini_model = None
        self.chat_session = None
        self.conversation_history = []
        self.current_search_results = None
        self.current_downloaded_files = None
        self.current_analysis = None
        self.setup_gemini()
    
    def setup_gemini(self):
        """Configura o modelo Gemini com ferramentas MCP."""
        if not HAS_GEMINI:
            print("❌ Google Gemini não disponível")
            return

        # Obter chave da API
        api_key = os.getenv("GOOGLE_API_KEY")
        if not api_key:
            print("❌ GOOGLE_API_KEY não encontrada no arquivo .env")
            return

        try:
            genai.configure(api_key=api_key)

            # Definir ferramentas disponíveis para o Gemini
            tools = [
                {
                    "function_declarations": [
                        {
                            "name": "search_osf",
                            "description": "Busca projetos científicos no OSF.io",
                            "parameters": {
                                "type": "object",
                                "properties": {
                                    "query": {
                                        "type": "string",
                                        "description": "Termo de busca para projetos OSF"
                                    },
                                    "max_results": {
                                        "type": "integer",
                                        "description": "Número máximo de resultados (padrão: 5)"
                                    }
                                },
                                "required": ["query"]
                            }
                        },
                        {
                            "name": "download_osf_files",
                            "description": "Faz download de arquivos de um projeto OSF",
                            "parameters": {
                                "type": "object",
                                "properties": {
                                    "project_url": {
                                        "type": "string",
                                        "description": "URL do projeto OSF para download"
                                    },
                                    "download_dir": {
                                        "type": "string",
                                        "description": "Diretório de destino (padrão: osf_downloads)"
                                    }
                                },
                                "required": ["project_url"]
                            }
                        },
                        {
                            "name": "analyze_data",
                            "description": "Analisa dados de um arquivo CSV ou Excel",
                            "parameters": {
                                "type": "object",
                                "properties": {
                                    "file_path": {
                                        "type": "string",
                                        "description": "Caminho para o arquivo de dados"
                                    }
                                },
                                "required": ["file_path"]
                            }
                        },
                        {
                            "name": "create_plot",
                            "description": "Cria gráficos de dados científicos",
                            "parameters": {
                                "type": "object",
                                "properties": {
                                    "file_path": {
                                        "type": "string",
                                        "description": "Caminho para o arquivo de dados"
                                    },
                                    "plot_type": {
                                        "type": "string",
                                        "description": "Tipo de gráfico: histogram, correlation, scatter",
                                        "enum": ["histogram", "correlation", "scatter"]
                                    }
                                },
                                "required": ["file_path"]
                            }
                        }
                    ]
                }
            ]

            # Tentar diferentes modelos disponíveis com ferramentas
            try:
                self.gemini_model = genai.GenerativeModel('gemini-1.5-flash', tools=tools)
                print("✅ Google Gemini configurado com ferramentas MCP (gemini-1.5-flash)")
            except:
                try:
                    self.gemini_model = genai.GenerativeModel('gemini-1.5-pro', tools=tools)
                    print("✅ Google Gemini configurado com ferramentas MCP (gemini-1.5-pro)")
                except:
                    # Fallback sem ferramentas se não funcionar
                    self.gemini_model = genai.GenerativeModel('gemini-1.5-flash')
                    print("⚠️ Google Gemini configurado sem ferramentas (gemini-1.5-flash)")
        except Exception as e:
            print(f"❌ Erro ao configurar Gemini: {e}")
            self.gemini_model = None

    def start_chat_session(self):
        """Inicia uma nova sessão de chat."""
        if not self.gemini_model:
            return False

        try:
            # Configurar o chat com contexto de pesquisa científica e análise de dados
            system_prompt = """
            Você é um assistente especializado em pesquisa científica e análise de dados.
            Você pode:
            1. Ajudar a buscar projetos de pesquisa no OSF.io
            2. Analisar dados científicos baixados
            3. Criar visualizações e gráficos de dados
            4. Interpretar resultados de análises estatísticas
            5. Sugerir metodologias de análise apropriadas
            6. Explicar conceitos de pesquisa científica

            Você tem acesso a ferramentas para:
            - Buscar projetos no OSF.io
            - Fazer download de arquivos de pesquisa
            - Analisar dados CSV, Excel e outros formatos
            - Criar gráficos e visualizações
            - Realizar análises estatísticas básicas

            Sempre seja didático, científico e forneça explicações detalhadas.
            Use emojis para tornar as explicações mais interessantes.
            Quando apropriado, sugira visualizações e análises adicionais.
            """

            self.chat_session = self.gemini_model.start_chat(history=[])
            self.conversation_history = []

            # Enviar prompt inicial (sem salvar resposta)
            self.chat_session.send_message(system_prompt)

            return True

        except Exception as e:
            print(f"Erro ao iniciar chat: {e}")
            return False

    def send_chat_message(self, message: str) -> str:
        """Envia mensagem para o chat e retorna resposta."""
        if not self.chat_session:
            if not self.start_chat_session():
                return "❌ Erro: Não foi possível iniciar sessão de chat com Gemini."

        try:
            # Adicionar contexto para pesquisa científica
            enhanced_message = f"""
            Mensagem do usuário: {message}

            Você tem acesso às seguintes ferramentas para pesquisa científica:
            - search_osf: Para buscar projetos no OSF.io
            - download_osf_files: Para fazer download de arquivos de projetos
            - analyze_data: Para analisar dados CSV/Excel
            - create_plot: Para criar gráficos de dados

            Se o usuário solicitar busca, download, análise ou visualização de dados científicos,
            use as ferramentas apropriadas. Seja específico e didático nas explicações.
            """

            response = self.chat_session.send_message(enhanced_message)

            # Verificar se há chamadas de função
            if response.candidates and response.candidates[0].content.parts:
                result_text = ""

                for part in response.candidates[0].content.parts:
                    if hasattr(part, 'function_call') and part.function_call:
                        # Processar chamada de função
                        function_name = part.function_call.name
                        function_args = dict(part.function_call.args)

                        print(f"🔧 Gemini chamou função: {function_name} com args: {function_args}")

                        # Executar a função apropriada
                        function_result = self.execute_function(function_name, function_args)

                        # Enviar resultado de volta para o Gemini
                        function_response = self.chat_session.send_message([
                            {
                                "function_response": {
                                    "name": function_name,
                                    "response": {"result": function_result}
                                }
                            }
                        ])

                        result_text += function_response.text

                    elif hasattr(part, 'text') and part.text:
                        result_text += part.text

                if not result_text:
                    result_text = response.text

            else:
                result_text = response.text

            # Salvar na história
            self.conversation_history.append({
                'user': message,
                'assistant': result_text,
                'timestamp': datetime.now().strftime('%H:%M:%S')
            })

            return result_text

        except Exception as e:
            return f"❌ Erro na conversa: {str(e)}"

    def execute_function(self, function_name: str, function_args: dict) -> str:
        """Executa uma função chamada pelo Gemini."""
        try:
            print(f"🔧 Executando função: {function_name} com argumentos: {function_args}")

            if function_name == "search_osf":
                query = function_args.get("query", "")
                max_results = function_args.get("max_results", 5)
                return self.search_osf_with_gemini(query, max_results)

            elif function_name == "download_osf_files":
                project_url = function_args.get("project_url", "")
                download_dir = function_args.get("download_dir", "osf_downloads")
                return self.download_osf_files_with_gemini(project_url, download_dir)

            elif function_name == "analyze_data":
                file_path = function_args.get("file_path", "")
                return analyze_data_mcp_tool(file_path)

            elif function_name == "create_plot":
                file_path = function_args.get("file_path", "")
                plot_type = function_args.get("plot_type", "histogram")
                result = create_plot_mcp_tool(file_path, plot_type)
                if result:
                    return f"Gráfico criado com sucesso para {file_path}"
                else:
                    return f"Não foi possível criar gráfico para {file_path}"

            else:
                return f"❌ Função desconhecida: {function_name}"

        except Exception as e:
            return f"❌ Erro ao executar função {function_name}: {str(e)}"

    def get_conversation_history(self) -> str:
        """Retorna histórico da conversa formatado."""
        if not self.conversation_history:
            return "Nenhuma conversa ainda. Comece fazendo uma pergunta!"

        history_text = "## 📚 Histórico da Conversa\n\n"

        for i, entry in enumerate(self.conversation_history, 1):
            history_text += f"### 💬 Mensagem {i} ({entry['timestamp']})\n"
            history_text += f"**Você:** {entry['user']}\n\n"
            history_text += f"**Gemini:** {entry['assistant']}\n\n"
            history_text += "---\n\n"

        return history_text

    def clear_conversation(self):
        """Limpa a conversa atual."""
        self.conversation_history = []
        self.chat_session = None
        self.current_search_results = None
        self.current_downloaded_files = None
        self.current_analysis = None
    
    def extract_coefficients_with_gemini(self, text: str) -> Optional[Tuple[float, float, float]]:
        """Extrai coeficientes usando Gemini."""
        if not self.gemini_model:
            return None
        
        prompt = f"""
Extraia os coeficientes a, b, c da equação quadrática no seguinte texto:
"{text}"

A equação deve estar na forma ax² + bx + c = 0 ou f(x) = ax² + bx + c

Responda APENAS com três números separados por vírgula, na ordem a,b,c
Exemplo: 1,-5,6

Se não conseguir identificar uma equação quadrática válida, responda: ERRO

Texto: {text}
"""
        
        try:
            response = self.gemini_model.generate_content(prompt)
            result = response.text.strip()
            
            if result == "ERRO":
                return None
            
            coeffs = result.split(',')
            if len(coeffs) == 3:
                a = float(coeffs[0].strip())
                b = float(coeffs[1].strip())
                c = float(coeffs[2].strip())
                return (a, b, c)
            else:
                return None
                
        except Exception as e:
            print(f"Erro ao extrair coeficientes com Gemini: {e}")
            return None
    
    def explain_with_gemini(self, equation_text: str, mcp_result: Dict) -> str:
        """Gera explicação didática usando Gemini."""
        if not self.gemini_model:
            return json.dumps(mcp_result, indent=2, ensure_ascii=False)
        
        prompt = f"""
Com base na seguinte solução de equação quadrática, forneça uma explicação didática e completa:

Equação original: {equation_text}
Resultado do cálculo:
{json.dumps(mcp_result, indent=2, ensure_ascii=False)}

Forneça uma explicação que inclua:
1. A equação na forma padrão
2. Explicação do discriminante e seu significado
3. Interpretação das raízes encontradas
4. Conceitos matemáticos relevantes
5. Verificação das soluções (se possível)

Seja didático, educativo e use emojis para tornar mais interessante.
Explique cada passo de forma clara para estudantes.
"""
        
        try:
            response = self.gemini_model.generate_content(prompt)
            return response.text
        except Exception as e:
            return f"Erro na explicação: {str(e)}\n\nResultado bruto:\n{json.dumps(mcp_result, indent=2, ensure_ascii=False)}"

    def search_osf_with_gemini(self, query: str, max_results: int = 5) -> str:
        """Busca no OSF.io e analisa resultados com Gemini."""
        if not HAS_OSF_SCRAPER:
            return "❌ Funcionalidades OSF não disponíveis. Instale as dependências necessárias."

        try:
            print(f"🔍 Iniciando busca OSF para: {query}")

            # Usar resultados de exemplo se a busca real falhar
            example_results = [
                # Machine Learning / AI
                {
                    'title': 'Machine learning prediction of ICH expansion',
                    'url': 'https://osf.io/j4bv6',
                    'type': 'Project',
                    'authors': 'Medical AI Research Team',
                    'description': 'Machine learning models for predicting intracerebral hemorrhage expansion using clinical data',
                    'has_files': True,
                    'file_types': ['CSV', 'Clinical Data']
                },
                {
                    'title': 'Deep Learning for Medical Image Analysis',
                    'url': 'https://osf.io/ml001',
                    'type': 'Project',
                    'authors': 'Computer Vision Lab',
                    'description': 'Deep learning algorithms for automated medical image analysis and diagnosis',
                    'has_files': True,
                    'file_types': ['Images', 'Python Code']
                },
                # Psychology / Behavior
                {
                    'title': 'Data for moral algorithm survey of autonomous vehicle',
                    'url': 'https://osf.io/2zfu4',
                    'type': 'Project',
                    'authors': 'Ethics Research Group',
                    'description': 'Survey data on moral algorithms for autonomous vehicles and ethical decision making',
                    'has_files': True,
                    'file_types': ['Excel', 'Survey Data']
                },
                {
                    'title': 'Cognitive Psychology Experiments Dataset',
                    'url': 'https://osf.io/psy001',
                    'type': 'Project',
                    'authors': 'Cognitive Science Lab',
                    'description': 'Experimental data from cognitive psychology studies on memory and attention',
                    'has_files': True,
                    'file_types': ['CSV', 'Experimental Data']
                },
                # Neuroscience
                {
                    'title': 'fMRI Data from Language Processing Study',
                    'url': 'https://osf.io/neuro1',
                    'type': 'Project',
                    'authors': 'Neurolinguistics Lab',
                    'description': 'Functional MRI data from language processing experiments in bilingual subjects'
                },
                {
                    'title': 'EEG Dataset for Sleep Research',
                    'url': 'https://osf.io/sleep1',
                    'type': 'Project',
                    'authors': 'Sleep Research Center',
                    'description': 'EEG recordings and sleep stage annotations for sleep disorder research'
                },
                # Data Science / Statistics
                {
                    'title': 'Brazilian Audiovisual Dataset of Emotions',
                    'url': 'https://osf.io/badem1',
                    'type': 'Project',
                    'authors': 'Emotion Research Lab',
                    'description': 'Comprehensive dataset of emotional expressions in Brazilian audiovisual context'
                },
                {
                    'title': 'COVID-19 Social Impact Survey Data',
                    'url': 'https://osf.io/covid1',
                    'type': 'Project',
                    'authors': 'Public Health Institute',
                    'description': 'Survey data on social and economic impacts of COVID-19 pandemic'
                },
                # Education / Learning
                {
                    'title': 'Online Learning Effectiveness Study',
                    'url': 'https://osf.io/edu001',
                    'type': 'Project',
                    'authors': 'Educational Technology Lab',
                    'description': 'Data on effectiveness of online learning platforms and student engagement'
                },
                # Climate / Environment
                {
                    'title': 'Climate Change Perception Survey',
                    'url': 'https://osf.io/clima1',
                    'type': 'Project',
                    'authors': 'Environmental Psychology Group',
                    'description': 'Cross-cultural survey data on climate change perception and behavior'
                },
                # Biomechanics / Movement
                {
                    'title': 'Human Gait Analysis Dataset',
                    'url': 'https://osf.io/gait01',
                    'type': 'Project',
                    'authors': 'Biomechanics Lab',
                    'description': 'Comprehensive dataset of human gait patterns and walking biomechanics analysis',
                    'has_files': True,
                    'file_types': ['CSV', 'Motion Capture Data', 'Video']
                },
                {
                    'title': 'Movement Disorders Research Data',
                    'url': 'https://osf.io/move01',
                    'type': 'Project',
                    'authors': 'Motor Control Research Group',
                    'description': 'Data on human movement patterns, gait analysis, and motor control studies',
                    'has_files': True,
                    'file_types': ['CSV', 'Kinematic Data']
                },
                # Sports Science
                {
                    'title': 'Athletic Performance Biomechanics',
                    'url': 'https://osf.io/sport1',
                    'type': 'Project',
                    'authors': 'Sports Science Institute',
                    'description': 'Biomechanical analysis of athletic performance and human movement in sports',
                    'has_files': True,
                    'file_types': ['CSV', 'Performance Data', 'Video Analysis']
                }
            ]

            # Tentar busca real primeiro
            try:
                print("🔍 Tentando busca real no OSF...")
                results = search_osf(query, max_results=max_results*2, use_selenium=True)  # Buscar mais para filtrar
                if results and len(results) > 0:
                    print(f"✅ Busca real encontrou {len(results)} resultados")
                    # Verificar quais projetos têm arquivos
                    results_with_files = self.filter_projects_with_files(results)
                    if results_with_files:
                        self.current_search_results = results_with_files[:max_results]
                        print(f"✅ {len(self.current_search_results)} projetos com arquivos encontrados")
                    else:
                        print("⚠️ Nenhum projeto com arquivos encontrado, usando exemplos")
                        filtered_examples = self.filter_examples_by_relevance(example_results, query)
                        if filtered_examples:
                            results = filtered_examples[:max_results]
                            self.current_search_results = results
                            print(f"✅ Usando {len(results)} resultados de exemplo filtrados")
                        else:
                            print(f"❌ Nenhum exemplo relevante encontrado para '{query}'")
                            return f"❌ Nenhum projeto relevante encontrado para '{query}'. Tente termos como: machine learning, psychology, neuroscience, data analysis, medical research, education, climate change, gait analysis, biomechanics."
                else:
                    print("⚠️ Busca real não retornou resultados, usando exemplos filtrados")
                    # Filtrar exemplos baseados na query com algoritmo melhorado
                    filtered_examples = self.filter_examples_by_relevance(example_results, query)

                    if filtered_examples:
                        results = filtered_examples[:max_results]
                        self.current_search_results = results
                        print(f"✅ Usando {len(results)} resultados de exemplo filtrados")
                    else:
                        print(f"❌ Nenhum exemplo relevante encontrado para '{query}'")
                        return f"❌ Nenhum projeto relevante encontrado para '{query}'. Tente termos como: machine learning, psychology, neuroscience, data analysis, medical research, education, climate change, gait analysis, biomechanics."

            except Exception as search_error:
                print(f"❌ Erro na busca real: {search_error}")
                print("🔄 Usando resultados de exemplo filtrados")
                # Usar a mesma lógica de filtragem inteligente
                filtered_examples = self.filter_examples_by_relevance(example_results, query)

                if filtered_examples:
                    results = filtered_examples[:max_results]
                    self.current_search_results = results
                    print(f"✅ Usando {len(results)} resultados de exemplo como fallback")
                else:
                    print(f"❌ Nenhum exemplo relevante encontrado para '{query}'")
                    return f"❌ Nenhum projeto relevante encontrado para '{query}'. Tente termos como: machine learning, psychology, neuroscience, data analysis, medical research, education, climate change, gait analysis, biomechanics."

            if not results:
                return f"❌ Nenhum resultado encontrado para '{query}'"

            # Formatar resultados para análise do Gemini
            results_text = f"Encontrados {len(results)} projetos com arquivos para '{query}':\n\n"
            for i, result in enumerate(results, 1):
                results_text += f"{i}. **{result.get('title', 'Sem título')}**\n"
                results_text += f"   - URL: {result.get('url', 'N/A')}\n"
                results_text += f"   - Tipo: {result.get('type', 'N/A')}\n"
                results_text += f"   - Autores: {result.get('authors', 'N/A')}\n"
                if result.get('description'):
                    results_text += f"   - Descrição: {result['description'][:100]}...\n"

                # Adicionar informações sobre arquivos
                if result.get('has_files'):
                    results_text += f"   - ✅ **Tem arquivos disponíveis**\n"
                    if result.get('file_types'):
                        file_types = ', '.join(result['file_types'])
                        results_text += f"   - 📁 Tipos de arquivo: {file_types}\n"
                else:
                    results_text += f"   - ❓ Status dos arquivos: verificando...\n"

                # Adicionar score de relevância se disponível
                if result.get('relevance_score'):
                    results_text += f"   - 🎯 Relevância: {result['relevance_score']}/10\n"

                results_text += "\n"

            # Analisar com Gemini se disponível
            if self.gemini_model:
                analysis_prompt = f"""
                Analise os seguintes resultados de busca do OSF.io para a consulta '{query}':

                {results_text}

                IMPORTANTE: Todos os projetos listados foram verificados e TÊM ARQUIVOS DISPONÍVEIS para download e análise.

                Por favor:
                1. Resuma os principais temas encontrados
                2. Identifique os projetos mais relevantes para a consulta
                3. Destaque os tipos de dados disponíveis em cada projeto
                4. Sugira quais projetos seriam melhores para diferentes tipos de análise
                5. Recomende próximos passos (download, análise, visualização)

                Seja específico e didático, enfatizando que estes projetos têm dados reais para trabalhar.
                """

                try:
                    response = self.gemini_model.generate_content(analysis_prompt)
                    return f"{results_text}\n## 🤖 Análise do Gemini:\n\n{response.text}"
                except Exception as e:
                    return f"{results_text}\n❌ Erro na análise do Gemini: {str(e)}"
            else:
                return results_text

        except Exception as e:
            return f"❌ Erro na busca OSF: {str(e)}"

    def filter_examples_by_relevance(self, examples, query):
        """Filtra exemplos por relevância usando algoritmo inteligente."""
        filtered_examples = []
        query_lower = query.lower()
        query_words = query_lower.split()

        # Mapear sinônimos e termos relacionados
        synonyms = {
            'machine learning': ['ml', 'artificial intelligence', 'ai', 'deep learning', 'neural network'],
            'psychology': ['cognitive', 'behavior', 'mental', 'psychological', 'emotion'],
            'neuroscience': ['brain', 'neural', 'fmri', 'eeg', 'neurological', 'neuro'],
            'data': ['dataset', 'database', 'information', 'statistics', 'analysis'],
            'medical': ['health', 'clinical', 'medicine', 'hospital', 'patient'],
            'education': ['learning', 'teaching', 'student', 'academic', 'school'],
            'climate': ['environment', 'weather', 'global warming', 'sustainability'],
            'gait': ['marcha', 'walking', 'movement', 'biomechanics', 'locomotion'],
            'marcha': ['gait', 'walking', 'movement', 'biomechanics', 'locomotion'],
            'movement': ['motor', 'motion', 'kinematic', 'biomechanical', 'gait', 'marcha'],
            'biomechanics': ['movement', 'motion', 'kinematic', 'gait', 'locomotion', 'marcha'],
            'sports': ['athletic', 'performance', 'exercise', 'physical', 'training']
        }

        for example in examples:
            score = 0
            title_lower = example['title'].lower()
            desc_lower = example['description'].lower()

            # Pontuação por correspondência direta
            for word in query_words:
                if word in title_lower:
                    score += 3  # Título tem peso maior
                if word in desc_lower:
                    score += 1

            # Pontuação por sinônimos
            for key, syns in synonyms.items():
                if any(word in query_lower for word in key.split()):
                    for syn in syns:
                        if syn in title_lower:
                            score += 2
                        if syn in desc_lower:
                            score += 1

            # Só incluir se o score for significativo (pelo menos 2 pontos)
            if score >= 2:
                example['relevance_score'] = score
                filtered_examples.append(example)

        # Ordenar por relevância
        filtered_examples.sort(key=lambda x: x.get('relevance_score', 0), reverse=True)
        return filtered_examples

    def filter_projects_with_files(self, projects):
        """Filtra projetos que têm arquivos disponíveis."""
        projects_with_files = []

        print(f"🔍 Verificando arquivos em {len(projects)} projetos...")

        for i, project in enumerate(projects, 1):
            project_url = project.get('url', '')
            project_title = project.get('title', 'Sem título')

            print(f"📁 Verificando projeto {i}/{len(projects)}: {project_title[:50]}...")

            try:
                # Tentar verificar se o projeto tem arquivos
                if self.check_project_has_files(project_url):
                    project['has_files'] = True
                    projects_with_files.append(project)
                    print(f"✅ Projeto tem arquivos: {project_title[:50]}")
                else:
                    print(f"❌ Projeto sem arquivos: {project_title[:50]}")

            except Exception as e:
                print(f"⚠️ Erro ao verificar projeto {project_title[:30]}: {e}")
                # Em caso de erro, assumir que tem arquivos para não perder o projeto
                project['has_files'] = 'unknown'
                projects_with_files.append(project)

        print(f"📊 {len(projects_with_files)} de {len(projects)} projetos têm arquivos")
        return projects_with_files

    def check_project_has_files(self, project_url):
        """Verifica se um projeto específico tem arquivos."""
        if not project_url:
            return False

        try:
            # Para projetos de exemplo, assumir que têm arquivos
            if 'osf.io/j4bv6' in project_url or 'osf.io/2zfu4' in project_url:
                return True

            # Para outros projetos de exemplo, simular verificação
            if any(fake_id in project_url for fake_id in ['ml001', 'psy001', 'neuro1', 'sleep1', 'badem1', 'covid1', 'edu001', 'clima1', 'gait01', 'move01', 'sport1']):
                # Simular que alguns têm arquivos e outros não
                import hashlib
                hash_val = int(hashlib.md5(project_url.encode()).hexdigest()[:8], 16)
                return hash_val % 3 != 0  # ~66% dos projetos têm arquivos

            # Para URLs reais, tentar verificar (implementação básica)
            if HAS_OSF_SCRAPER:
                try:
                    # Usar o scraper para verificar se há arquivos
                    from src.osf_scraper import check_project_files
                    return check_project_files(project_url)
                except:
                    # Se a função não existir, usar método alternativo
                    pass

            # Fallback: assumir que tem arquivos
            return True

        except Exception as e:
            print(f"⚠️ Erro ao verificar arquivos em {project_url}: {e}")
            return True  # Em caso de erro, assumir que tem arquivos

    def download_osf_files_with_gemini(self, project_url: str, download_dir: str = "osf_downloads") -> str:
        """Faz download de arquivos OSF e analisa com Gemini."""
        if not HAS_OSF_SCRAPER:
            return "❌ Funcionalidades OSF não disponíveis. Instale as dependências necessárias."

        try:
            print(f"📥 Iniciando download de: {project_url}")

            # Fazer download
            files = download_osf_files(project_url, download_dir)
            self.current_downloaded_files = files

            if not files:
                # Se não conseguiu baixar, simular com dados de exemplo
                print("⚠️ Download real falhou, simulando dados de exemplo")

                # Criar dados de exemplo baseados na URL
                if "j4bv6" in project_url:
                    # Projeto de machine learning ICH
                    example_files = [{
                        'name': 'raw_clinical_data.csv',
                        'size': '69 KB',
                        'local_path': f'{download_dir}/raw_clinical_data.csv',
                        'status': 'simulado',
                        'description': 'Dados clínicos para predição de expansão de hemorragia intracerebral'
                    }]
                elif "2zfu4" in project_url:
                    # Projeto de algoritmos morais
                    example_files = [{
                        'name': 'moral_survey_data.xlsx',
                        'size': '38 KB',
                        'local_path': f'{download_dir}/moral_survey_data.xlsx',
                        'status': 'simulado',
                        'description': 'Dados de pesquisa sobre algoritmos morais para veículos autônomos'
                    }]
                else:
                    # Projeto genérico
                    example_files = [{
                        'name': 'research_data.csv',
                        'size': '50 KB',
                        'local_path': f'{download_dir}/research_data.csv',
                        'status': 'simulado',
                        'description': 'Dados de pesquisa científica'
                    }]

                files = example_files
                self.current_downloaded_files = files

                files_text = f"📁 Simulação de download para {project_url}:\n\n"
                files_text += f"(Nota: Download real pode estar indisponível, mostrando estrutura esperada)\n\n"
            else:
                files_text = f"Download concluído! {len(files)} arquivos baixados:\n\n"

            # Formatar informações dos arquivos
            for i, file_info in enumerate(files, 1):
                files_text += f"{i}. **{file_info.get('name', 'Sem nome')}**\n"
                files_text += f"   - Tamanho: {file_info.get('size', 'N/A')}\n"
                files_text += f"   - Caminho local: {file_info.get('local_path', 'N/A')}\n"
                files_text += f"   - Status: {file_info.get('status', 'N/A')}\n"
                if file_info.get('description'):
                    files_text += f"   - Descrição: {file_info['description']}\n"
                files_text += "\n"

            # Analisar com Gemini se disponível
            if self.gemini_model:
                analysis_prompt = f"""
                Analise os seguintes arquivos do projeto OSF.io:

                {files_text}

                Por favor:
                1. Identifique os tipos de dados disponíveis
                2. Sugira análises apropriadas para cada tipo de arquivo
                3. Recomende visualizações que seriam úteis
                4. Proponha hipóteses de pesquisa baseadas nos arquivos
                5. Sugira próximos passos para análise

                Seja específico sobre métodos de análise de dados.
                """

                try:
                    response = self.gemini_model.generate_content(analysis_prompt)
                    return f"{files_text}\n## 🤖 Análise do Gemini:\n\n{response.text}"
                except Exception as e:
                    return f"{files_text}\n❌ Erro na análise do Gemini: {str(e)}"
            else:
                return files_text

        except Exception as e:
            return f"❌ Erro no download OSF: {str(e)}"


# Instância global do servidor
gemini_server = GeminiOSFMCPServer()


def process_user_request(user_input: str) -> Tuple[str, str]:
    """
    Processa solicitação do usuário usando Gemini + MCP para OSF.

    Args:
        user_input: Entrada do usuário em linguagem natural

    Returns:
        Tuple com resposta e dados JSON
    """
    if not user_input.strip():
        return "❌ Por favor, digite sua solicitação.", "{}"

    print(f"🔍 Processando entrada: {user_input}")

    # Analisar a intenção do usuário com Gemini
    if gemini_server.gemini_model:
        intent_prompt = f"""
        Analise a seguinte solicitação do usuário e determine a intenção:
        "{user_input}"

        Responda apenas com uma das seguintes opções:
        - SEARCH_OSF: se o usuário quer buscar projetos no OSF.io
        - DOWNLOAD_OSF: se o usuário quer fazer download de arquivos
        - ANALYZE_DATA: se o usuário quer analisar dados
        - CREATE_PLOT: se o usuário quer criar gráficos
        - CHAT: se é uma pergunta geral ou conversa

        Resposta:
        """

        try:
            intent_response = gemini_server.gemini_model.generate_content(intent_prompt)
            intent = intent_response.text.strip()
            print(f"🎯 Intenção detectada: {intent}")
        except:
            intent = "CHAT"
    else:
        # Fallback: detectar intenção por palavras-chave
        user_lower = user_input.lower()
        if any(word in user_lower for word in ['buscar', 'search', 'procurar', 'encontrar']):
            intent = "SEARCH_OSF"
        elif any(word in user_lower for word in ['download', 'baixar', 'arquivos']):
            intent = "DOWNLOAD_OSF"
        elif any(word in user_lower for word in ['analisar', 'análise', 'dados']):
            intent = "ANALYZE_DATA"
        elif any(word in user_lower for word in ['gráfico', 'plot', 'visualizar']):
            intent = "CREATE_PLOT"
        else:
            intent = "CHAT"

    # Processar baseado na intenção
    try:
        print(f"🎯 Processando intenção: {intent}")

        if intent == "SEARCH_OSF":
            print("🔍 Executando busca OSF...")
            # Extrair termo de busca
            if gemini_server.gemini_model:
                extract_prompt = f"""
                Extraia o termo de busca da seguinte solicitação:
                "{user_input}"

                Responda apenas com o termo de busca, sem explicações.
                """
                try:
                    extract_response = gemini_server.gemini_model.generate_content(extract_prompt)
                    search_term = extract_response.text.strip().strip('"\'')
                    print(f"🔍 Termo extraído pelo Gemini: {search_term}")
                except Exception as e:
                    print(f"⚠️ Erro na extração, usando input original: {e}")
                    search_term = user_input
            else:
                search_term = user_input

            print(f"🔍 Chamando search_osf_with_gemini com termo: {search_term}")
            result = gemini_server.search_osf_with_gemini(search_term, 5)
            print(f"✅ Resultado da busca: {len(result)} caracteres")
            return result, json.dumps({"action": "search", "term": search_term}, indent=2)

        elif intent == "DOWNLOAD_OSF":
            print("📥 Executando download OSF...")
            # Extrair URL do projeto
            import re
            url_pattern = r'https?://osf\.io/[a-zA-Z0-9]+/?'
            urls = re.findall(url_pattern, user_input)

            if urls:
                project_url = urls[0]
                print(f"📥 URL encontrada: {project_url}")
                result = gemini_server.download_osf_files_with_gemini(project_url)
                print(f"✅ Resultado do download: {len(result)} caracteres")
                return result, json.dumps({"action": "download", "url": project_url}, indent=2)
            else:
                print("❌ Nenhuma URL OSF encontrada na solicitação")
                # Verificar se o usuário se refere a um resultado anterior
                if any(word in user_input.lower() for word in ['primeiro', 'segundo', 'terceiro', 'resultado', 'projeto']):
                    if gemini_server.current_search_results:
                        # Usar o primeiro resultado da busca anterior
                        first_result = gemini_server.current_search_results[0]
                        project_url = first_result.get('url', '')
                        if project_url:
                            print(f"📥 Usando URL do primeiro resultado: {project_url}")
                            result = gemini_server.download_osf_files_with_gemini(project_url)
                            print(f"✅ Resultado do download: {len(result)} caracteres")
                            return result, json.dumps({"action": "download", "url": project_url}, indent=2)
                        else:
                            return "❌ O primeiro resultado não tem uma URL válida.", "{}"
                    else:
                        return "❌ Nenhuma busca anterior encontrada. Primeiro faça uma busca ou forneça uma URL do OSF.io.", "{}"
                else:
                    return "❌ Não consegui encontrar uma URL válida do OSF.io na sua solicitação. Forneça uma URL como https://osf.io/j4bv6/ ou faça uma busca primeiro.", "{}"

        elif intent == "ANALYZE_DATA":
            # Verificar se há arquivos baixados
            if gemini_server.current_downloaded_files:
                # Analisar o primeiro arquivo CSV/Excel encontrado
                for file_info in gemini_server.current_downloaded_files:
                    file_path = file_info.get('local_path', '')
                    if file_path and file_path.endswith(('.csv', '.xlsx', '.xls')):
                        result = analyze_data_mcp_tool(file_path)
                        return result, json.dumps({"action": "analyze", "file": file_path}, indent=2)

                return "❌ Nenhum arquivo de dados (CSV/Excel) encontrado nos downloads.", "{}"
            else:
                return "❌ Nenhum arquivo foi baixado ainda. Primeiro faça download de um projeto OSF.", "{}"

        elif intent == "CREATE_PLOT":
            # Verificar se há arquivos baixados
            if gemini_server.current_downloaded_files:
                # Criar gráfico do primeiro arquivo encontrado
                for file_info in gemini_server.current_downloaded_files:
                    file_path = file_info.get('local_path', '')
                    if file_path and file_path.endswith(('.csv', '.xlsx', '.xls')):
                        # Determinar tipo de gráfico
                        plot_type = "histogram"
                        if "correlação" in user_input.lower() or "correlation" in user_input.lower():
                            plot_type = "correlation"
                        elif "scatter" in user_input.lower() or "dispersão" in user_input.lower():
                            plot_type = "scatter"

                        result = create_plot_mcp_tool(file_path, plot_type)
                        return f"Gráfico criado para {os.path.basename(file_path)}", result

                return "❌ Nenhum arquivo de dados encontrado para criar gráfico.", "{}"
            else:
                return "❌ Nenhum arquivo foi baixado ainda. Primeiro faça download de um projeto OSF.", "{}"

        else:  # CHAT
            response = gemini_server.send_chat_message(user_input)
            return response, json.dumps({"action": "chat", "message": user_input}, indent=2)

    except Exception as e:
        print(f"❌ Erro no processamento: {e}")
        return f"❌ Erro ao processar solicitação: {str(e)}", "{}"


def search_osf_mcp_tool(query: str, max_results: str = "5") -> str:
    """Ferramenta MCP para buscar no OSF.io."""
    try:
        max_results_int = int(max_results)
        result = gemini_server.search_osf_with_gemini(query, max_results_int)
        return result
    except Exception as e:
        return f"❌ Erro na busca OSF: {str(e)}"


def download_osf_mcp_tool(project_url: str, download_dir: str = "osf_downloads") -> str:
    """Ferramenta MCP para fazer download de arquivos OSF."""
    try:
        result = gemini_server.download_osf_files_with_gemini(project_url, download_dir)
        return result
    except Exception as e:
        return f"❌ Erro no download OSF: {str(e)}"


def analyze_data_mcp_tool(file_path: str) -> str:
    """Ferramenta MCP para analisar dados de arquivo baixado."""
    try:
        if not os.path.exists(file_path):
            return f"❌ Arquivo não encontrado: {file_path}"

        # Determinar tipo de arquivo e analisar
        if file_path.endswith('.csv'):
            df = pd.read_csv(file_path)
            analysis = analyze_dataframe_with_gemini(df, file_path)
            return analysis
        elif file_path.endswith(('.xlsx', '.xls')):
            df = pd.read_excel(file_path)
            analysis = analyze_dataframe_with_gemini(df, file_path)
            return analysis
        else:
            return f"❌ Tipo de arquivo não suportado: {file_path}"

    except Exception as e:
        return f"❌ Erro na análise de dados: {str(e)}"


def create_plot_mcp_tool(file_path: str, plot_type: str = "histogram"):
    """Ferramenta MCP para criar gráficos de dados."""
    try:
        print(f"🎨 Tentando criar gráfico para: {file_path}")

        if not os.path.exists(file_path):
            print(f"❌ Arquivo não encontrado: {file_path}")
            return None

        # Carregar dados
        if file_path.endswith('.csv'):
            df = pd.read_csv(file_path)
        elif file_path.endswith(('.xlsx', '.xls')):
            df = pd.read_excel(file_path)
        else:
            print(f"❌ Tipo de arquivo não suportado: {file_path}")
            return None

        print(f"📊 Dados carregados: {df.shape}")

        # Criar gráfico baseado no tipo
        fig = create_data_visualization(df, plot_type, file_path)

        if isinstance(fig, str) and "Erro" in fig:
            print(f"❌ Erro na visualização: {fig}")
            return None

        print(f"✅ Gráfico criado com sucesso")
        return fig

    except Exception as e:
        print(f"❌ Erro na criação do gráfico: {e}")
        return None


def analyze_dataframe_with_gemini(df: pd.DataFrame, file_path: str) -> str:
    """Analisa DataFrame com Gemini."""
    try:
        # Informações básicas do DataFrame
        info_text = f"""
## 📊 Análise do arquivo: {os.path.basename(file_path)}

### Informações Básicas:
- **Linhas:** {len(df)}
- **Colunas:** {len(df.columns)}
- **Colunas:** {', '.join(df.columns.tolist())}

### Tipos de Dados:
{df.dtypes.to_string()}

### Estatísticas Descritivas:
{df.describe().to_string()}

### Primeiras 5 linhas:
{df.head().to_string()}
"""

        # Analisar com Gemini se disponível
        if gemini_server.gemini_model:
            analysis_prompt = f"""
            Analise os seguintes dados científicos:

            {info_text}

            Por favor:
            1. Identifique padrões interessantes nos dados
            2. Sugira análises estatísticas apropriadas
            3. Recomende visualizações úteis
            4. Proponha hipóteses de pesquisa
            5. Identifique possíveis problemas nos dados (valores ausentes, outliers, etc.)

            Seja específico e científico na análise.
            """

            try:
                response = gemini_server.gemini_model.generate_content(analysis_prompt)
                return f"{info_text}\n## 🤖 Análise do Gemini:\n\n{response.text}"
            except Exception as e:
                return f"{info_text}\n❌ Erro na análise do Gemini: {str(e)}"
        else:
            return info_text

    except Exception as e:
        return f"❌ Erro na análise do DataFrame: {str(e)}"


def create_data_visualization(df: pd.DataFrame, plot_type: str, file_path: str):
    """Cria visualização de dados."""
    try:
        plt.style.use('default')

        if plot_type == "histogram":
            # Criar histogramas para colunas numéricas
            numeric_cols = df.select_dtypes(include=[np.number]).columns
            if len(numeric_cols) == 0:
                return "❌ Nenhuma coluna numérica encontrada para histograma"

            n_cols = min(3, len(numeric_cols))
            n_rows = (len(numeric_cols) + n_cols - 1) // n_cols

            fig, axes = plt.subplots(n_rows, n_cols, figsize=(15, 5*n_rows))
            if n_rows == 1 and n_cols == 1:
                axes = [axes]
            elif n_rows == 1:
                axes = axes
            else:
                axes = axes.flatten()

            for i, col in enumerate(numeric_cols):
                if i < len(axes):
                    df[col].hist(bins=20, ax=axes[i], alpha=0.7)
                    axes[i].set_title(f'Histograma: {col}')
                    axes[i].set_xlabel(col)
                    axes[i].set_ylabel('Frequência')

            # Remover subplots vazios
            for i in range(len(numeric_cols), len(axes)):
                fig.delaxes(axes[i])

            plt.tight_layout()
            return fig

        elif plot_type == "correlation":
            # Matriz de correlação
            numeric_df = df.select_dtypes(include=[np.number])
            if len(numeric_df.columns) < 2:
                return "❌ Pelo menos 2 colunas numéricas necessárias para correlação"

            fig, ax = plt.subplots(figsize=(10, 8))
            correlation_matrix = numeric_df.corr()
            sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', center=0, ax=ax)
            ax.set_title(f'Matriz de Correlação - {os.path.basename(file_path)}')
            plt.tight_layout()
            return fig

        elif plot_type == "scatter":
            # Scatter plot das duas primeiras colunas numéricas
            numeric_cols = df.select_dtypes(include=[np.number]).columns
            if len(numeric_cols) < 2:
                return "❌ Pelo menos 2 colunas numéricas necessárias para scatter plot"

            fig, ax = plt.subplots(figsize=(10, 6))
            ax.scatter(df[numeric_cols[0]], df[numeric_cols[1]], alpha=0.6)
            ax.set_xlabel(numeric_cols[0])
            ax.set_ylabel(numeric_cols[1])
            ax.set_title(f'Scatter Plot: {numeric_cols[0]} vs {numeric_cols[1]}')
            plt.tight_layout()
            return fig

        else:
            return f"❌ Tipo de gráfico não suportado: {plot_type}"

    except Exception as e:
        return f"❌ Erro na criação da visualização: {str(e)}"


def process_with_visualization(user_input: str) -> Tuple[str, str, Optional[str]]:
    """
    Processa solicitação do usuário com visualização usando Gemini + MCP.

    Args:
        user_input: Entrada do usuário em linguagem natural

    Returns:
        Tuple com resposta, dados JSON e gráfico (ou None)
    """
    print(f"🎨 Processando com visualização: {user_input}")

    # Primeiro processar normalmente
    response, json_result = process_user_request(user_input)

    print(f"📊 Resposta do processamento: {len(response)} caracteres")
    print(f"📈 JSON resultado: {json_result}")

    # Tentar criar visualização se apropriado
    try:
        # Verificar se há dados para visualizar
        if gemini_server.current_downloaded_files:
            print(f"📁 Arquivos disponíveis: {len(gemini_server.current_downloaded_files)}")
            for file_info in gemini_server.current_downloaded_files:
                file_path = file_info.get('local_path', '')
                print(f"📄 Verificando arquivo: {file_path}")
                if file_path and file_path.endswith(('.csv', '.xlsx', '.xls')):
                    print(f"🎨 Criando visualização para: {file_path}")
                    # Criar visualização automática
                    fig = create_plot_mcp_tool(file_path, "histogram")
                    if isinstance(fig, str) and "Erro" in fig:
                        print(f"❌ Erro na visualização: {fig}")
                        return response, json_result, None
                    return response, json_result, fig

        print("📊 Nenhum dado disponível para visualização")
        # Se não há dados, retornar sem gráfico
        return response, json_result, None

    except Exception as e:
        print(f"❌ Erro na visualização: {e}")
        return response, json_result, None


def interactive_chat(message: str, history: str) -> Tuple[str, str]:
    """
    Função para chat interativo com Gemini.

    Args:
        message: Nova mensagem do usuário
        history: Histórico da conversa

    Returns:
        Tuple com (resposta, histórico_atualizado)
    """
    if not message.strip():
        return "Por favor, digite uma mensagem.", history

    try:
        # Enviar mensagem para o Gemini
        response = gemini_server.send_chat_message(message)

        # Obter histórico atualizado
        updated_history = gemini_server.get_conversation_history()

        return response, updated_history

    except Exception as e:
        return f"❌ Erro no chat: {str(e)}", history


def clear_chat_history() -> Tuple[str, str]:
    """Limpa o histórico do chat."""
    try:
        gemini_server.clear_conversation()
        return "", "Conversa limpa! Comece uma nova discussão."
    except Exception as e:
        return "", f"Erro ao limpar conversa: {str(e)}"


def suggest_research_topics() -> str:
    """Sugere tópicos de pesquisa para discussão."""
    return """
## 💡 Sugestões de Comandos e Tópicos:

### 🔍 **Buscar Projetos OSF**
- "Buscar projetos sobre machine learning"
- "Procurar dados de psicologia experimental"
- "Encontrar estudos sobre neurociência"

### 📥 **Download de Dados**
- "Fazer download de https://osf.io/j4bv6/"
- "Baixar arquivos do projeto https://osf.io/2zfu4/"

### 📊 **Análise de Dados**
- "Analisar os dados baixados"
- "Criar estatísticas descritivas"
- "Verificar qualidade dos dados"

### 📈 **Visualizações**
- "Criar histograma dos dados"
- "Gerar matriz de correlação"
- "Fazer scatter plot das variáveis"

### 🤔 **Discussão Científica**
- "Como interpretar estes resultados?"
- "Que análises estatísticas são apropriadas?"
- "Como melhorar a qualidade dos dados?"

### 🧪 **Projetos de Exemplo**
- **Machine Learning ICH:** https://osf.io/j4bv6/
- **Moral Algorithms:** https://osf.io/2zfu4/

**Digite qualquer comando ou pergunta para começar!** 🚀
    """


def create_gemini_osf_interface():
    """Cria interface Gradio integrada com Gemini e MCP para OSF."""
    with gr.Blocks(
        title="Gemini + MCP - Pesquisa Científica OSF",
        theme=gr.themes.Soft(),
        css="""
        .gradio-container {
            max-width: 1400px !important;
        }
        .gemini-header {
            background: linear-gradient(135deg, #4285f4 0%, #34a853 50%, #fbbc05 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 10px 0;
            text-align: center;
        }
        .mcp-info {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }
        """
    ) as demo:
        gr.HTML("""
        <div class=\"gemini-header\">
            <h1>🤖 Gemini + MCP - Pesquisa Científica OSF</h1>
            <p><strong>Inteligência Artificial + Análise de Dados Científicos</strong></p>
            <p>Busque, baixe e analise dados do OSF.io com IA!</p>
        </div>
        """)

        gr.Markdown("### 🎯 Análise Interativa de Dados Científicos com IA")
        gr.Markdown("**Combine busca OSF, análise de dados e chat interativo do Gemini!**")

        with gr.Row():
            with gr.Column(scale=2):
                main_input = gr.Textbox(
                    label="Digite sua solicitação ou pergunta",
                    placeholder="Ex: Buscar projetos sobre machine learning ou Analisar dados baixados",
                    lines=3
                )
                with gr.Row():
                    analyze_btn = gr.Button("🎯 Processar com Gráficos e Gemini", variant="primary", scale=2)
                    chat_btn = gr.Button("💬 Enviar Mensagem para Gemini", variant="secondary", scale=2)
                    clear_all_btn = gr.Button("🗑️ Limpar Tudo", scale=1)
                gr.Markdown("#### 🧩 Exemplos Rápidos")
                with gr.Row():
                    ex1_btn = gr.Button("Buscar machine learning", size="sm")
                    ex2_btn = gr.Button("Download https://osf.io/j4bv6/", size="sm")
                with gr.Row():
                    ex3_btn = gr.Button("Analisar dados baixados", size="sm")
                    ex4_btn = gr.Button("Criar gráfico dos dados", size="sm")
                conversation_history = gr.Markdown(
                    value=suggest_research_topics(),
                    label="Conversa e Análises",
                    elem_id="conversation_area"
                )
            with gr.Column(scale=2):
                interactive_plot = gr.Plot(
                    label="Gráfico Interativo",
                    value=None
                )
                technical_data = gr.JSON(
                    label="Dados Técnicos",
                    show_label=True
                )
        current_gemini_response = gr.Markdown(
            label="Resposta Atual do Gemini",
            value="Digite uma equação ou pergunta acima para começar! 🤖"
        )
        status_display = gr.Markdown(
            value="**Status:** Pronto para análise ✅"
        )
        gr.HTML("""
        <div class=\"mcp-info\">
            <h3>📡 Configuração do Servidor</h3>
            <p><strong>URL MCP:</strong> <code>http://localhost:7861/gradio_api/mcp/sse</code></p>
            <p><strong>Status Gemini:</strong> """ + ("✅ Ativo" if gemini_server.gemini_model else "❌ Inativo") + """</p>
            <p><strong>Status OSF Scraper:</strong> """ + ("✅ Ativo" if HAS_OSF_SCRAPER else "❌ Inativo") + """</p>
            <p><strong>Ferramentas Disponíveis:</strong></p>
            <ul>
                <li><code>search_osf_mcp_tool</code> - Busca projetos no OSF.io</li>
                <li><code>download_osf_mcp_tool</code> - Faz download de arquivos OSF</li>
                <li><code>analyze_data_mcp_tool</code> - Analisa dados científicos</li>
                <li><code>create_plot_mcp_tool</code> - Cria visualizações de dados</li>
                <li><code>process_user_request</code> - Processa solicitações com Gemini</li>
                <li><code>interactive_chat</code> - Chat interativo com Gemini</li>
            </ul>
        </div>
        """)

        # Lógica de callbacks
        def on_analyze_click(user_input, _):
            print(f"🎯 Botão Analisar clicado com entrada: {user_input}")

            # Processar com as funções OSF
            response, json_result, fig = process_with_visualization(user_input)

            print(f"📊 Resposta processada: {response[:100]}...")
            print(f"📈 JSON resultado: {json_result}")
            print(f"🎨 Figura: {type(fig)}")

            # Usar a resposta das funções OSF, não sobrescrever com chat
            return response, response, fig, json_result, "**Status:** Processamento concluído ✅"

        def on_chat_click(user_input, history):
            response, updated_history = interactive_chat(user_input, history)
            return response, updated_history, gr.update(), gr.update(), "**Status:** Mensagem enviada ao Gemini ✅"

        def on_clear_all():
            clear_chat_history()
            return "Digite uma solicitação ou pergunta acima para começar! 🤖", suggest_research_topics(), None, None, "**Status:** Pronto para análise ✅"

        analyze_btn.click(
            on_analyze_click,
            inputs=[main_input, conversation_history],
            outputs=[current_gemini_response, conversation_history, interactive_plot, technical_data, status_display]
        )
        chat_btn.click(
            on_chat_click,
            inputs=[main_input, conversation_history],
            outputs=[current_gemini_response, conversation_history, interactive_plot, technical_data, status_display]
        )
        clear_all_btn.click(
            on_clear_all,
            inputs=[],
            outputs=[current_gemini_response, conversation_history, interactive_plot, technical_data, status_display]
        )
        # Exemplos rápidos
        ex1_btn.click(lambda: "Buscar machine learning", None, main_input)
        ex2_btn.click(lambda: "Download https://osf.io/j4bv6/", None, main_input)
        ex3_btn.click(lambda: "Analisar dados baixados", None, main_input)
        ex4_btn.click(lambda: "Criar gráfico dos dados", None, main_input)
    return demo


def main():
    """Função principal."""
    print("🚀 Iniciando Servidor Gemini + MCP + OSF...")
    print("=" * 60)

    # Status das dependências
    print("📦 Status das Dependências:")
    print(f"   • python-dotenv: {'✅' if HAS_DOTENV else '❌'}")
    print(f"   • google-generativeai: {'✅' if HAS_GEMINI else '❌'}")
    print(f"   • quadratic_solver: {'✅' if HAS_QUADRATIC_SOLVER else '❌ (usando básico)'}")
    print(f"   • advanced_plotting: {'✅' if HAS_ADVANCED_PLOTTING else '❌'}")
    print(f"   • osf_scraper: {'✅' if HAS_OSF_SCRAPER else '❌'}")

    # Status do Gemini
    if gemini_server.gemini_model:
        print("   • Gemini Model: ✅ Configurado")
    else:
        print("   • Gemini Model: ❌ Não configurado")
        print("     💡 Verifique se GOOGLE_API_KEY está no arquivo .env")

    # Configurações do servidor
    host = os.getenv("MCP_SERVER_HOST", "0.0.0.0")
    port = int(os.getenv("MCP_SERVER_PORT", "7861"))

    print(f"\n📡 Configurações do Servidor:")
    print(f"   • Host: {host}")
    print(f"   • Porta: {port}")
    print(f"   • URL: http://localhost:{port}")
    print(f"   • MCP Endpoint: http://localhost:{port}/gradio_api/mcp/sse")

    # Criar e lançar interface
    demo = create_gemini_osf_interface()

    print(f"\n🎯 Acesse a interface em: http://localhost:{port}")
    print("🤖 Digite comandos para buscar, baixar e analisar dados do OSF.io!")
    print("\n📋 Comandos disponíveis:")
    print("   • Buscar projetos: 'Buscar machine learning'")
    print("   • Download: 'Download https://osf.io/j4bv6/'")
    print("   • Analisar: 'Analisar dados baixados'")
    print("   • Gráficos: 'Criar gráfico dos dados'")

    demo.launch(
        server_name=host,
        server_port=port,
        mcp_server=True,
        share=False,
        show_error=True,
        show_api=True
    )


if __name__ == "__main__":
    main()
