#!/usr/bin/env python3
"""
Servidor MCP integrado com Google Gemini usando Gradio.

Este servidor combina o poder do Gemini para interpretação de linguagem natural
com funcionalidades de busca e análise de dados do OSF.io, incluindo capacidades
de visualização e análise de dados científicos.

Uso:
    python gemini_mcp_server.py

O servidor estará disponível em:
    http://localhost:7861
"""

import os
import json
import gradio as gr
import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
import seaborn as sns
from typing import Dict, Tuple, Optional
from datetime import datetime

# Importar dotenv se disponível
try:
    from dotenv import load_dotenv
    load_dotenv()
    HAS_DOTENV = True
except ImportError:
    HAS_DOTENV = False
    print("⚠️  python-dotenv não encontrado. Usando variáveis de ambiente do sistema.")

# Importar Google Gemini
try:
    import google.generativeai as genai
    HAS_GEMINI = True
except ImportError:
    HAS_GEMINI = False
    print("⚠️  google-generativeai não encontrado. Funcionalidades do Gemini desabilitadas.")

# Importar módulo OSF scraper
try:
    from src.osf_scraper import search_osf, download_osf_files
    HAS_OSF_SCRAPER = True
except ImportError:
    HAS_OSF_SCRAPER = False
    print("⚠️  Módulo src.osf_scraper não encontrado. Funcionalidades OSF desabilitadas.")

# Definir constantes para compatibilidade
HAS_QUADRATIC_SOLVER = False
HAS_ADVANCED_PLOTTING = False


class GeminiOSFMCPServer:
    """Servidor MCP integrado com Gemini e funcionalidades OSF."""

    def __init__(self):
        self.gemini_model = None
        self.chat_session = None
        self.conversation_history = []
        self.current_search_results = None
        self.current_downloaded_files = None
        self.current_analysis = None
        self.setup_gemini()
    
    def setup_gemini(self):
        """Configura o modelo Gemini."""
        if not HAS_GEMINI:
            print("❌ Google Gemini não disponível")
            return
        
        # Obter chave da API
        api_key = os.getenv("GOOGLE_API_KEY")
        if not api_key:
            print("❌ GOOGLE_API_KEY não encontrada no arquivo .env")
            return
        
        try:
            genai.configure(api_key=api_key)
            # Tentar diferentes modelos disponíveis
            try:
                self.gemini_model = genai.GenerativeModel('gemini-1.5-flash')
                print("✅ Google Gemini configurado com sucesso (gemini-1.5-flash)")
            except:
                try:
                    self.gemini_model = genai.GenerativeModel('gemini-1.5-pro')
                    print("✅ Google Gemini configurado com sucesso (gemini-1.5-pro)")
                except:
                    self.gemini_model = genai.GenerativeModel('models/gemini-1.5-flash-latest')
                    print("✅ Google Gemini configurado com sucesso (gemini-1.5-flash-latest)")
        except Exception as e:
            print(f"❌ Erro ao configurar Gemini: {e}")
            self.gemini_model = None

    def start_chat_session(self):
        """Inicia uma nova sessão de chat."""
        if not self.gemini_model:
            return False

        try:
            # Configurar o chat com contexto de pesquisa científica e análise de dados
            system_prompt = """
            Você é um assistente especializado em pesquisa científica e análise de dados.
            Você pode:
            1. Ajudar a buscar projetos de pesquisa no OSF.io
            2. Analisar dados científicos baixados
            3. Criar visualizações e gráficos de dados
            4. Interpretar resultados de análises estatísticas
            5. Sugerir metodologias de análise apropriadas
            6. Explicar conceitos de pesquisa científica

            Você tem acesso a ferramentas para:
            - Buscar projetos no OSF.io
            - Fazer download de arquivos de pesquisa
            - Analisar dados CSV, Excel e outros formatos
            - Criar gráficos e visualizações
            - Realizar análises estatísticas básicas

            Sempre seja didático, científico e forneça explicações detalhadas.
            Use emojis para tornar as explicações mais interessantes.
            Quando apropriado, sugira visualizações e análises adicionais.
            """

            self.chat_session = self.gemini_model.start_chat(history=[])
            self.conversation_history = []

            # Enviar prompt inicial (sem salvar resposta)
            self.chat_session.send_message(system_prompt)

            return True

        except Exception as e:
            print(f"Erro ao iniciar chat: {e}")
            return False

    def send_chat_message(self, message: str) -> str:
        """Envia mensagem para o chat e retorna resposta."""
        if not self.chat_session:
            if not self.start_chat_session():
                return "❌ Erro: Não foi possível iniciar sessão de chat com Gemini."

        try:
            # Adicionar contexto se a mensagem contém equação
            if any(char in message for char in ['x²', 'x^2', '=', '+', '-']) and 'x' in message:
                enhanced_message = f"""
                Mensagem do usuário: {message}

                Se esta mensagem contém uma equação quadrática, por favor:
                1. Identifique os coeficientes a, b, c
                2. Resolva passo a passo
                3. Explique o significado das soluções
                4. Sugira visualizações se apropriado

                Se for uma pergunta sobre conceitos, explique de forma didática.
                """
            else:
                enhanced_message = message

            response = self.chat_session.send_message(enhanced_message)

            # Salvar na história
            self.conversation_history.append({
                'user': message,
                'assistant': response.text,
                'timestamp': datetime.now().strftime('%H:%M:%S')
            })

            return response.text

        except Exception as e:
            return f"❌ Erro na conversa: {str(e)}"

    def get_conversation_history(self) -> str:
        """Retorna histórico da conversa formatado."""
        if not self.conversation_history:
            return "Nenhuma conversa ainda. Comece fazendo uma pergunta!"

        history_text = "## 📚 Histórico da Conversa\n\n"

        for i, entry in enumerate(self.conversation_history, 1):
            history_text += f"### 💬 Mensagem {i} ({entry['timestamp']})\n"
            history_text += f"**Você:** {entry['user']}\n\n"
            history_text += f"**Gemini:** {entry['assistant']}\n\n"
            history_text += "---\n\n"

        return history_text

    def clear_conversation(self):
        """Limpa a conversa atual."""
        self.conversation_history = []
        self.chat_session = None
        self.current_search_results = None
        self.current_downloaded_files = None
        self.current_analysis = None
    
    def extract_coefficients_with_gemini(self, text: str) -> Optional[Tuple[float, float, float]]:
        """Extrai coeficientes usando Gemini."""
        if not self.gemini_model:
            return None
        
        prompt = f"""
Extraia os coeficientes a, b, c da equação quadrática no seguinte texto:
"{text}"

A equação deve estar na forma ax² + bx + c = 0 ou f(x) = ax² + bx + c

Responda APENAS com três números separados por vírgula, na ordem a,b,c
Exemplo: 1,-5,6

Se não conseguir identificar uma equação quadrática válida, responda: ERRO

Texto: {text}
"""
        
        try:
            response = self.gemini_model.generate_content(prompt)
            result = response.text.strip()
            
            if result == "ERRO":
                return None
            
            coeffs = result.split(',')
            if len(coeffs) == 3:
                a = float(coeffs[0].strip())
                b = float(coeffs[1].strip())
                c = float(coeffs[2].strip())
                return (a, b, c)
            else:
                return None
                
        except Exception as e:
            print(f"Erro ao extrair coeficientes com Gemini: {e}")
            return None
    
    def explain_with_gemini(self, equation_text: str, mcp_result: Dict) -> str:
        """Gera explicação didática usando Gemini."""
        if not self.gemini_model:
            return json.dumps(mcp_result, indent=2, ensure_ascii=False)
        
        prompt = f"""
Com base na seguinte solução de equação quadrática, forneça uma explicação didática e completa:

Equação original: {equation_text}
Resultado do cálculo:
{json.dumps(mcp_result, indent=2, ensure_ascii=False)}

Forneça uma explicação que inclua:
1. A equação na forma padrão
2. Explicação do discriminante e seu significado
3. Interpretação das raízes encontradas
4. Conceitos matemáticos relevantes
5. Verificação das soluções (se possível)

Seja didático, educativo e use emojis para tornar mais interessante.
Explique cada passo de forma clara para estudantes.
"""
        
        try:
            response = self.gemini_model.generate_content(prompt)
            return response.text
        except Exception as e:
            return f"Erro na explicação: {str(e)}\n\nResultado bruto:\n{json.dumps(mcp_result, indent=2, ensure_ascii=False)}"

    def search_osf_with_gemini(self, query: str, max_results: int = 5) -> str:
        """Busca no OSF.io e analisa resultados com Gemini."""
        if not HAS_OSF_SCRAPER:
            return "❌ Funcionalidades OSF não disponíveis. Instale as dependências necessárias."

        try:
            # Realizar busca
            results = search_osf(query, max_results=max_results, use_selenium=True)
            self.current_search_results = results

            if not results:
                return f"❌ Nenhum resultado encontrado para '{query}'"

            # Formatar resultados para análise do Gemini
            results_text = f"Encontrados {len(results)} resultados para '{query}':\n\n"
            for i, result in enumerate(results, 1):
                results_text += f"{i}. **{result.get('title', 'Sem título')}**\n"
                results_text += f"   - URL: {result.get('url', 'N/A')}\n"
                results_text += f"   - Tipo: {result.get('type', 'N/A')}\n"
                results_text += f"   - Autores: {result.get('authors', 'N/A')}\n"
                if result.get('description'):
                    results_text += f"   - Descrição: {result['description'][:100]}...\n"
                results_text += "\n"

            # Analisar com Gemini se disponível
            if self.gemini_model:
                analysis_prompt = f"""
                Analise os seguintes resultados de busca do OSF.io para a consulta '{query}':

                {results_text}

                Por favor:
                1. Resuma os principais temas encontrados
                2. Identifique os projetos mais relevantes
                3. Sugira quais projetos podem ter dados interessantes para análise
                4. Recomende próximos passos para análise de dados

                Seja específico e didático.
                """

                try:
                    response = self.gemini_model.generate_content(analysis_prompt)
                    return f"{results_text}\n## 🤖 Análise do Gemini:\n\n{response.text}"
                except Exception as e:
                    return f"{results_text}\n❌ Erro na análise do Gemini: {str(e)}"
            else:
                return results_text

        except Exception as e:
            return f"❌ Erro na busca OSF: {str(e)}"

    def download_osf_files_with_gemini(self, project_url: str, download_dir: str = "osf_downloads") -> str:
        """Faz download de arquivos OSF e analisa com Gemini."""
        if not HAS_OSF_SCRAPER:
            return "❌ Funcionalidades OSF não disponíveis. Instale as dependências necessárias."

        try:
            # Fazer download
            files = download_osf_files(project_url, download_dir)
            self.current_downloaded_files = files

            if not files:
                return f"❌ Nenhum arquivo encontrado no projeto {project_url}"

            # Formatar informações dos arquivos
            files_text = f"Download concluído! {len(files)} arquivos baixados:\n\n"
            for i, file_info in enumerate(files, 1):
                files_text += f"{i}. **{file_info.get('name', 'Sem nome')}**\n"
                files_text += f"   - Tamanho: {file_info.get('size', 'N/A')}\n"
                files_text += f"   - Caminho local: {file_info.get('local_path', 'N/A')}\n"
                files_text += f"   - Status: {file_info.get('status', 'N/A')}\n\n"

            # Analisar com Gemini se disponível
            if self.gemini_model:
                analysis_prompt = f"""
                Analise os seguintes arquivos baixados do OSF.io:

                {files_text}

                Por favor:
                1. Identifique os tipos de dados disponíveis
                2. Sugira análises apropriadas para cada tipo de arquivo
                3. Recomende visualizações que seriam úteis
                4. Proponha hipóteses de pesquisa baseadas nos arquivos

                Seja específico sobre métodos de análise de dados.
                """

                try:
                    response = self.gemini_model.generate_content(analysis_prompt)
                    return f"{files_text}\n## 🤖 Análise do Gemini:\n\n{response.text}"
                except Exception as e:
                    return f"{files_text}\n❌ Erro na análise do Gemini: {str(e)}"
            else:
                return files_text

        except Exception as e:
            return f"❌ Erro no download OSF: {str(e)}"


# Instância global do servidor
gemini_server = GeminiOSFMCPServer()


def process_user_request(user_input: str) -> Tuple[str, str]:
    """
    Processa solicitação do usuário usando Gemini + MCP para OSF.

    Args:
        user_input: Entrada do usuário em linguagem natural

    Returns:
        Tuple com resposta e dados JSON
    """
    if not user_input.strip():
        return "❌ Por favor, digite sua solicitação.", "{}"

    print(f"🔍 Processando entrada: {user_input}")

    # Analisar a intenção do usuário com Gemini
    if gemini_server.gemini_model:
        intent_prompt = f"""
        Analise a seguinte solicitação do usuário e determine a intenção:
        "{user_input}"

        Responda apenas com uma das seguintes opções:
        - SEARCH_OSF: se o usuário quer buscar projetos no OSF.io
        - DOWNLOAD_OSF: se o usuário quer fazer download de arquivos
        - ANALYZE_DATA: se o usuário quer analisar dados
        - CREATE_PLOT: se o usuário quer criar gráficos
        - CHAT: se é uma pergunta geral ou conversa

        Resposta:
        """

        try:
            intent_response = gemini_server.gemini_model.generate_content(intent_prompt)
            intent = intent_response.text.strip()
            print(f"🎯 Intenção detectada: {intent}")
        except:
            intent = "CHAT"
    else:
        # Fallback: detectar intenção por palavras-chave
        user_lower = user_input.lower()
        if any(word in user_lower for word in ['buscar', 'search', 'procurar', 'encontrar']):
            intent = "SEARCH_OSF"
        elif any(word in user_lower for word in ['download', 'baixar', 'arquivos']):
            intent = "DOWNLOAD_OSF"
        elif any(word in user_lower for word in ['analisar', 'análise', 'dados']):
            intent = "ANALYZE_DATA"
        elif any(word in user_lower for word in ['gráfico', 'plot', 'visualizar']):
            intent = "CREATE_PLOT"
        else:
            intent = "CHAT"

    # Processar baseado na intenção
    try:
        if intent == "SEARCH_OSF":
            # Extrair termo de busca
            if gemini_server.gemini_model:
                extract_prompt = f"""
                Extraia o termo de busca da seguinte solicitação:
                "{user_input}"

                Responda apenas com o termo de busca, sem explicações.
                """
                try:
                    extract_response = gemini_server.gemini_model.generate_content(extract_prompt)
                    search_term = extract_response.text.strip().strip('"\'')
                except:
                    search_term = user_input
            else:
                search_term = user_input

            result = gemini_server.search_osf_with_gemini(search_term, 5)
            return result, json.dumps({"action": "search", "term": search_term}, indent=2)

        elif intent == "DOWNLOAD_OSF":
            # Extrair URL do projeto
            import re
            url_pattern = r'https?://osf\.io/[a-zA-Z0-9]+/?'
            urls = re.findall(url_pattern, user_input)

            if urls:
                project_url = urls[0]
                result = gemini_server.download_osf_files_with_gemini(project_url)
                return result, json.dumps({"action": "download", "url": project_url}, indent=2)
            else:
                return "❌ Não consegui encontrar uma URL válida do OSF.io na sua solicitação.", "{}"

        elif intent == "ANALYZE_DATA":
            # Verificar se há arquivos baixados
            if gemini_server.current_downloaded_files:
                # Analisar o primeiro arquivo CSV/Excel encontrado
                for file_info in gemini_server.current_downloaded_files:
                    file_path = file_info.get('local_path', '')
                    if file_path and file_path.endswith(('.csv', '.xlsx', '.xls')):
                        result = analyze_data_mcp_tool(file_path)
                        return result, json.dumps({"action": "analyze", "file": file_path}, indent=2)

                return "❌ Nenhum arquivo de dados (CSV/Excel) encontrado nos downloads.", "{}"
            else:
                return "❌ Nenhum arquivo foi baixado ainda. Primeiro faça download de um projeto OSF.", "{}"

        elif intent == "CREATE_PLOT":
            # Verificar se há arquivos baixados
            if gemini_server.current_downloaded_files:
                # Criar gráfico do primeiro arquivo encontrado
                for file_info in gemini_server.current_downloaded_files:
                    file_path = file_info.get('local_path', '')
                    if file_path and file_path.endswith(('.csv', '.xlsx', '.xls')):
                        # Determinar tipo de gráfico
                        plot_type = "histogram"
                        if "correlação" in user_input.lower() or "correlation" in user_input.lower():
                            plot_type = "correlation"
                        elif "scatter" in user_input.lower() or "dispersão" in user_input.lower():
                            plot_type = "scatter"

                        result = create_plot_mcp_tool(file_path, plot_type)
                        return f"Gráfico criado para {os.path.basename(file_path)}", result

                return "❌ Nenhum arquivo de dados encontrado para criar gráfico.", "{}"
            else:
                return "❌ Nenhum arquivo foi baixado ainda. Primeiro faça download de um projeto OSF.", "{}"

        else:  # CHAT
            response = gemini_server.send_chat_message(user_input)
            return response, json.dumps({"action": "chat", "message": user_input}, indent=2)

    except Exception as e:
        print(f"❌ Erro no processamento: {e}")
        return f"❌ Erro ao processar solicitação: {str(e)}", "{}"


def search_osf_mcp_tool(query: str, max_results: str = "5") -> str:
    """Ferramenta MCP para buscar no OSF.io."""
    try:
        max_results_int = int(max_results)
        result = gemini_server.search_osf_with_gemini(query, max_results_int)
        return result
    except Exception as e:
        return f"❌ Erro na busca OSF: {str(e)}"


def download_osf_mcp_tool(project_url: str, download_dir: str = "osf_downloads") -> str:
    """Ferramenta MCP para fazer download de arquivos OSF."""
    try:
        result = gemini_server.download_osf_files_with_gemini(project_url, download_dir)
        return result
    except Exception as e:
        return f"❌ Erro no download OSF: {str(e)}"


def analyze_data_mcp_tool(file_path: str) -> str:
    """Ferramenta MCP para analisar dados de arquivo baixado."""
    try:
        if not os.path.exists(file_path):
            return f"❌ Arquivo não encontrado: {file_path}"

        # Determinar tipo de arquivo e analisar
        if file_path.endswith('.csv'):
            df = pd.read_csv(file_path)
            analysis = analyze_dataframe_with_gemini(df, file_path)
            return analysis
        elif file_path.endswith(('.xlsx', '.xls')):
            df = pd.read_excel(file_path)
            analysis = analyze_dataframe_with_gemini(df, file_path)
            return analysis
        else:
            return f"❌ Tipo de arquivo não suportado: {file_path}"

    except Exception as e:
        return f"❌ Erro na análise de dados: {str(e)}"


def create_plot_mcp_tool(file_path: str, plot_type: str = "histogram") -> str:
    """Ferramenta MCP para criar gráficos de dados."""
    try:
        if not os.path.exists(file_path):
            return f"❌ Arquivo não encontrado: {file_path}"

        # Carregar dados
        if file_path.endswith('.csv'):
            df = pd.read_csv(file_path)
        elif file_path.endswith(('.xlsx', '.xls')):
            df = pd.read_excel(file_path)
        else:
            return f"❌ Tipo de arquivo não suportado: {file_path}"

        # Criar gráfico baseado no tipo
        fig = create_data_visualization(df, plot_type, file_path)
        return fig

    except Exception as e:
        return f"❌ Erro na criação do gráfico: {str(e)}"


def analyze_dataframe_with_gemini(df: pd.DataFrame, file_path: str) -> str:
    """Analisa DataFrame com Gemini."""
    try:
        # Informações básicas do DataFrame
        info_text = f"""
## 📊 Análise do arquivo: {os.path.basename(file_path)}

### Informações Básicas:
- **Linhas:** {len(df)}
- **Colunas:** {len(df.columns)}
- **Colunas:** {', '.join(df.columns.tolist())}

### Tipos de Dados:
{df.dtypes.to_string()}

### Estatísticas Descritivas:
{df.describe().to_string()}

### Primeiras 5 linhas:
{df.head().to_string()}
"""

        # Analisar com Gemini se disponível
        if gemini_server.gemini_model:
            analysis_prompt = f"""
            Analise os seguintes dados científicos:

            {info_text}

            Por favor:
            1. Identifique padrões interessantes nos dados
            2. Sugira análises estatísticas apropriadas
            3. Recomende visualizações úteis
            4. Proponha hipóteses de pesquisa
            5. Identifique possíveis problemas nos dados (valores ausentes, outliers, etc.)

            Seja específico e científico na análise.
            """

            try:
                response = gemini_server.gemini_model.generate_content(analysis_prompt)
                return f"{info_text}\n## 🤖 Análise do Gemini:\n\n{response.text}"
            except Exception as e:
                return f"{info_text}\n❌ Erro na análise do Gemini: {str(e)}"
        else:
            return info_text

    except Exception as e:
        return f"❌ Erro na análise do DataFrame: {str(e)}"


def create_data_visualization(df: pd.DataFrame, plot_type: str, file_path: str):
    """Cria visualização de dados."""
    try:
        plt.style.use('default')

        if plot_type == "histogram":
            # Criar histogramas para colunas numéricas
            numeric_cols = df.select_dtypes(include=[np.number]).columns
            if len(numeric_cols) == 0:
                return "❌ Nenhuma coluna numérica encontrada para histograma"

            n_cols = min(3, len(numeric_cols))
            n_rows = (len(numeric_cols) + n_cols - 1) // n_cols

            fig, axes = plt.subplots(n_rows, n_cols, figsize=(15, 5*n_rows))
            if n_rows == 1 and n_cols == 1:
                axes = [axes]
            elif n_rows == 1:
                axes = axes
            else:
                axes = axes.flatten()

            for i, col in enumerate(numeric_cols):
                if i < len(axes):
                    df[col].hist(bins=20, ax=axes[i], alpha=0.7)
                    axes[i].set_title(f'Histograma: {col}')
                    axes[i].set_xlabel(col)
                    axes[i].set_ylabel('Frequência')

            # Remover subplots vazios
            for i in range(len(numeric_cols), len(axes)):
                fig.delaxes(axes[i])

            plt.tight_layout()
            return fig

        elif plot_type == "correlation":
            # Matriz de correlação
            numeric_df = df.select_dtypes(include=[np.number])
            if len(numeric_df.columns) < 2:
                return "❌ Pelo menos 2 colunas numéricas necessárias para correlação"

            fig, ax = plt.subplots(figsize=(10, 8))
            correlation_matrix = numeric_df.corr()
            sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', center=0, ax=ax)
            ax.set_title(f'Matriz de Correlação - {os.path.basename(file_path)}')
            plt.tight_layout()
            return fig

        elif plot_type == "scatter":
            # Scatter plot das duas primeiras colunas numéricas
            numeric_cols = df.select_dtypes(include=[np.number]).columns
            if len(numeric_cols) < 2:
                return "❌ Pelo menos 2 colunas numéricas necessárias para scatter plot"

            fig, ax = plt.subplots(figsize=(10, 6))
            ax.scatter(df[numeric_cols[0]], df[numeric_cols[1]], alpha=0.6)
            ax.set_xlabel(numeric_cols[0])
            ax.set_ylabel(numeric_cols[1])
            ax.set_title(f'Scatter Plot: {numeric_cols[0]} vs {numeric_cols[1]}')
            plt.tight_layout()
            return fig

        else:
            return f"❌ Tipo de gráfico não suportado: {plot_type}"

    except Exception as e:
        return f"❌ Erro na criação da visualização: {str(e)}"


def process_with_visualization(user_input: str) -> Tuple[str, str, str]:
    """
    Processa solicitação do usuário com visualização usando Gemini + MCP.

    Args:
        user_input: Entrada do usuário em linguagem natural

    Returns:
        Tuple com resposta, dados JSON e gráfico
    """
    # Primeiro processar normalmente
    response, json_result = process_user_request(user_input)

    # Tentar criar visualização se apropriado
    try:
        # Verificar se há dados para visualizar
        if gemini_server.current_downloaded_files:
            for file_info in gemini_server.current_downloaded_files:
                file_path = file_info.get('local_path', '')
                if file_path and file_path.endswith(('.csv', '.xlsx', '.xls')):
                    # Criar visualização automática
                    fig = create_plot_mcp_tool(file_path, "histogram")
                    return response, json_result, fig

        # Se não há dados, retornar sem gráfico
        return response, json_result, None

    except Exception as e:
        return response, json_result, f"Erro ao criar visualização: {str(e)}"


def interactive_chat(message: str, history: str) -> Tuple[str, str]:
    """
    Função para chat interativo com Gemini.

    Args:
        message: Nova mensagem do usuário
        history: Histórico da conversa

    Returns:
        Tuple com (resposta, histórico_atualizado)
    """
    if not message.strip():
        return "Por favor, digite uma mensagem.", history

    try:
        # Enviar mensagem para o Gemini
        response = gemini_server.send_chat_message(message)

        # Obter histórico atualizado
        updated_history = gemini_server.get_conversation_history()

        return response, updated_history

    except Exception as e:
        return f"❌ Erro no chat: {str(e)}", history


def clear_chat_history() -> Tuple[str, str]:
    """Limpa o histórico do chat."""
    try:
        gemini_server.clear_conversation()
        return "", "Conversa limpa! Comece uma nova discussão."
    except Exception as e:
        return "", f"Erro ao limpar conversa: {str(e)}"


def suggest_research_topics() -> str:
    """Sugere tópicos de pesquisa para discussão."""
    return """
## 💡 Sugestões de Comandos e Tópicos:

### 🔍 **Buscar Projetos OSF**
- "Buscar projetos sobre machine learning"
- "Procurar dados de psicologia experimental"
- "Encontrar estudos sobre neurociência"

### 📥 **Download de Dados**
- "Fazer download de https://osf.io/j4bv6/"
- "Baixar arquivos do projeto https://osf.io/2zfu4/"

### 📊 **Análise de Dados**
- "Analisar os dados baixados"
- "Criar estatísticas descritivas"
- "Verificar qualidade dos dados"

### 📈 **Visualizações**
- "Criar histograma dos dados"
- "Gerar matriz de correlação"
- "Fazer scatter plot das variáveis"

### 🤔 **Discussão Científica**
- "Como interpretar estes resultados?"
- "Que análises estatísticas são apropriadas?"
- "Como melhorar a qualidade dos dados?"

### 🧪 **Projetos de Exemplo**
- **Machine Learning ICH:** https://osf.io/j4bv6/
- **Moral Algorithms:** https://osf.io/2zfu4/

**Digite qualquer comando ou pergunta para começar!** 🚀
    """


def create_gemini_osf_interface():
    """Cria interface Gradio integrada com Gemini e MCP para OSF."""
    with gr.Blocks(
        title="Gemini + MCP - Pesquisa Científica OSF",
        theme=gr.themes.Soft(),
        css="""
        .gradio-container {
            max-width: 1400px !important;
        }
        .gemini-header {
            background: linear-gradient(135deg, #4285f4 0%, #34a853 50%, #fbbc05 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 10px 0;
            text-align: center;
        }
        .mcp-info {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }
        """
    ) as demo:
        gr.HTML("""
        <div class=\"gemini-header\">
            <h1>🤖 Gemini + MCP - Pesquisa Científica OSF</h1>
            <p><strong>Inteligência Artificial + Análise de Dados Científicos</strong></p>
            <p>Busque, baixe e analise dados do OSF.io com IA!</p>
        </div>
        """)

        gr.Markdown("### 🎯 Análise Interativa de Dados Científicos com IA")
        gr.Markdown("**Combine busca OSF, análise de dados e chat interativo do Gemini!**")

        with gr.Row():
            with gr.Column(scale=2):
                main_input = gr.Textbox(
                    label="Digite sua solicitação ou pergunta",
                    placeholder="Ex: Buscar projetos sobre machine learning ou Analisar dados baixados",
                    lines=3
                )
                with gr.Row():
                    analyze_btn = gr.Button("🎯 Processar com Gráficos e Gemini", variant="primary", scale=2)
                    chat_btn = gr.Button("💬 Enviar Mensagem para Gemini", variant="secondary", scale=2)
                    clear_all_btn = gr.Button("🗑️ Limpar Tudo", scale=1)
                gr.Markdown("#### 🧩 Exemplos Rápidos")
                with gr.Row():
                    ex1_btn = gr.Button("Buscar machine learning", size="sm")
                    ex2_btn = gr.Button("Download https://osf.io/j4bv6/", size="sm")
                with gr.Row():
                    ex3_btn = gr.Button("Analisar dados baixados", size="sm")
                    ex4_btn = gr.Button("Criar gráfico dos dados", size="sm")
                conversation_history = gr.Markdown(
                    value=suggest_research_topics(),
                    label="Conversa e Análises",
                    elem_id="conversation_area"
                )
            with gr.Column(scale=2):
                interactive_plot = gr.Plot(
                    label="Gráfico Interativo",
                    value=None
                )
                technical_data = gr.JSON(
                    label="Dados Técnicos",
                    show_label=True
                )
        current_gemini_response = gr.Markdown(
            label="Resposta Atual do Gemini",
            value="Digite uma equação ou pergunta acima para começar! 🤖"
        )
        status_display = gr.Markdown(
            value="**Status:** Pronto para análise ✅"
        )
        gr.HTML("""
        <div class=\"mcp-info\">
            <h3>📡 Configuração do Servidor</h3>
            <p><strong>URL MCP:</strong> <code>http://localhost:7861/gradio_api/mcp/sse</code></p>
            <p><strong>Status Gemini:</strong> """ + ("✅ Ativo" if gemini_server.gemini_model else "❌ Inativo") + """</p>
            <p><strong>Status OSF Scraper:</strong> """ + ("✅ Ativo" if HAS_OSF_SCRAPER else "❌ Inativo") + """</p>
            <p><strong>Ferramentas Disponíveis:</strong></p>
            <ul>
                <li><code>search_osf_mcp_tool</code> - Busca projetos no OSF.io</li>
                <li><code>download_osf_mcp_tool</code> - Faz download de arquivos OSF</li>
                <li><code>analyze_data_mcp_tool</code> - Analisa dados científicos</li>
                <li><code>create_plot_mcp_tool</code> - Cria visualizações de dados</li>
                <li><code>process_user_request</code> - Processa solicitações com Gemini</li>
                <li><code>interactive_chat</code> - Chat interativo com Gemini</li>
            </ul>
        </div>
        """)

        # Lógica de callbacks
        def on_analyze_click(user_input, _):
            response, json_result, fig = process_with_visualization(user_input)
            # Atualiza histórico e resposta
            if gemini_server.gemini_model:
                chat_response, updated_history = interactive_chat(user_input, "")
            else:
                chat_response, updated_history = response, ""
            return chat_response, updated_history, fig, json_result, "**Status:** Processamento concluído ✅"

        def on_chat_click(user_input, history):
            response, updated_history = interactive_chat(user_input, history)
            return response, updated_history, gr.update(), gr.update(), "**Status:** Mensagem enviada ao Gemini ✅"

        def on_clear_all():
            clear_chat_history()
            return "Digite uma solicitação ou pergunta acima para começar! 🤖", suggest_research_topics(), None, None, "**Status:** Pronto para análise ✅"

        analyze_btn.click(
            on_analyze_click,
            inputs=[main_input, conversation_history],
            outputs=[current_gemini_response, conversation_history, interactive_plot, technical_data, status_display]
        )
        chat_btn.click(
            on_chat_click,
            inputs=[main_input, conversation_history],
            outputs=[current_gemini_response, conversation_history, interactive_plot, technical_data, status_display]
        )
        clear_all_btn.click(
            on_clear_all,
            inputs=[],
            outputs=[current_gemini_response, conversation_history, interactive_plot, technical_data, status_display]
        )
        # Exemplos rápidos
        ex1_btn.click(lambda: "Buscar machine learning", None, main_input)
        ex2_btn.click(lambda: "Download https://osf.io/j4bv6/", None, main_input)
        ex3_btn.click(lambda: "Analisar dados baixados", None, main_input)
        ex4_btn.click(lambda: "Criar gráfico dos dados", None, main_input)
    return demo


def main():
    """Função principal."""
    print("🚀 Iniciando Servidor Gemini + MCP + OSF...")
    print("=" * 60)

    # Status das dependências
    print("📦 Status das Dependências:")
    print(f"   • python-dotenv: {'✅' if HAS_DOTENV else '❌'}")
    print(f"   • google-generativeai: {'✅' if HAS_GEMINI else '❌'}")
    print(f"   • quadratic_solver: {'✅' if HAS_QUADRATIC_SOLVER else '❌ (usando básico)'}")
    print(f"   • advanced_plotting: {'✅' if HAS_ADVANCED_PLOTTING else '❌'}")
    print(f"   • osf_scraper: {'✅' if HAS_OSF_SCRAPER else '❌'}")

    # Status do Gemini
    if gemini_server.gemini_model:
        print("   • Gemini Model: ✅ Configurado")
    else:
        print("   • Gemini Model: ❌ Não configurado")
        print("     💡 Verifique se GOOGLE_API_KEY está no arquivo .env")

    # Configurações do servidor
    host = os.getenv("MCP_SERVER_HOST", "0.0.0.0")
    port = int(os.getenv("MCP_SERVER_PORT", "7861"))

    print(f"\n📡 Configurações do Servidor:")
    print(f"   • Host: {host}")
    print(f"   • Porta: {port}")
    print(f"   • URL: http://localhost:{port}")
    print(f"   • MCP Endpoint: http://localhost:{port}/gradio_api/mcp/sse")

    # Criar e lançar interface
    demo = create_gemini_osf_interface()

    print(f"\n🎯 Acesse a interface em: http://localhost:{port}")
    print("🤖 Digite comandos para buscar, baixar e analisar dados do OSF.io!")
    print("\n📋 Comandos disponíveis:")
    print("   • Buscar projetos: 'Buscar machine learning'")
    print("   • Download: 'Download https://osf.io/j4bv6/'")
    print("   • Analisar: 'Analisar dados baixados'")
    print("   • Gráficos: 'Criar gráfico dos dados'")

    demo.launch(
        server_name=host,
        server_port=port,
        mcp_server=True,
        share=False,
        show_error=True,
        show_api=True
    )


if __name__ == "__main__":
    main()
