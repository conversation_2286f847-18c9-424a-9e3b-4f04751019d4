#!/usr/bin/env python3
"""
Servidor MCP integrado com Google Gemini usando Gradio.

Este servidor combina o poder do Gemini para interpretação de linguagem natural
com o servidor MCP para cálculos matemáticos precisos de equações quadráticas.

Uso:
    python gemini_mcp_server.py

O servidor estará disponível em:
    http://localhost:7861
"""

import os
import json
import gradio as gr
import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, Tuple, List, Union, Optional
from datetime import datetime

# Importar dotenv se disponível
try:
    from dotenv import load_dotenv
    load_dotenv()
    HAS_DOTENV = True
except ImportError:
    HAS_DOTENV = False
    print("⚠️  python-dotenv não encontrado. Usando variáveis de ambiente do sistema.")

# Importar Google Gemini
try:
    import google.generativeai as genai
    HAS_GEMINI = True
except ImportError:
    HAS_GEMINI = False
    print("⚠️  google-generativeai não encontrado. Funcionalidades do Gemini desabilitadas.")

# Importar módulo de equações quadráticas
try:
    from src.quadratic_solver import solve_quadratic, format_solution, plot_quadratic
    HAS_QUADRATIC_SOLVER = True
except ImportError:
    HAS_QUADRATIC_SOLVER = False
    print("⚠️  Módulo src.quadratic_solver não encontrado. Usando implementação básica.")

# Importar módulo de gráficos avançados
try:
    from src.advanced_plotting import (
        create_interactive_quadratic_plot,
        create_solution_method_visualization
    )
    HAS_ADVANCED_PLOTTING = True
except ImportError:
    HAS_ADVANCED_PLOTTING = False
    print("⚠️  Módulo src.advanced_plotting não encontrado. Gráficos avançados desabilitados.")
    
    # Implementação básica
    def solve_quadratic(a, b, c):
        result = {'coefficients': {'a': a, 'b': b, 'c': c}, 'equation_type': 'quadratic' if a != 0 else 'linear', 'discriminant': None, 'roots': [], 'roots_type': '', 'vertex': None, 'axis_of_symmetry': None, 'y_intercept': c, 'concavity': '', 'factored_form': '', 'has_real_roots': False}
        if a == 0:
            if b == 0:
                result['equation_type'] = 'invalid'
                result['roots_type'] = 'sem solução válida'
            else:
                result['equation_type'] = 'linear'
                root = -c / b
                result['roots'] = [root]
                result['roots_type'] = 'uma raiz real'
                result['has_real_roots'] = True
        else:
            discriminant = b**2 - 4*a*c
            result['discriminant'] = discriminant
            result['vertex'] = (-b/(2*a), a*(-b/(2*a))**2 + b*(-b/(2*a)) + c)
            result['axis_of_symmetry'] = -b/(2*a)
            result['concavity'] = 'para cima' if a > 0 else 'para baixo'
            if discriminant > 0:
                x1 = (-b + discriminant**0.5) / (2*a)
                x2 = (-b - discriminant**0.5) / (2*a)
                result['roots'] = [x1, x2]
                result['roots_type'] = 'duas raízes reais distintas'
                result['has_real_roots'] = True
                result['factored_form'] = f"{a}(x - {x1:.3f})(x - {x2:.3f})"
            elif discriminant == 0:
                x = -b / (2*a)
                result['roots'] = [x]
                result['roots_type'] = 'uma raiz real (raiz dupla)'
                result['has_real_roots'] = True
                result['factored_form'] = f"{a}(x - {x:.3f})²"
            else:
                real_part = -b / (2*a)
                imag_part = (-discriminant)**0.5 / (2*a)
                x1 = complex(real_part, imag_part)
                x2 = complex(real_part, -imag_part)
                result['roots'] = [x1, x2]
                result['roots_type'] = 'duas raízes complexas conjugadas'
                result['has_real_roots'] = False
        return result
    
    def format_solution(solution):
        a, b, c = solution['coefficients']['a'], solution['coefficients']['b'], solution['coefficients']['c']
        if solution['equation_type'] == 'invalid':
            return "❌ Equação inválida"
        elif solution['equation_type'] == 'linear':
            x = solution['roots'][0]
            return f"📐 Equação Linear: {b}x + {c} = 0\n✅ Solução: x = {x:.6f}"
        else:
            text = f"📐 Equação Quadrática: {a}x² + {b}x + {c} = 0\n"
            text += f"🔍 Discriminante: {solution['discriminant']:.6f}\n"
            text += f"✅ {solution['roots_type']}\n"
            if solution['has_real_roots']:
                for i, root in enumerate(solution['roots'], 1):
                    if isinstance(root, complex):
                        text += f"x{i} = {root.real:.6f} + {root.imag:.6f}i\n"
                    else:
                        text += f"x{i} = {root:.6f}\n"
            return text


class GeminiMCPServer:
    """Servidor MCP integrado com Gemini."""

    def __init__(self):
        self.gemini_model = None
        self.chat_session = None
        self.conversation_history = []
        self.setup_gemini()
    
    def setup_gemini(self):
        """Configura o modelo Gemini."""
        if not HAS_GEMINI:
            print("❌ Google Gemini não disponível")
            return
        
        # Obter chave da API
        api_key = os.getenv("GOOGLE_API_KEY")
        if not api_key:
            print("❌ GOOGLE_API_KEY não encontrada no arquivo .env")
            return
        
        try:
            genai.configure(api_key=api_key)
            # Tentar diferentes modelos disponíveis
            try:
                self.gemini_model = genai.GenerativeModel('gemini-1.5-flash')
                print("✅ Google Gemini configurado com sucesso (gemini-1.5-flash)")
            except:
                try:
                    self.gemini_model = genai.GenerativeModel('gemini-1.5-pro')
                    print("✅ Google Gemini configurado com sucesso (gemini-1.5-pro)")
                except:
                    self.gemini_model = genai.GenerativeModel('models/gemini-1.5-flash-latest')
                    print("✅ Google Gemini configurado com sucesso (gemini-1.5-flash-latest)")
        except Exception as e:
            print(f"❌ Erro ao configurar Gemini: {e}")
            self.gemini_model = None

    def start_chat_session(self):
        """Inicia uma nova sessão de chat."""
        if not self.gemini_model:
            return False

        try:
            # Configurar o chat com contexto matemático
            system_prompt = """
            Você é um assistente especializado em matemática, especificamente em equações quadráticas.
            Você pode:
            1. Resolver equações quadráticas passo a passo
            2. Explicar conceitos matemáticos de forma didática
            3. Corrigir erros em soluções
            4. Sugerir métodos alternativos de resolução
            5. Discutir propriedades das funções quadráticas

            Sempre seja didático, paciente e forneça explicações detalhadas.
            Use emojis para tornar as explicações mais interessantes.
            Quando apropriado, sugira visualizações gráficas.
            """

            self.chat_session = self.gemini_model.start_chat(history=[])
            self.conversation_history = []

            # Enviar prompt inicial (sem salvar resposta)
            self.chat_session.send_message(system_prompt)

            return True

        except Exception as e:
            print(f"Erro ao iniciar chat: {e}")
            return False

    def send_chat_message(self, message: str) -> str:
        """Envia mensagem para o chat e retorna resposta."""
        if not self.chat_session:
            if not self.start_chat_session():
                return "❌ Erro: Não foi possível iniciar sessão de chat com Gemini."

        try:
            # Adicionar contexto se a mensagem contém equação
            if any(char in message for char in ['x²', 'x^2', '=', '+', '-']) and 'x' in message:
                enhanced_message = f"""
                Mensagem do usuário: {message}

                Se esta mensagem contém uma equação quadrática, por favor:
                1. Identifique os coeficientes a, b, c
                2. Resolva passo a passo
                3. Explique o significado das soluções
                4. Sugira visualizações se apropriado

                Se for uma pergunta sobre conceitos, explique de forma didática.
                """
            else:
                enhanced_message = message

            response = self.chat_session.send_message(enhanced_message)

            # Salvar na história
            self.conversation_history.append({
                'user': message,
                'assistant': response.text,
                'timestamp': datetime.now().strftime('%H:%M:%S')
            })

            return response.text

        except Exception as e:
            return f"❌ Erro na conversa: {str(e)}"

    def get_conversation_history(self) -> str:
        """Retorna histórico da conversa formatado."""
        if not self.conversation_history:
            return "Nenhuma conversa ainda. Comece fazendo uma pergunta!"

        history_text = "## 📚 Histórico da Conversa\n\n"

        for i, entry in enumerate(self.conversation_history, 1):
            history_text += f"### 💬 Mensagem {i} ({entry['timestamp']})\n"
            history_text += f"**Você:** {entry['user']}\n\n"
            history_text += f"**Gemini:** {entry['assistant']}\n\n"
            history_text += "---\n\n"

        return history_text

    def clear_conversation(self):
        """Limpa a conversa atual."""
        self.conversation_history = []
        self.chat_session = None
    
    def extract_coefficients_with_gemini(self, text: str) -> Optional[Tuple[float, float, float]]:
        """Extrai coeficientes usando Gemini."""
        if not self.gemini_model:
            return None
        
        prompt = f"""
Extraia os coeficientes a, b, c da equação quadrática no seguinte texto:
"{text}"

A equação deve estar na forma ax² + bx + c = 0 ou f(x) = ax² + bx + c

Responda APENAS com três números separados por vírgula, na ordem a,b,c
Exemplo: 1,-5,6

Se não conseguir identificar uma equação quadrática válida, responda: ERRO

Texto: {text}
"""
        
        try:
            response = self.gemini_model.generate_content(prompt)
            result = response.text.strip()
            
            if result == "ERRO":
                return None
            
            coeffs = result.split(',')
            if len(coeffs) == 3:
                a = float(coeffs[0].strip())
                b = float(coeffs[1].strip())
                c = float(coeffs[2].strip())
                return (a, b, c)
            else:
                return None
                
        except Exception as e:
            print(f"Erro ao extrair coeficientes com Gemini: {e}")
            return None
    
    def explain_with_gemini(self, equation_text: str, mcp_result: Dict) -> str:
        """Gera explicação didática usando Gemini."""
        if not self.gemini_model:
            return json.dumps(mcp_result, indent=2, ensure_ascii=False)
        
        prompt = f"""
Com base na seguinte solução de equação quadrática, forneça uma explicação didática e completa:

Equação original: {equation_text}
Resultado do cálculo:
{json.dumps(mcp_result, indent=2, ensure_ascii=False)}

Forneça uma explicação que inclua:
1. A equação na forma padrão
2. Explicação do discriminante e seu significado
3. Interpretação das raízes encontradas
4. Conceitos matemáticos relevantes
5. Verificação das soluções (se possível)

Seja didático, educativo e use emojis para tornar mais interessante.
Explique cada passo de forma clara para estudantes.
"""
        
        try:
            response = self.gemini_model.generate_content(prompt)
            return response.text
        except Exception as e:
            return f"Erro na explicação: {str(e)}\n\nResultado bruto:\n{json.dumps(mcp_result, indent=2, ensure_ascii=False)}"


# Instância global do servidor
gemini_server = GeminiMCPServer()


def solve_with_gemini_mcp(user_input: str) -> Tuple[str, str]:
    """
    Resolve equação quadrática usando Gemini + MCP.

    Args:
        user_input: Entrada do usuário em linguagem natural

    Returns:
        Tuple com explicação e resultado JSON
    """
    if not user_input.strip():
        return "❌ Por favor, digite uma equação quadrática.", "{}"

    print(f"🔍 Processando entrada: {user_input}")

    # Tentar extrair coeficientes com Gemini
    coeffs = gemini_server.extract_coefficients_with_gemini(user_input)

    if not coeffs:
        print("⚠️ Gemini falhou, tentando extração manual...")
        # Fallback: tentar extrair manualmente
        try:
            # Buscar padrões simples como "x² - 5x + 6"
            import re

            # Padrões mais robustos
            patterns = [
                r'([+-]?\d*\.?\d*)\s*x²\s*([+-]\s*\d*\.?\d*)\s*x\s*([+-]\s*\d*\.?\d*)',
                r'([+-]?\d*\.?\d*)\s*x\^2\s*([+-]\s*\d*\.?\d*)\s*x\s*([+-]\s*\d*\.?\d*)',
                r'x²\s*([+-]\s*\d+\.?\d*)\s*x\s*([+-]\s*\d+\.?\d*)',  # Para casos como "x² - 5x + 6"
            ]

            for pattern in patterns:
                match = re.search(pattern, user_input)
                if match:
                    groups = match.groups()
                    if len(groups) == 3:
                        a_str, b_str, c_str = groups
                        # Processar coeficiente a
                        if not a_str or a_str.strip() in ['', '+']:
                            a = 1
                        elif a_str.strip() == '-':
                            a = -1
                        else:
                            a = float(a_str.replace(' ', ''))

                        # Processar coeficiente b
                        if not b_str or b_str.strip() in ['', '+']:
                            b = 1
                        elif b_str.strip() == '-':
                            b = -1
                        else:
                            b = float(b_str.replace(' ', ''))

                        # Processar coeficiente c
                        if not c_str or c_str.strip() in ['', '+']:
                            c = 1
                        elif c_str.strip() == '-':
                            c = -1
                        else:
                            c = float(c_str.replace(' ', ''))

                        coeffs = (a, b, c)
                        print(f"✅ Coeficientes extraídos manualmente: a={a}, b={b}, c={c}")
                        break

            if not coeffs:
                # Tentar padrão mais simples para casos como "x² - 5x + 6 = 0"
                if "x²" in user_input or "x^2" in user_input:
                    # Exemplo básico para teste
                    coeffs = (1, -5, 6)  # Padrão de exemplo
                    print(f"⚠️ Usando coeficientes de exemplo: a=1, b=-5, c=6")
                else:
                    return "❌ Não consegui identificar os coeficientes da equação. Tente um formato como 'x² - 5x + 6 = 0'", "{}"
        except Exception as e:
            print(f"❌ Erro na extração manual: {e}")
            return f"❌ Erro ao processar a equação: {str(e)}", "{}"
    else:
        print(f"✅ Coeficientes extraídos pelo Gemini: a={coeffs[0]}, b={coeffs[1]}, c={coeffs[2]}")

    a, b, c = coeffs

    # Resolver usando MCP
    try:
        print(f"🔢 Resolvendo equação: {a}x² + {b}x + {c} = 0")
        mcp_result = solve_quadratic(a, b, c)
        print(f"✅ Resultado calculado: {mcp_result['roots_type']}")

        # Gerar explicação com Gemini (se disponível)
        if gemini_server.gemini_model:
            explanation = gemini_server.explain_with_gemini(user_input, mcp_result)
        else:
            # Fallback: explicação básica
            explanation = f"""
# 📐 Solução da Equação Quadrática

## Equação: {a}x² + {b}x + {c} = 0

## Coeficientes:
- a = {a}
- b = {b}
- c = {c}

## Resultado:
{format_solution(mcp_result)}

## Análise:
- Tipo: {mcp_result['equation_type']}
- Raízes: {mcp_result['roots_type']}
- Discriminante: {mcp_result.get('discriminant', 'N/A')}
"""

        # Resultado JSON para exibição
        json_result = json.dumps(mcp_result, indent=2, ensure_ascii=False)

        return explanation, json_result

    except Exception as e:
        print(f"❌ Erro no cálculo: {e}")
        return f"❌ Erro no cálculo: {str(e)}", "{}"


def solve_quadratic_mcp_tool(a: str, b: str, c: str) -> str:
    """Ferramenta MCP para resolver equação quadrática."""
    try:
        a_val = float(a)
        b_val = float(b)
        c_val = float(c)
        
        result = solve_quadratic(a_val, b_val, c_val)
        
        # Converter complexos para string
        for i, root in enumerate(result['roots']):
            if isinstance(root, complex):
                result['roots'][i] = f"{root.real:.6f} + {root.imag:.6f}i"
        
        return json.dumps(result, indent=2, ensure_ascii=False)

    except Exception as e:
        return json.dumps({"error": str(e)}, indent=2, ensure_ascii=False)


def solve_with_advanced_graphics(user_input: str) -> Tuple[str, str, str]:
    """
    Resolve equação quadrática com gráficos avançados usando Gemini + MCP.

    Args:
        user_input: Entrada do usuário em linguagem natural

    Returns:
        Tuple com explicação, resultado JSON e gráfico avançado
    """
    # Primeiro resolver normalmente
    explanation, json_result = solve_with_gemini_mcp(user_input)

    # Tentar extrair coeficientes para gráfico avançado
    if not HAS_ADVANCED_PLOTTING:
        return explanation, json_result, "Gráficos avançados não disponíveis"

    try:
        # Tentar extrair coeficientes
        coeffs = gemini_server.extract_coefficients_with_gemini(user_input)

        if not coeffs:
            # Fallback para coeficientes de exemplo
            coeffs = (1, -5, 6)

        a, b, c = coeffs

        # Criar gráfico interativo
        fig = create_interactive_quadratic_plot(a, b, c)

        return explanation, json_result, fig

    except Exception as e:
        return explanation, json_result, f"Erro ao criar gráfico avançado: {str(e)}"


def create_methods_visualization(a: str, b: str, c: str) -> str:
    """Cria visualização dos métodos de resolução."""
    if not HAS_ADVANCED_PLOTTING:
        return "Gráficos avançados não disponíveis"

    try:
        a_val = float(a)
        b_val = float(b)
        c_val = float(c)

        fig = create_solution_method_visualization(a_val, b_val, c_val)
        return fig

    except Exception as e:
        return f"Erro ao criar visualização: {str(e)}"


def interactive_chat(message: str, history: str) -> Tuple[str, str]:
    """
    Função para chat interativo com Gemini.

    Args:
        message: Nova mensagem do usuário
        history: Histórico da conversa

    Returns:
        Tuple com (resposta, histórico_atualizado)
    """
    if not message.strip():
        return "Por favor, digite uma mensagem.", history

    try:
        # Enviar mensagem para o Gemini
        response = gemini_server.send_chat_message(message)

        # Obter histórico atualizado
        updated_history = gemini_server.get_conversation_history()

        return response, updated_history

    except Exception as e:
        return f"❌ Erro no chat: {str(e)}", history


def clear_chat_history() -> Tuple[str, str]:
    """Limpa o histórico do chat."""
    try:
        gemini_server.clear_conversation()
        return "", "Conversa limpa! Comece uma nova discussão."
    except Exception as e:
        return "", f"Erro ao limpar conversa: {str(e)}"


def suggest_math_topics() -> str:
    """Sugere tópicos matemáticos para discussão."""
    return """
## 💡 Sugestões de Tópicos para Discussão:

### 🔢 **Equações Quadráticas**
- "Como resolver x² - 5x + 6 = 0 passo a passo?"
- "Qual a diferença entre discriminante positivo e negativo?"
- "Como completar quadrados em 2x² - 8x + 6 = 0?"

### 📊 **Análise Gráfica**
- "Como o coeficiente 'a' afeta a forma da parábola?"
- "O que significa o vértice de uma parábola?"
- "Como encontrar onde a parábola cruza os eixos?"

### 🧮 **Métodos de Resolução**
- "Quando usar a fórmula de Bhaskara vs completar quadrados?"
- "Como verificar se minha solução está correta?"
- "Existe um método mais fácil para esta equação?"

### 🤔 **Conceitos Avançados**
- "O que são raízes complexas e quando aparecem?"
- "Como relacionar equações quadráticas com problemas reais?"
- "Qual a relação entre fatores e raízes?"

**Digite qualquer pergunta ou equação para começar!** 🚀
    """


def create_gemini_mcp_interface():
    """Cria interface Gradio integrada com Gemini e MCP em uma única aba."""
    with gr.Blocks(
        title="Gemini + MCP - Equações Quadráticas",
        theme=gr.themes.Soft(),
        css="""
        .gradio-container {
            max-width: 1400px !important;
        }
        .gemini-header {
            background: linear-gradient(135deg, #4285f4 0%, #34a853 50%, #fbbc05 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 10px 0;
            text-align: center;
        }
        .mcp-info {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }
        """
    ) as demo:
        gr.HTML("""
        <div class=\"gemini-header\">
            <h1>🤖 Gemini + MCP - Equações Quadráticas</h1>
            <p><strong>Inteligência Artificial + Precisão Matemática</strong></p>
            <p>Digite equações em linguagem natural e receba explicações didáticas completas!</p>
        </div>
        """)

        gr.Markdown("### 🎯 Análise Interativa de Equações Quadráticas com IA e Chat")
        gr.Markdown("**Combine gráficos avançados, explicações e chat interativo do Gemini!**")

        with gr.Row():
            with gr.Column(scale=2):
                main_input = gr.Textbox(
                    label="Digite sua equação ou pergunta",
                    placeholder="Ex: Analise x² - 5x + 6 = 0 ou Como o coeficiente 'a' afeta a parábola?",
                    lines=3
                )
                with gr.Row():
                    analyze_btn = gr.Button("🎯 Analisar com Gráficos e Gemini", variant="primary", scale=2)
                    chat_btn = gr.Button("💬 Enviar Mensagem para Gemini", variant="secondary", scale=2)
                    clear_all_btn = gr.Button("🗑️ Limpar Tudo", scale=1)
                gr.Markdown("#### 🧩 Exemplos Rápidos")
                with gr.Row():
                    ex1_btn = gr.Button("Analise x² - 5x + 6 = 0", size="sm")
                    ex2_btn = gr.Button("Compare x² vs 2x²", size="sm")
                with gr.Row():
                    ex3_btn = gr.Button("O que é discriminante?", size="sm")
                    ex4_btn = gr.Button("Como graficar parábolas?", size="sm")
                conversation_history = gr.Markdown(
                    value=suggest_math_topics(),
                    label="Conversa e Análises",
                    elem_id="conversation_area"
                )
            with gr.Column(scale=2):
                interactive_plot = gr.Plot(
                    label="Gráfico Interativo",
                    value=None
                )
                technical_data = gr.JSON(
                    label="Dados Técnicos",
                    show_label=True
                )
        current_gemini_response = gr.Markdown(
            label="Resposta Atual do Gemini",
            value="Digite uma equação ou pergunta acima para começar! 🤖"
        )
        status_display = gr.Markdown(
            value="**Status:** Pronto para análise ✅"
        )
        gr.HTML("""
        <div class=\"mcp-info\">
            <h3>📡 Configuração do Servidor</h3>
            <p><strong>URL MCP:</strong> <code>http://localhost:7861/gradio_api/mcp/sse</code></p>
            <p><strong>Status Gemini:</strong> """ + ("✅ Ativo" if gemini_server.gemini_model else "❌ Inativo") + """</p>
            <p><strong>Status Gráficos Avançados:</strong> """ + ("✅ Ativo" if HAS_ADVANCED_PLOTTING else "❌ Inativo") + """</p>
            <p><strong>Ferramentas Disponíveis:</strong></p>
            <ul>
                <li><code>solve_quadratic_mcp_tool</code> - Resolve equações quadráticas</li>
                <li><code>solve_with_gemini_mcp</code> - Resolve com explicação do Gemini</li>
                <li><code>solve_with_advanced_graphics</code> - Resolve com gráficos interativos</li>
                <li><code>create_methods_visualization</code> - Visualiza métodos de resolução</li>
                <li><code>interactive_chat</code> - Chat interativo com Gemini</li>
                <li><code>clear_chat_history</code> - Limpa histórico do chat</li>
            </ul>
        </div>
        """)

        # Lógica de callbacks
        def on_analyze_click(user_input, _):
            explanation, json_result, fig = solve_with_advanced_graphics(user_input)
            # Atualiza histórico e resposta
            if gemini_server.gemini_model:
                response, updated_history = interactive_chat(user_input, "")
            else:
                response, updated_history = explanation, ""
            return response, updated_history, fig, json_result, "**Status:** Análise concluída ✅"

        def on_chat_click(user_input, history):
            response, updated_history = interactive_chat(user_input, history)
            return response, updated_history, gr.update(), gr.update(), "**Status:** Mensagem enviada ao Gemini ✅"

        def on_clear_all():
            clear_chat_history()
            return "Digite uma equação ou pergunta acima para começar! 🤖", suggest_math_topics(), None, None, "**Status:** Pronto para análise ✅"

        analyze_btn.click(
            on_analyze_click,
            inputs=[main_input, conversation_history],
            outputs=[current_gemini_response, conversation_history, interactive_plot, technical_data, status_display]
        )
        chat_btn.click(
            on_chat_click,
            inputs=[main_input, conversation_history],
            outputs=[current_gemini_response, conversation_history, interactive_plot, technical_data, status_display]
        )
        clear_all_btn.click(
            on_clear_all,
            inputs=[],
            outputs=[current_gemini_response, conversation_history, interactive_plot, technical_data, status_display]
        )
        # Exemplos rápidos
        ex1_btn.click(lambda: "Analise x² - 5x + 6 = 0", None, main_input)
        ex2_btn.click(lambda: "Compare x² vs 2x²", None, main_input)
        ex3_btn.click(lambda: "O que é discriminante?", None, main_input)
        ex4_btn.click(lambda: "Como graficar parábolas?", None, main_input)
    return demo


def main():
    """Função principal."""
    print("🚀 Iniciando Servidor Gemini + MCP...")
    print("=" * 60)
    
    # Status das dependências
    print("📦 Status das Dependências:")
    print(f"   • python-dotenv: {'✅' if HAS_DOTENV else '❌'}")
    print(f"   • google-generativeai: {'✅' if HAS_GEMINI else '❌'}")
    print(f"   • quadratic_solver: {'✅' if HAS_QUADRATIC_SOLVER else '❌ (usando básico)'}")
    print(f"   • advanced_plotting: {'✅' if HAS_ADVANCED_PLOTTING else '❌'}")
    
    # Status do Gemini
    if gemini_server.gemini_model:
        print("   • Gemini Model: ✅ Configurado")
    else:
        print("   • Gemini Model: ❌ Não configurado")
        print("     💡 Verifique se GOOGLE_API_KEY está no arquivo .env")
    
    # Configurações do servidor
    host = os.getenv("MCP_SERVER_HOST", "0.0.0.0")
    port = int(os.getenv("MCP_SERVER_PORT", "7861"))
    
    print(f"\n📡 Configurações do Servidor:")
    print(f"   • Host: {host}")
    print(f"   • Porta: {port}")
    print(f"   • URL: http://localhost:{port}")
    print(f"   • MCP Endpoint: http://localhost:{port}/gradio_api/mcp/sse")
    
    # Criar e lançar interface
    demo = create_gemini_mcp_interface()
    
    print(f"\n🎯 Acesse a interface em: http://localhost:{port}")
    print("🤖 Digite equações em linguagem natural para usar o Gemini!")
    
    demo.launch(
        server_name=host,
        server_port=port,
        mcp_server=True,
        share=False,
        show_error=True,
        show_api=True
    )


if __name__ == "__main__":
    main()
