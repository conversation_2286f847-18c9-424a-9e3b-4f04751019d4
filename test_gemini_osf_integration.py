#!/usr/bin/env python3
"""
Teste da integração Gemini + OSF no servidor MCP
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from main import (
    osf_search_with_gemini_discussion,
    osf_analyze_with_gemini_discussion,
    osf_suggest_search_terms_gemini,
    osf_chat_with_gemini,
    gemini_osf_assistant
)

def test_gemini_availability():
    """Testa se Gemini está disponível"""
    print("🤖 Testando disponibilidade do Gemini...")
    
    if gemini_osf_assistant.gemini_model:
        print("✅ Gemini está disponível e configurado")
        return True
    else:
        print("❌ Gemini não está disponível")
        print("💡 Para habilitar Gemini:")
        print("   1. Instale: uv add google-generativeai")
        print("   2. Configure GEMINI_API_KEY no arquivo .env")
        print("   3. <PERSON><PERSON><PERSON> o servidor")
        return False

def test_search_suggestions():
    """Testa sugestões de termos de busca"""
    print("\n🔍 Testando sugestões de termos de busca...")
    
    research_area = "machine learning"
    suggestions = osf_suggest_search_terms_gemini(research_area)
    
    print(f"Área de pesquisa: {research_area}")
    print("Sugestões do Gemini:")
    print(suggestions)
    print("-" * 50)

def test_search_with_discussion():
    """Testa busca com discussão Gemini"""
    print("\n🔍 Testando busca com discussão Gemini...")
    
    query = "psychology"
    max_results = 3
    
    search_result, chat_history = osf_search_with_gemini_discussion(query, max_results)
    
    print("Resultado da busca:")
    print(search_result[:200] + "..." if len(search_result) > 200 else search_result)
    
    print("\nHistórico inicial do chat:")
    for chat in chat_history:
        print(f"Usuário: {chat[0][:50]}...")
        print(f"Gemini: {chat[1][:100]}...")
        print()
    
    print("-" * 50)

def test_chat_interaction():
    """Testa interação de chat"""
    print("\n💬 Testando chat interativo...")
    
    # Simular uma conversa
    chat_history = []
    
    # Primeira mensagem
    message1 = "Quais são os principais critérios para avaliar a qualidade de um dataset de pesquisa?"
    chat_history, _ = osf_chat_with_gemini(message1, chat_history, "general")
    
    print("Primeira interação:")
    if chat_history:
        print(f"Usuário: {chat_history[-1][0]}")
        print(f"Gemini: {chat_history[-1][1][:150]}...")
    
    # Segunda mensagem
    message2 = "Como identificar outliers em dados médicos?"
    chat_history, _ = osf_chat_with_gemini(message2, chat_history, "general")
    
    print("\nSegunda interação:")
    if len(chat_history) > 1:
        print(f"Usuário: {chat_history[-1][0]}")
        print(f"Gemini: {chat_history[-1][1][:150]}...")
    
    print(f"\nTotal de mensagens no histórico: {len(chat_history)}")
    print("-" * 50)

def test_analysis_discussion():
    """Testa discussão sobre análise de dados"""
    print("\n📊 Testando discussão sobre análise...")
    
    # Simular análise de dados
    download_dir = "test_mcp_downloads"  # Diretório que pode ter dados de testes anteriores
    
    if os.path.exists(download_dir):
        analysis_text, plot, chat_history = osf_analyze_with_gemini_discussion(download_dir)
        
        print("Resultado da análise:")
        print(analysis_text[:200] + "..." if len(analysis_text) > 200 else analysis_text)
        
        print(f"\nGráfico gerado: {'Sim' if plot else 'Não'}")
        
        print("\nHistórico inicial do chat:")
        for chat in chat_history:
            print(f"Usuário: {chat[0][:50]}...")
            print(f"Gemini: {chat[1][:100]}...")
            print()
    else:
        print(f"Diretório {download_dir} não encontrado - pulando teste de análise")
    
    print("-" * 50)

def main():
    """Função principal de teste"""
    print("TESTE DE INTEGRAÇÃO GEMINI + OSF")
    print("=" * 60)
    
    # Teste 1: Disponibilidade do Gemini
    gemini_available = test_gemini_availability()
    
    if not gemini_available:
        print("\n⚠️  Gemini não está disponível - alguns testes serão pulados")
        print("🌐 Servidor MCP ainda funciona sem Gemini!")
        print("📍 Acesse: http://localhost:7860")
        print("📂 Nova aba: '🔬 OSF.io Research Data'")
        return
    
    try:
        # Teste 2: Sugestões de busca
        test_search_suggestions()
        
        # Teste 3: Busca com discussão
        test_search_with_discussion()
        
        # Teste 4: Chat interativo
        test_chat_interaction()
        
        # Teste 5: Discussão sobre análise
        test_analysis_discussion()
        
        print("✅ TODOS OS TESTES GEMINI + OSF CONCLUÍDOS!")
        
    except Exception as e:
        print(f"❌ Erro durante os testes: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n🎉 INTEGRAÇÃO COMPLETA DISPONÍVEL!")
    print("🌐 Servidor MCP: http://localhost:7860")
    print("📂 Nova aba: '🔬 OSF.io Research Data + 🤖 Gemini'")
    print("💡 Funcionalidades:")
    print("   - Busca inteligente com sugestões Gemini")
    print("   - Download automático de dados")
    print("   - Análise com discussão interativa")
    print("   - Pipeline completo com chat IA")

if __name__ == "__main__":
    main()
