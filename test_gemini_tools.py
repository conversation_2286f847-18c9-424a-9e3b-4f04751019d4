#!/usr/bin/env python3
"""
Teste das ferramentas do Gemini com function calling.
"""

import sys
import time

# Adicionar o diretório atual ao path
sys.path.append('.')

def test_gemini_tools():
    """Testa se o Gemini pode chamar as ferramentas."""
    print("🧪 Testando Ferramentas do Gemini")
    print("=" * 50)
    
    try:
        from gemini_mcp_server import gemini_server
        
        if not gemini_server.gemini_model:
            print("❌ Gemini não está configurado")
            return False
        
        # Testar se o modelo tem ferramentas
        print("🔧 Verificando configuração do modelo...")
        print(f"Modelo: {type(gemini_server.gemini_model)}")
        
        # Testar chat simples primeiro
        print("\n💬 Teste 1: Chat simples")
        response = gemini_server.send_chat_message("Olá! Você pode me ajudar com pesquisa científica?")
        print(f"Resposta: {response[:200]}...")
        
        # Testar solicitação de busca
        print("\n🔍 Teste 2: Solicitação de busca OSF")
        response = gemini_server.send_chat_message("Por favor, busque projetos sobre machine learning no OSF.io")
        print(f"Resposta: {response[:300]}...")
        
        # Testar solicitação de download
        print("\n📥 Teste 3: Solicitação de download")
        response = gemini_server.send_chat_message("Faça download dos arquivos do projeto https://osf.io/j4bv6/")
        print(f"Resposta: {response[:300]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro no teste: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_function_execution():
    """Testa a execução direta das funções."""
    print("\n🔧 Testando execução direta das funções")
    print("=" * 50)
    
    try:
        from gemini_mcp_server import gemini_server
        
        # Testar função de busca
        print("🔍 Testando função search_osf...")
        result = gemini_server.execute_function("search_osf", {"query": "machine learning", "max_results": 3})
        print(f"✅ Resultado: {len(result)} caracteres")
        print(result[:200] + "..." if len(result) > 200 else result)
        
        # Testar função de download
        print("\n📥 Testando função download_osf_files...")
        result = gemini_server.execute_function("download_osf_files", {"project_url": "https://osf.io/j4bv6/"})
        print(f"✅ Resultado: {len(result)} caracteres")
        print(result[:200] + "..." if len(result) > 200 else result)
        
        return True
        
    except Exception as e:
        print(f"❌ Erro no teste: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Função principal."""
    print("🚀 TESTE DAS FERRAMENTAS GEMINI + MCP")
    print("=" * 60)
    
    # Aguardar servidor inicializar
    print("⏳ Aguardando servidor inicializar...")
    time.sleep(3)
    
    # Testar ferramentas do Gemini
    success1 = test_gemini_tools()
    
    # Testar execução direta
    success2 = test_function_execution()
    
    # Resumo
    print("\n" + "="*60)
    print("📊 RESUMO DOS TESTES")
    print("="*60)
    
    print(f"Ferramentas Gemini:     {'✅ PASSOU' if success1 else '❌ FALHOU'}")
    print(f"Execução Direta:        {'✅ PASSOU' if success2 else '❌ FALHOU'}")
    
    if success1 and success2:
        print("\n🎉 Todos os testes passaram!")
        print("\n💡 Como usar na interface:")
        print("1. Acesse: http://localhost:7861")
        print("2. No chat, digite: 'Busque projetos sobre machine learning'")
        print("3. O Gemini deve chamar automaticamente a função search_osf")
        print("4. Você verá logs no terminal do servidor mostrando a execução")
    else:
        print("\n⚠️ Alguns testes falharam.")
        print("Verifique os erros acima e a configuração do Gemini.")
    
    print("\n📋 Comandos para testar na interface:")
    print("- 'Busque projetos sobre machine learning'")
    print("- 'Faça download de https://osf.io/j4bv6/'")
    print("- 'Analise os dados baixados'")
    print("- 'Crie um gráfico dos dados'")

if __name__ == "__main__":
    main()
