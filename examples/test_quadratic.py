#!/usr/bin/env python3
"""
Exemplos de uso do resolvedor de equações quadráticas.

Este script demonstra como usar o módulo quadratic_solver
para resolver diferentes tipos de equações.
"""

import sys
import os

# Adicionar o diretório src ao path para importar o módulo
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from quadratic_solver import solve_quadratic, format_solution, plot_quadratic, EXAMPLES


def test_all_examples():
    """Testa todos os exemplos pré-definidos."""
    print("🧮 Testando Resolvedor de Equações Quadráticas")
    print("=" * 60)
    
    for name, (a, b, c) in EXAMPLES.items():
        print(f"\n📐 Teste: {name.replace('_', ' ').title()}")
        print(f"Equação: {a}x² + {b}x + {c} = 0")
        print("-" * 40)
        
        # Resolver a equação
        solution = solve_quadratic(a, b, c)
        
        # Exibir resultado formatado
        result_text = format_solution(solution)
        print(result_text)
        
        # Informações adicionais do dicionário
        print("📊 Informações Técnicas:")
        print(f"   • Tipo de equação: {solution['equation_type']}")
        print(f"   • Tipo de raízes: {solution['roots_type']}")
        print(f"   • Possui raízes reais: {solution['has_real_roots']}")
        
        if solution['vertex']:
            vx, vy = solution['vertex']
            print(f"   • Vértice: ({vx:.3f}, {vy:.3f})")
        
        if solution['discriminant'] is not None:
            print(f"   • Discriminante: {solution['discriminant']:.3f}")
        
        print("\n" + "="*60)


def test_custom_equation():
    """Testa uma equação personalizada."""
    print("\n🎯 Teste com Equação Personalizada")
    print("=" * 40)
    
    # Equação: 2x² - 8x + 6 = 0
    a, b, c = 2, -8, 6
    print(f"Equação: {a}x² + {b}x + {c} = 0")
    
    solution = solve_quadratic(a, b, c)
    result_text = format_solution(solution)
    print(result_text)
    
    # Verificar as raízes manualmente
    if solution['has_real_roots'] and len(solution['roots']) == 2:
        x1, x2 = solution['roots']
        print("🔍 Verificação das raízes:")
        
        # Substituir x1 na equação
        result1 = a * x1**2 + b * x1 + c
        print(f"   f({x1:.3f}) = {result1:.6f} ≈ 0")
        
        # Substituir x2 na equação
        result2 = a * x2**2 + b * x2 + c
        print(f"   f({x2:.3f}) = {result2:.6f} ≈ 0")


def interactive_solver():
    """Resolvedor interativo."""
    print("\n🎮 Resolvedor Interativo")
    print("=" * 30)
    print("Digite os coeficientes da equação ax² + bx + c = 0")
    print("(Digite 'q' para sair)")
    
    while True:
        try:
            print("\n" + "-" * 30)
            a_input = input("Coeficiente a (x²): ")
            if a_input.lower() == 'q':
                break
            
            b_input = input("Coeficiente b (x): ")
            if b_input.lower() == 'q':
                break
                
            c_input = input("Coeficiente c (constante): ")
            if c_input.lower() == 'q':
                break
            
            # Converter para float
            a = float(a_input)
            b = float(b_input)
            c = float(c_input)
            
            print(f"\n📐 Resolvendo: {a}x² + {b}x + {c} = 0")
            print("-" * 40)
            
            # Resolver
            solution = solve_quadratic(a, b, c)
            result_text = format_solution(solution)
            print(result_text)
            
        except ValueError:
            print("❌ Erro: Digite apenas números válidos!")
        except KeyboardInterrupt:
            print("\n👋 Saindo...")
            break
        except Exception as e:
            print(f"❌ Erro inesperado: {e}")


def main():
    """Função principal."""
    print("🚀 Demonstração do Resolvedor de Equações Quadráticas")
    print("=" * 60)
    
    # Testar todos os exemplos
    test_all_examples()
    
    # Testar equação personalizada
    test_custom_equation()
    
    # Perguntar se quer usar o modo interativo
    print("\n" + "="*60)
    choice = input("Deseja usar o resolvedor interativo? (s/n): ").lower()
    
    if choice in ['s', 'sim', 'y', 'yes']:
        interactive_solver()
    
    print("\n✅ Demonstração concluída!")
    print("💡 Para usar a interface web, execute: python src/main.py")


if __name__ == "__main__":
    main()
