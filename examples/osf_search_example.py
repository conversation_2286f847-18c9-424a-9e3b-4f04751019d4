"""
Exemplo de uso do OSF Scraper

Este exemplo demonstra como usar a função de web scraping do OSF.io
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

from osf_scraper import search_osf, OSFScraper


def exemplo_busca_simples():
    """Exemplo 1: Busca simples usando a função conveniente"""
    print("Exemplo 1: Busca simples por 'machine learning'")
    print("-" * 50)

    try:
        # Busca usando a função conveniente
        results = search_osf("machine learning", max_results=3, use_selenium=True)

        if results:
            for i, result in enumerate(results, 1):
                print(f"\n{i}. {result.get('title', 'Sem título')}")
                print(f"   URL: {result.get('url', 'N/A')}")
                print(f"   Autores: {result.get('authors', 'N/A')}")
                if result.get('description'):
                    print(f"   Descrição: {result['description'][:100]}...")
        else:
            print("Nenhum resultado encontrado.")

    except Exception as e:
        print(f"Erro durante a busca: {e}")


def exemplo_busca_avancada():
    """Exemplo 2: Busca usando a classe OSFScraper diretamente"""
    print("\n\nExemplo 2: Busca avançada por 'psychology research'")
    print("-" * 55)

    try:
        # Criar instância do scraper
        scraper = OSFScraper(timeout=15, use_selenium=True)

        # Realizar busca
        results = scraper.search("psychology research", max_results=5)

        print(f"Total de resultados encontrados: {len(results)}")

        if results:
            for i, result in enumerate(results, 1):
                title = result.get('title', 'Sem título')
                url = result.get('url', 'N/A')
                authors = result.get('authors', 'N/A')

                print(f"\n{i}. {title[:60]}{'...' if len(title) > 60 else ''}")
                print(f"   URL: {url}")
                print(f"   Autores: {authors}")
        else:
            print("Nenhum resultado encontrado.")

    except Exception as e:
        print(f"Erro durante a busca avançada: {e}")


def exemplo_busca_sem_selenium():
    """Exemplo 3: Busca apenas com requests (sem Selenium)"""
    print("\n\nExemplo 3: Busca sem Selenium (apenas requests)")
    print("-" * 50)

    try:
        # Busca sem usar Selenium
        results = search_osf("data science", max_results=3, use_selenium=False)

        print(f"Resultados encontrados: {len(results)}")

        if results:
            for i, result in enumerate(results, 1):
                print(f"{i}. {result.get('title', 'Sem título')}")
                print(f"   {result.get('url', 'N/A')}")
        else:
            print("Nenhum resultado encontrado (esperado, pois OSF.io requer JavaScript).")

    except Exception as e:
        print(f"Erro: {e}")


def main():
    """Função principal que executa todos os exemplos"""
    print("=== Exemplos de Uso do OSF Scraper ===\n")

    # Executar exemplos
    exemplo_busca_simples()
    exemplo_busca_avancada()
    exemplo_busca_sem_selenium()

    print("\n" + "="*60)
    print("Exemplos concluídos!")
    print("\nNota: O OSF.io requer JavaScript, então os melhores resultados")
    print("são obtidos com use_selenium=True (padrão).")


if __name__ == "__main__":
    main()
