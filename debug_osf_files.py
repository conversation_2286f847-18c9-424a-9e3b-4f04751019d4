#!/usr/bin/env python3
"""
Debug script para investigar a estrutura da aba Files do OSF.io
"""

import sys
import os
import time
from bs4 import BeautifulSoup

# Adicionar src ao path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def debug_files_page():
    """Debug da página de arquivos de um projeto OSF"""
    print("=== Debug: Página de Files do OSF.io ===")
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.chrome.service import Service
        from webdriver_manager.chrome import ChromeDriverManager
        from selenium.webdriver.common.by import By
        from selenium.webdriver.support.ui import WebDriverWait
        from selenium.webdriver.support import expected_conditions as EC
        
        # Configurar Chrome
        chrome_options = Options()
        chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)
        
        try:
            # URL de exemplo de um projeto OSF
            project_url = "https://osf.io/j4bv6/"
            files_url = project_url + "files/"
            
            print(f"Acessando projeto: {project_url}")
            driver.get(project_url)
            time.sleep(3)
            
            print(f"Título da página: {driver.title}")
            
            # Tentar acessar a aba Files
            print(f"\nAcessando aba Files: {files_url}")
            driver.get(files_url)
            time.sleep(5)
            
            print(f"Título da página Files: {driver.title}")
            print(f"URL atual: {driver.current_url}")
            
            # Analisar o HTML da página
            soup = BeautifulSoup(driver.page_source, 'html.parser')
            
            # Procurar por elementos que podem conter arquivos
            print("\n=== Procurando elementos de arquivos ===")
            
            # Seletores possíveis para arquivos
            file_selectors = [
                '[data-test-file]',
                '.file-item',
                '.file-row',
                '.file-browser-item',
                '[class*="file"]',
                'a[href*="download"]',
                'a[href*="/file/"]',
                '.tree-item',
                '.file-tree-item'
            ]
            
            for selector in file_selectors:
                elements = soup.select(selector)
                if elements:
                    print(f"✓ Encontrados {len(elements)} elementos com seletor: {selector}")
                    # Mostrar o primeiro elemento
                    if elements:
                        first_elem = elements[0]
                        print(f"  Primeiro elemento: {first_elem.name}, classes: {first_elem.get('class', [])}")
                        text = first_elem.get_text(strip=True)[:100]
                        print(f"  Texto: {text}...")
                        
                        # Verificar se tem links de download
                        links = first_elem.find_all('a', href=True)
                        for link in links[:2]:
                            href = link.get('href')
                            if 'download' in href or '/file/' in href:
                                print(f"  Link de download: {href}")
                else:
                    print(f"✗ Nenhum elemento com seletor: {selector}")
            
            # Procurar por todos os links na página
            print("\n=== Analisando todos os links ===")
            all_links = soup.find_all('a', href=True)
            download_links = []
            
            for link in all_links:
                href = link.get('href', '')
                text = link.get_text(strip=True)
                
                # Procurar por padrões de download
                if any(pattern in href.lower() for pattern in ['download', '/file/', '/files/', 'attachment']):
                    download_links.append({
                        'href': href,
                        'text': text,
                        'full_url': href if href.startswith('http') else f"https://osf.io{href}"
                    })
            
            print(f"Links de download encontrados: {len(download_links)}")
            for i, link in enumerate(download_links[:5]):  # Mostrar apenas os primeiros 5
                print(f"  {i+1}. {link['text'][:50]} -> {link['href']}")
            
            # Procurar por estrutura de árvore de arquivos
            print("\n=== Procurando estrutura de árvore ===")
            tree_selectors = [
                '.file-tree',
                '.tree-view',
                '[class*="tree"]',
                '.file-browser',
                '.file-list'
            ]
            
            for selector in tree_selectors:
                elements = soup.select(selector)
                if elements:
                    print(f"✓ Estrutura encontrada com seletor: {selector}")
                    elem = elements[0]
                    print(f"  Classes: {elem.get('class', [])}")
                    
                    # Procurar por itens dentro da estrutura
                    items = elem.find_all(['li', 'div', 'tr'])
                    print(f"  Itens dentro da estrutura: {len(items)}")
                    
                    for i, item in enumerate(items[:3]):
                        item_text = item.get_text(strip=True)[:50]
                        print(f"    {i+1}. {item_text}...")
            
            # Verificar se há JavaScript que carrega arquivos dinamicamente
            print("\n=== Verificando carregamento dinâmico ===")
            
            # Aguardar um pouco mais para JavaScript carregar
            time.sleep(5)
            
            # Analisar novamente após aguardar
            soup_after = BeautifulSoup(driver.page_source, 'html.parser')
            
            # Comparar número de elementos antes e depois
            links_before = len(soup.find_all('a', href=True))
            links_after = len(soup_after.find_all('a', href=True))
            
            print(f"Links antes da espera: {links_before}")
            print(f"Links depois da espera: {links_after}")
            
            if links_after > links_before:
                print("✓ Conteúdo carregado dinamicamente detectado")
            else:
                print("✗ Nenhum carregamento dinâmico detectado")
            
        finally:
            driver.quit()
            
    except Exception as e:
        print(f"Erro durante debug: {e}")

def main():
    """Função principal de debug"""
    print("OSF.io Files Debug Script")
    print("=" * 50)
    
    debug_files_page()
    
    print("\n" + "=" * 50)
    print("Debug concluído!")

if __name__ == "__main__":
    main()
