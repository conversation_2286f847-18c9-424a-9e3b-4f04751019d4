# 🤖 Servidor MCP Integrado: Gemini + OSF.io

Um servidor **Model Context Protocol (MCP)** integrado que combina o poder do Google Gemini AI com funcionalidades de busca e análise de dados científicos do OSF.io (Open Science Framework).

## 🚀 Características Principais

### 🔍 **Busca Inteligente OSF**
- Busca projetos científicos no OSF.io usando linguagem natural
- Análise automática dos resultados com Gemini AI
- Sugestões de projetos relevantes para análise

### 📥 **Download Automatizado**
- Download de arquivos de projetos OSF
- Análise automática dos tipos de dados disponíveis
- Sugestões de análises apropriadas

### 📊 **Análise de Dados com IA**
- Análise automática de arquivos CSV e Excel
- Estatísticas descritivas e identificação de padrões
- Detecção de problemas nos dados (valores ausentes, outliers)

### 📈 **Visualizações Inteligentes**
- Criação automática de gráficos apropriados
- Histogramas, matrizes de correlação, scatter plots
- Interpretação dos gráficos com Gemini AI

### 💬 **Chat Interativo**
- Discussão científica com Gemini AI
- Interpretação de resultados
- Sugestões metodológicas

## 🛠️ Instalação

### Dependências Necessárias

```bash
# Instalar dependências principais
uv add gradio google-generativeai python-dotenv
uv add numpy pandas matplotlib seaborn
uv add requests beautifulsoup4 selenium webdriver-manager
```

### Configuração da API

1. **Obter chave do Google Gemini:**
   - Acesse: https://makersuite.google.com/app/apikey
   - Crie uma nova chave de API

2. **Configurar arquivo .env:**
```bash
# Criar arquivo .env na raiz do projeto
echo "GOOGLE_API_KEY=sua_chave_aqui" > .env
```

## 🚀 Uso

### Iniciar o Servidor

```bash
python gemini_mcp_server.py
```

O servidor estará disponível em:
- **Interface Web:** http://localhost:7861
- **Endpoint MCP:** http://localhost:7861/gradio_api/mcp/sse

### Comandos Disponíveis

#### 🔍 **Buscar Projetos**
```
Buscar projetos sobre machine learning
Procurar dados de psicologia experimental
Encontrar estudos sobre neurociência
```

#### 📥 **Download de Dados**
```
Download https://osf.io/j4bv6/
Baixar arquivos do projeto https://osf.io/2zfu4/
```

#### 📊 **Análise de Dados**
```
Analisar os dados baixados
Criar estatísticas descritivas
Verificar qualidade dos dados
```

#### 📈 **Visualizações**
```
Criar histograma dos dados
Gerar matriz de correlação
Fazer scatter plot das variáveis
```

#### 💬 **Chat Científico**
```
Como interpretar estes resultados?
Que análises estatísticas são apropriadas?
Como melhorar a qualidade dos dados?
```

## 🔧 Ferramentas MCP Disponíveis

### Para LLMs (Claude, Cursor, etc.)

1. **`search_osf_mcp_tool`** - Busca projetos no OSF.io
2. **`download_osf_mcp_tool`** - Download de arquivos OSF
3. **`analyze_data_mcp_tool`** - Análise de dados científicos
4. **`create_plot_mcp_tool`** - Criação de visualizações
5. **`process_user_request`** - Processamento inteligente de solicitações
6. **`interactive_chat`** - Chat interativo com Gemini

### Configuração para Claude Desktop

```json
{
  "mcpServers": {
    "gemini-osf-research": {
      "url": "http://localhost:7861/gradio_api/mcp/sse"
    }
  }
}
```

## 📋 Exemplos de Uso

### Exemplo 1: Workflow Completo

1. **Buscar:** "Buscar projetos sobre machine learning"
2. **Selecionar:** Escolher projeto interessante dos resultados
3. **Download:** "Download https://osf.io/j4bv6/"
4. **Analisar:** "Analisar dados baixados"
5. **Visualizar:** "Criar gráfico dos dados"
6. **Discutir:** "Como interpretar estes resultados?"

### Exemplo 2: Projetos de Teste

- **Machine Learning ICH:** https://osf.io/j4bv6/
- **Moral Algorithms:** https://osf.io/2zfu4/

## 🎯 Interface Unificada

A interface combina todas as funcionalidades em uma única aba:

- **Campo de entrada:** Para comandos em linguagem natural
- **Botões de ação:** Processar com gráficos ou chat simples
- **Área de conversa:** Histórico e análises do Gemini
- **Gráficos interativos:** Visualizações automáticas
- **Dados técnicos:** Informações estruturadas em JSON

## 🔍 Status das Dependências

O servidor verifica automaticamente:
- ✅ Google Gemini AI configurado
- ✅ OSF Scraper disponível
- ✅ Bibliotecas de análise de dados
- ⚠️ Selenium (opcional, para busca avançada)

## 🚨 Solução de Problemas

### Problema: Gemini não configurado
```
❌ Gemini Model: Não configurado
💡 Verifique se GOOGLE_API_KEY está no arquivo .env
```

### Problema: OSF Scraper não funciona
```
⚠️ Selenium não disponível. Apenas busca básica será possível.
```
**Solução:** `uv add selenium webdriver-manager`

### Problema: Erro na análise de dados
- Verifique se o arquivo é CSV ou Excel válido
- Confirme que os dados foram baixados corretamente

## 🎉 Recursos Avançados

- **Detecção automática de intenção** usando Gemini AI
- **Análise contextual** de dados científicos
- **Sugestões inteligentes** de visualizações
- **Interpretação automática** de resultados
- **Workflow científico completo** integrado

---

**Desenvolvido para pesquisadores que querem combinar IA com análise de dados científicos!** 🧪🤖
