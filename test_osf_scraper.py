"""
Teste do OSF Scraper

Script para testar a funcionalidade de web scraping do OSF.io
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from osf_scraper import search_osf, OSFScraper


def test_basic_search():
    """Teste básico de busca"""
    print("=== Teste Básico de Busca ===")
    
    query = "python"
    print(f"Buscando por: '{query}'")
    
    try:
        results = search_osf(query, max_results=3, headless=True)
        
        print(f"\nResultados encontrados: {len(results)}")
        
        for i, result in enumerate(results, 1):
            print(f"\n--- Resultado {i} ---")
            for key, value in result.items():
                print(f"{key.capitalize()}: {value}")
                
    except Exception as e:
        print(f"Erro durante a busca: {e}")


def test_advanced_search():
    """Teste com busca mais específica"""
    print("\n\n=== Teste de Busca Avançada ===")
    
    query = "machine learning psychology"
    print(f"Buscando por: '{query}'")
    
    try:
        scraper = OSFScraper(headless=True, timeout=15)
        results = scraper.search(query, max_results=5)
        
        print(f"\nResultados encontrados: {len(results)}")
        
        # Mostrar apenas títulos e URLs para economizar espaço
        for i, result in enumerate(results, 1):
            title = result.get('title', 'Sem título')
            url = result.get('url', 'N/A')
            authors = result.get('authors', 'N/A')
            
            print(f"\n{i}. {title}")
            print(f"   URL: {url}")
            print(f"   Autores: {authors}")
            
    except Exception as e:
        print(f"Erro durante a busca avançada: {e}")


def test_interactive_search():
    """Teste interativo onde o usuário pode inserir termos de busca"""
    print("\n\n=== Teste Interativo ===")
    
    while True:
        query = input("\nDigite um termo de busca (ou 'quit' para sair): ").strip()
        
        if query.lower() in ['quit', 'exit', 'sair']:
            break
            
        if not query:
            print("Por favor, digite um termo de busca válido.")
            continue
            
        try:
            print(f"Buscando por: '{query}'...")
            results = search_osf(query, max_results=3, headless=True)
            
            if results:
                print(f"\nEncontrados {len(results)} resultados:")
                for i, result in enumerate(results, 1):
                    print(f"\n{i}. {result.get('title', 'Sem título')}")
                    print(f"   URL: {result.get('url', 'N/A')}")
                    if result.get('authors'):
                        print(f"   Autores: {result['authors']}")
            else:
                print("Nenhum resultado encontrado.")
                
        except Exception as e:
            print(f"Erro durante a busca: {e}")


if __name__ == "__main__":
    print("Testando OSF Scraper")
    print("=" * 50)
    
    # Executar testes
    test_basic_search()
    test_advanced_search()
    
    # Perguntar se quer teste interativo
    response = input("\nDeseja executar o teste interativo? (s/n): ").strip().lower()
    if response in ['s', 'sim', 'y', 'yes']:
        test_interactive_search()
    
    print("\nTestes concluídos!")
