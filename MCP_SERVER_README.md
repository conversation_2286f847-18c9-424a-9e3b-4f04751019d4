# 🔧 Servidor MCP - Equações Quadráticas

Um servidor **Model Context Protocol (MCP)** usando Gradio que expõe ferramentas matemáticas para resolução de equações quadráticas. Este servidor permite que LLMs como Claude, <PERSON>ursor ou Cline acessem funcionalidades avançadas de matemática.

## 🚀 Características

### 🛠️ Ferramentas Disponíveis

1. **`solve_quadratic_equation_mcp`**
   - Resolve equações quadráticas ax² + bx + c = 0
   - Retorna análise completa em JSON
   - Suporta raízes reais e complexas

2. **`analyze_quadratic_function`**
   - Ana<PERSON>a propriedades da função quadrática
   - Domínio, imagem, vértice, crescimento/decrescimento
   - Interceptações com eixos x e y

3. **`find_quadratic_from_roots`**
   - Constrói equação quadrática a partir das raízes
   - Usa fórmulas de Vieta para verificação
   - Forma padrão e fatorada

## 📦 Instalação

### Pré-requisitos

```bash
# Instalar Gradio com suporte MCP
pip install "gradio[mcp]"

# Ou instalar dependências individuais
pip install gradio numpy matplotlib
```

### Dependências do Projeto

Certifique-se de que o módulo `src/quadratic_solver.py` está disponível:

```bash
# Estrutura necessária
projeto/
├── src/
│   ├── __init__.py
│   └── quadratic_solver.py
├── mcp_server.py
└── MCP_SERVER_README.md
```

## 🎯 Como Usar

### 1. Iniciar o Servidor

```bash
python mcp_server.py
```

O servidor estará disponível em:
- **Interface Web**: http://localhost:7860
- **Endpoint MCP**: http://localhost:7860/gradio_api/mcp/sse
- **Schema MCP**: http://localhost:7860/gradio_api/mcp/schema

### 2. Configurar Cliente MCP

#### Claude Desktop

Edite o arquivo de configuração:
- **macOS**: `~/Library/Application Support/Claude/claude_desktop_config.json`
- **Windows**: `%APPDATA%\Claude\claude_desktop_config.json`

```json
{
  "mcpServers": {
    "quadratic-solver": {
      "url": "http://localhost:7860/gradio_api/mcp/sse"
    }
  }
}
```

#### Cursor/Cline

Adicione a configuração MCP nas configurações do editor:

```json
{
  "mcp": {
    "servers": {
      "quadratic-solver": {
        "url": "http://localhost:7860/gradio_api/mcp/sse"
      }
    }
  }
}
```

### 3. Reiniciar o Cliente

Após adicionar a configuração, reinicie seu cliente MCP (Claude Desktop, Cursor, etc.).

## 🧪 Exemplos de Uso

### Resolver Equação Quadrática

**Prompt para o LLM:**
```
Resolva a equação x² - 5x + 6 = 0
```

**Resposta esperada:**
O LLM usará a ferramenta `solve_quadratic_equation_mcp` e retornará:
- Tipo de equação e raízes
- Discriminante e análise
- Vértice e propriedades
- Forma fatorada

### Analisar Função

**Prompt para o LLM:**
```
Analise a função f(x) = 2x² - 4x + 1
```

**Resposta esperada:**
O LLM usará `analyze_quadratic_function` e fornecerá:
- Domínio e imagem
- Vértice e eixo de simetria
- Intervalos de crescimento/decrescimento
- Interceptações

### Construir Equação

**Prompt para o LLM:**
```
Encontre uma equação quadrática com raízes 2 e 3
```

**Resposta esperada:**
O LLM usará `find_quadratic_from_roots` e retornará:
- Equação na forma padrão
- Forma fatorada
- Verificação com fórmulas de Vieta

## 🔧 Estrutura das Ferramentas

### Entrada (Todas as ferramentas usam strings)

```python
# Exemplo de entrada
a = "1"    # Coeficiente de x²
b = "-5"   # Coeficiente de x
c = "6"    # Termo independente
```

### Saída (JSON estruturado)

```json
{
  "equation": "1.0x² + -5.0x + 6.0 = 0",
  "equation_type": "quadratic",
  "roots_type": "duas raízes reais distintas",
  "has_real_roots": true,
  "roots": [3.0, 2.0],
  "analysis": {
    "discriminant": 1.0,
    "vertex": {"x": 2.5, "y": -0.25},
    "axis_of_symmetry": 2.5,
    "y_intercept": 6.0,
    "concavity": "para cima",
    "factored_form": "1.0(x - 3.000)(x - 2.000)"
  },
  "formatted_solution": "📐 Equação Quadrática: 1.0x² + -5.0x + 6.0 = 0..."
}
```

## 🛡️ Tratamento de Erros

O servidor trata automaticamente:
- **Entradas inválidas**: Coeficientes não numéricos
- **Casos especiais**: Equações lineares (a=0)
- **Erros internos**: Problemas de cálculo

Exemplo de resposta de erro:
```json
{
  "error": "Erro de entrada",
  "message": "Os coeficientes devem ser números válidos: invalid literal for float()",
  "equation": "ax² + bx + c = 0"
}
```

## 🔍 Depuração

### Verificar Schema MCP

Visite: http://localhost:7860/gradio_api/mcp/schema

### Testar Ferramentas

Use a interface web em: http://localhost:7860

### Logs do Servidor

O servidor imprime informações detalhadas no console:
```
🚀 Iniciando Servidor MCP - Equações Quadráticas...
✅ Módulo quadratic_solver carregado com sucesso
📡 URL: http://localhost:7860/gradio_api/mcp/sse
🔧 Ferramentas Disponíveis: solve_quadratic_equation_mcp, analyze_quadratic_function, find_quadratic_from_roots
```

## 🚨 Solução de Problemas

### Problema: Módulo não encontrado
```bash
❌ Erro ao importar módulo: No module named 'src.quadratic_solver'
```
**Solução**: Certifique-se de que `src/quadratic_solver.py` existe e `src/__init__.py` está presente.

### Problema: Cliente MCP não conecta
**Soluções**:
1. Verificar se o servidor está rodando
2. Confirmar URL correta na configuração
3. Reiniciar o cliente MCP
4. Verificar firewall/proxy

### Problema: Ferramentas não aparecem
**Soluções**:
1. Verificar docstrings das funções
2. Confirmar type hints nos parâmetros
3. Reiniciar servidor e cliente

## 📚 Recursos Adicionais

- [Documentação MCP](https://modelcontextprotocol.io/)
- [Gradio MCP Guide](https://www.gradio.app/guides/building-mcp-server-with-gradio)
- [Claude Desktop](https://claude.ai/download)
- [MCP Inspector](https://github.com/modelcontextprotocol/inspector)

## 🤝 Contribuição

Para adicionar novas ferramentas:

1. Crie uma função com docstring detalhada
2. Use type hints `str` para parâmetros
3. Retorne JSON estruturado
4. Adicione à interface Gradio
5. Teste via interface web

## 📄 Licença

Este projeto segue a mesma licença do projeto principal.
