#!/usr/bin/env python3
"""
Teste da verificação de arquivos nos projetos OSF.
"""

import sys
import time

# Adicionar o diretório atual ao path
sys.path.append('.')

def test_files_verification():
    """Testa se a busca só retorna projetos com arquivos."""
    print("📁 Testando Verificação de Arquivos")
    print("=" * 50)
    
    try:
        from gemini_mcp_server import gemini_server
        
        # Testes específicos
        tests = [
            {
                "query": "marcha humana",
                "should_find": True,
                "description": "Busca em português deve encontrar projetos de biomecânica"
            },
            {
                "query": "human gait",
                "should_find": True,
                "description": "Busca em inglês deve encontrar projetos de marcha"
            },
            {
                "query": "machine learning",
                "should_find": True,
                "description": "Busca de ML deve encontrar projetos de IA"
            },
            {
                "query": "psychology",
                "should_find": True,
                "description": "Busca de psicologia deve encontrar projetos relevantes"
            }
        ]
        
        for test in tests:
            query = test["query"]
            should_find = test["should_find"]
            description = test["description"]
            
            print(f"\n🔍 Teste: '{query}'")
            print(f"📝 {description}")
            
            # Fazer busca
            result = gemini_server.search_osf_with_gemini(query, 3)
            
            # Verificar se encontrou projetos
            if hasattr(gemini_server, 'current_search_results') and gemini_server.current_search_results:
                projects = gemini_server.current_search_results
                print(f"✅ Encontrou {len(projects)} projetos:")
                
                # Verificar se todos têm arquivos
                projects_with_files = 0
                for i, proj in enumerate(projects, 1):
                    title = proj.get('title', 'Sem título')
                    has_files = proj.get('has_files', False)
                    file_types = proj.get('file_types', [])
                    
                    print(f"   {i}. {title[:50]}...")
                    if has_files:
                        print(f"      ✅ Tem arquivos: {', '.join(file_types) if file_types else 'Sim'}")
                        projects_with_files += 1
                    else:
                        print(f"      ❌ Sem arquivos confirmados")
                
                print(f"📊 {projects_with_files}/{len(projects)} projetos têm arquivos confirmados")
                
                if projects_with_files == len(projects):
                    print("✅ Todos os projetos têm arquivos - PERFEITO!")
                elif projects_with_files > 0:
                    print("⚠️ Alguns projetos têm arquivos - BOM")
                else:
                    print("❌ Nenhum projeto tem arquivos confirmados - PROBLEMA")
                    
            else:
                if should_find:
                    print("❌ Nenhum projeto encontrado (deveria ter encontrado)")
                else:
                    print("✅ Nenhum projeto encontrado (correto)")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_download_from_search():
    """Testa download usando resultado de busca anterior."""
    print("\n📥 Testando Download de Resultado de Busca")
    print("=" * 50)
    
    try:
        from gemini_mcp_server import process_user_request
        
        # Primeiro fazer uma busca
        print("🔍 Fazendo busca primeiro...")
        search_response, search_json = process_user_request("Buscar human gait")
        print(f"✅ Busca realizada: {len(search_response)} caracteres")
        
        # Agora tentar download do primeiro resultado
        print("\n📥 Tentando download do primeiro resultado...")
        download_response, download_json = process_user_request("baixar dados do primeiro resultado")
        print(f"✅ Download tentado: {len(download_response)} caracteres")
        
        if "❌" not in download_response:
            print("✅ Download funcionou!")
        else:
            print("⚠️ Download teve problemas (esperado para exemplos)")
            print(f"Resposta: {download_response[:200]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro: {e}")
        return False

def main():
    """Função principal."""
    print("🚀 TESTE DE VERIFICAÇÃO DE ARQUIVOS")
    print("=" * 60)
    
    # Aguardar servidor
    time.sleep(2)
    
    # Testar verificação de arquivos
    success1 = test_files_verification()
    
    # Testar download de resultado
    success2 = test_download_from_search()
    
    print("\n" + "="*60)
    print("📊 RESUMO DOS TESTES")
    print("="*60)
    
    print(f"Verificação de Arquivos: {'✅ PASSOU' if success1 else '❌ FALHOU'}")
    print(f"Download de Resultado:   {'✅ PASSOU' if success2 else '❌ FALHOU'}")
    
    if success1 and success2:
        print("\n🎉 Verificação de arquivos funcionando!")
        print("\n💡 Melhorias implementadas:")
        print("✅ Só mostra projetos com arquivos disponíveis")
        print("✅ Indica tipos de arquivos em cada projeto")
        print("✅ Permite download do primeiro resultado da busca")
        print("✅ Reconhece 'marcha humana' como sinônimo de 'gait'")
        
        print("\n🎮 Teste na interface:")
        print("1. Acesse: http://localhost:7861")
        print("2. Digite: 'Buscar marcha humana'")
        print("3. Observe que mostra projetos com arquivos")
        print("4. Digite: 'baixar dados do primeiro resultado'")
        print("5. Deve fazer download automaticamente")
    else:
        print("\n⚠️ Alguns testes falharam.")

if __name__ == "__main__":
    main()
