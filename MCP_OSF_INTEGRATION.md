# 🔬 Integração OSF.io no Servidor MCP

## 🎉 Funcionalidades Integradas

O servidor MCP agora inclui uma **nova aba completa** para trabalhar com dados de pesquisa do OSF.io (Open Science Framework).

### 📋 Nova Aba: "🔬 OSF.io Research Data"

A nova aba contém **4 sub-abas** com funcionalidades completas:

#### 1. 🔍 **Buscar Projetos**
- **Busca automática** no OSF.io
- **Filtros personalizáveis** (número de resultados)
- **Sugestões rápidas** (machine learning, psychology, etc.)
- **Resultados detalhados** com título, URL, tipo e autores

#### 2. 📥 **Download de Arquivos**
- **Download automático** de arquivos de projetos
- **Suporte a múltiplos formatos** (CSV, Excel, ZIP, JSON)
- **Projetos de exemplo** pré-configurados
- **Verificação de integridade** dos arquivos baixados

#### 3. 📊 **Análise de Dados**
- **Análise automática** de dados baixados
- **Estatísticas descritivas** completas
- **Gráficos automáticos** dos datasets
- **Suporte a múltiplos formatos** de dados

#### 4. 🚀 **Pipeline Completo**
- **Automação total**: Busca → Download → Análise
- **Pipelines rápidos** pré-configurados
- **Fallback inteligente** para projetos conhecidos
- **Visualizações automáticas** dos resultados

## 🚀 Como Usar

### 1. Iniciar o Servidor
```bash
uv run python src/main.py
```

### 2. Acessar a Interface
- **URL**: http://localhost:7860
- **Nova aba**: "🔬 OSF.io Research Data"

### 3. Fluxo de Trabalho Recomendado

#### Opção A: Pipeline Automático (Recomendado)
1. Vá para a sub-aba **"🚀 Pipeline Completo"**
2. Digite um termo de busca (ex: "data science")
3. Clique em **"🚀 Executar Pipeline Completo"**
4. Aguarde o resultado automático!

#### Opção B: Passo a Passo
1. **Buscar**: Sub-aba "🔍 Buscar Projetos"
2. **Baixar**: Copie uma URL e use "📥 Download de Arquivos"
3. **Analisar**: Use "📊 Análise de Dados" no diretório baixado

## 📊 Exemplos de Uso

### Busca Rápida
- **Termo**: "machine learning"
- **Resultado**: Lista de projetos com ML
- **Ação**: Copiar URL de interesse

### Download Direto
- **URL**: `https://osf.io/j4bv6/`
- **Resultado**: Arquivo CSV médico baixado
- **Tamanho**: ~69 KB

### Pipeline Automático
- **Termo**: "psychology"
- **Resultado**: Dados baixados + análise + gráficos
- **Tempo**: ~30-60 segundos

## 🎯 Projetos de Exemplo Testados

### 1. Machine Learning ICH Prediction
- **URL**: https://osf.io/j4bv6/
- **Dados**: CSV com 422 pacientes × 35 variáveis
- **Análise**: Dados médicos, estatísticas descritivas

### 2. Moral Algorithms Survey
- **URL**: https://osf.io/2zfu4/
- **Dados**: ZIP com dados de pesquisa
- **Tamanho**: ~56 MB

### 3. Data for Moral Algorithm Survey
- **URL**: https://osf.io/nkvjc/
- **Dados**: Excel com 460 participantes × 17 variáveis
- **Análise**: Escalas de 1-7, cenários morais

## 🛠️ Funcionalidades Técnicas

### Busca Inteligente
- **Selenium + BeautifulSoup** para JavaScript
- **Múltiplos seletores** para robustez
- **Fallback automático** se requests falhar

### Download Robusto
- **Verificação automática** de projetos com arquivos
- **Suporte a ZIP** com extração automática
- **Metadados completos** (tamanho, data, tipo)

### Análise Avançada
- **Pandas + NumPy** para processamento
- **Matplotlib + Seaborn** para visualização
- **Detecção automática** de tipos de dados
- **Estatísticas descritivas** completas

## 📈 Visualizações Geradas

### Gráficos Automáticos
1. **Informações do Dataset** (dimensões, tipos, memória)
2. **Distribuições Numéricas** (histogramas)
3. **Valores Ausentes** (análise de qualidade)
4. **Correlações** (matriz de correlação)

### Estatísticas Incluídas
- **Dimensões**: linhas × colunas
- **Tipos de dados**: numérico, texto, data
- **Valores ausentes**: contagem e percentual
- **Estatísticas descritivas**: média, desvio, min/max

## ⚠️ Notas Importantes

### Limitações
- **Nem todos os projetos têm arquivos** (normal no OSF.io)
- **Alguns arquivos podem ser privados**
- **Requer Chrome** instalado para Selenium
- **Tempo de execução** varia (30s-2min)

### Troubleshooting
- **Erro de Chrome**: Instalar Google Chrome
- **Timeout**: Aumentar tempo limite ou tentar novamente
- **Sem resultados**: Tentar termos de busca diferentes
- **Arquivos não encontrados**: Projeto pode não ter dados públicos

## 🎉 Benefícios da Integração

### Para Pesquisadores
- **Acesso fácil** a dados reais de pesquisa
- **Análise imediata** sem código
- **Visualizações automáticas**
- **Workflow completo** em uma interface

### Para Desenvolvedores
- **API completa** para OSF.io
- **Funções reutilizáveis**
- **Pipeline extensível**
- **Integração com Gradio**

### Para Educação
- **Dados reais** para ensino
- **Exemplos práticos** de análise
- **Interface intuitiva**
- **Resultados imediatos**

## 🚀 Próximos Passos

### Melhorias Futuras
- **Cache de resultados** para busca mais rápida
- **Filtros avançados** por tipo de arquivo
- **Exportação de relatórios** em PDF
- **Integração com outras plataformas** (GitHub, Zenodo)

### Extensões Possíveis
- **Machine Learning automático** nos dados
- **Comparação entre datasets**
- **Análise temporal** de projetos
- **Recomendações inteligentes**

---

## 📞 Suporte

Para problemas ou sugestões:
1. Verificar logs do servidor
2. Testar com projetos de exemplo
3. Verificar conectividade com OSF.io
4. Consultar documentação do OSF Scraper

**Status**: ✅ **Totalmente Funcional e Integrado!**
