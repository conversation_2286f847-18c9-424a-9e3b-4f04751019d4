#!/usr/bin/env python3
"""
Debug específico para extração de arquivos
"""

import sys
import os
import time
from bs4 import BeautifulSoup

def debug_file_extraction():
    """Debug da extração de arquivos"""
    print("=== Debug: Extração de Arquivos ===")
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.chrome.service import Service
        from webdriver_manager.chrome import ChromeDriverManager
        
        # Configurar Chrome
        chrome_options = Options()
        chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)
        
        try:
            # Acessar página de arquivos
            files_url = "https://osf.io/j4bv6/files/osfstorage"
            print(f"Acessando: {files_url}")
            
            driver.get(files_url)
            time.sleep(10)  # Aguardar mais tempo
            
            print(f"URL atual: {driver.current_url}")
            
            # Analisar HTML
            soup = BeautifulSoup(driver.page_source, 'html.parser')
            
            # Debug 1: Todos os links
            print("\n=== Todos os Links ===")
            all_links = soup.find_all('a', href=True)
            print(f"Total de links: {len(all_links)}")
            
            for i, link in enumerate(all_links):
                href = link.get('href', '')
                text = link.get_text(strip=True)
                
                if text and len(text) > 3:  # Apenas links com texto significativo
                    print(f"{i+1}. '{text[:50]}' -> {href}")
            
            # Debug 2: Links específicos de arquivo
            print("\n=== Links de Arquivo ===")
            file_links = []
            
            for link in all_links:
                href = link.get('href', '')
                text = link.get_text(strip=True)
                
                # Critérios mais amplos para detectar arquivos
                if any(pattern in href for pattern in ['/files/', '/osfstorage/', '6007ee7d']):
                    file_links.append({
                        'text': text,
                        'href': href,
                        'parent_text': link.parent.get_text(strip=True)[:100] if link.parent else ''
                    })
            
            print(f"Links de arquivo encontrados: {len(file_links)}")
            for i, link in enumerate(file_links):
                print(f"{i+1}. Text: '{link['text']}'")
                print(f"    Href: {link['href']}")
                print(f"    Parent: {link['parent_text']}")
                print()
            
            # Debug 3: Elementos com classes específicas
            print("\n=== Elementos com Classes de Arquivo ===")
            
            # Procurar por elementos que vimos no debug anterior
            file_classes = [
                '_FileList__item_1ilyal',
                '_FileList__item__name_1ilyal',
                '_FileList_1qi2e2'
            ]
            
            for class_name in file_classes:
                elements = soup.find_all(class_=class_name)
                print(f"Elementos com classe '{class_name}': {len(elements)}")
                
                for i, elem in enumerate(elements):
                    text = elem.get_text(strip=True)
                    print(f"  {i+1}. {text[:80]}...")
                    
                    # Procurar por links dentro do elemento
                    links_in_elem = elem.find_all('a', href=True)
                    for link in links_in_elem:
                        print(f"      Link: {link.get('href')} -> '{link.get_text(strip=True)}'")
                print()
            
            # Debug 4: Procurar por padrões de ID de arquivo
            print("\n=== Procurando IDs de Arquivo ===")
            
            # O ID que vimos no debug anterior: 6007ee7d86541a097214a07b
            file_id_pattern = "6007ee7d"
            
            # Procurar em todo o HTML
            html_content = str(soup)
            if file_id_pattern in html_content:
                print(f"✓ ID de arquivo '{file_id_pattern}' encontrado no HTML")
                
                # Encontrar contexto ao redor do ID
                import re
                matches = re.finditer(file_id_pattern, html_content)
                for match in matches:
                    start = max(0, match.start() - 100)
                    end = min(len(html_content), match.end() + 100)
                    context = html_content[start:end]
                    print(f"Contexto: ...{context}...")
                    print()
            else:
                print(f"✗ ID de arquivo '{file_id_pattern}' não encontrado")
            
            # Debug 5: Salvar HTML para análise manual
            print("\n=== Salvando HTML para Análise ===")
            
            with open('debug_osf_files.html', 'w', encoding='utf-8') as f:
                f.write(soup.prettify())
            
            print("HTML salvo em 'debug_osf_files.html'")
            
            # Debug 6: Tentar interagir com a página
            print("\n=== Tentando Interações ===")
            
            try:
                # Tentar rolar a página para baixo
                driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                time.sleep(3)
                
                # Tentar clicar em elementos que podem expandir conteúdo
                from selenium.webdriver.common.by import By
                
                clickable_selectors = [
                    "button",
                    "[role='button']",
                    ".clickable",
                    "[class*='expand']",
                    "[class*='toggle']"
                ]
                
                for selector in clickable_selectors:
                    try:
                        elements = driver.find_elements(By.CSS_SELECTOR, selector)
                        print(f"Elementos clicáveis '{selector}': {len(elements)}")
                        
                        for elem in elements[:2]:  # Tentar apenas os primeiros 2
                            try:
                                text = elem.text.strip()
                                if text:
                                    print(f"  Tentando clicar em: '{text}'")
                                    elem.click()
                                    time.sleep(2)
                            except:
                                pass
                    except:
                        pass
                
                # Analisar novamente após interações
                soup_after = BeautifulSoup(driver.page_source, 'html.parser')
                links_after = soup_after.find_all('a', href=True)
                
                print(f"Links após interações: {len(links_after)}")
                
                # Procurar novamente por arquivos
                new_file_links = []
                for link in links_after:
                    href = link.get('href', '')
                    text = link.get_text(strip=True)
                    
                    if any(pattern in href for pattern in ['/files/', '/osfstorage/', '6007ee7d']):
                        new_file_links.append({
                            'text': text,
                            'href': href
                        })
                
                print(f"Novos links de arquivo: {len(new_file_links)}")
                for link in new_file_links:
                    print(f"  - '{link['text']}' -> {link['href']}")
                
            except Exception as e:
                print(f"Erro durante interações: {e}")
            
        finally:
            driver.quit()
            
    except Exception as e:
        print(f"Erro durante debug: {e}")

def main():
    """Função principal"""
    print("Debug de Extração de Arquivos OSF")
    print("=" * 50)
    
    debug_file_extraction()
    
    print("\n" + "=" * 50)
    print("Debug concluído!")

if __name__ == "__main__":
    main()
