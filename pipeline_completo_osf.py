#!/usr/bin/env python3
"""
Pipeline Completo OSF: Busca → Download → Análise → Gráficos

Este script demonstra um pipeline completo:
1. Busca projetos no OSF.io
2. Faz download dos arquivos
3. Analisa os dados
4. Gera gráficos básicos
"""

import sys
import os
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
from pathlib import Path
import zipfile
import json

# Adicionar src ao path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from osf_scraper import search_osf, download_osf_files

# Configurar estilo dos gráficos
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

def buscar_projetos_com_dados():
    """Etapa 1: Buscar projetos que provavelmente têm dados"""
    print("🔍 ETAPA 1: Buscando projetos com dados")
    print("=" * 50)
    
    # Termos que provavelmente retornam projetos com dados
    termos_busca = ["data", "dataset", "analysis", "csv"]
    
    todos_projetos = []
    
    for termo in termos_busca:
        print(f"Buscando por: '{termo}'")
        
        try:
            resultados = search_osf(termo, max_results=3, use_selenium=True)
            
            for resultado in resultados:
                if resultado.get('type') == 'Project':
                    todos_projetos.append({
                        'titulo': resultado.get('title', 'Sem título'),
                        'url': resultado.get('url', ''),
                        'tipo': resultado.get('type', ''),
                        'termo_busca': termo
                    })
                    print(f"  ✓ {resultado.get('title', 'Sem título')[:50]}...")
                    
        except Exception as e:
            print(f"  ✗ Erro na busca por '{termo}': {e}")
    
    print(f"\n📊 Total de projetos encontrados: {len(todos_projetos)}")
    return todos_projetos

def fazer_download_projeto(projeto):
    """Etapa 2: Fazer download dos arquivos de um projeto"""
    print(f"\n📥 ETAPA 2: Fazendo download do projeto")
    print("=" * 50)
    
    titulo = projeto['titulo']
    url = projeto['url']
    
    print(f"Projeto: {titulo[:60]}...")
    print(f"URL: {url}")
    
    # Criar nome seguro para diretório
    nome_seguro = "".join(c for c in titulo if c.isalnum() or c in (' ', '-', '_')).rstrip()
    nome_seguro = nome_seguro.replace(' ', '_')[:30]
    
    diretorio_download = f"pipeline_downloads/{nome_seguro}"
    
    try:
        arquivos_baixados = download_osf_files(url, diretorio_download)
        
        if arquivos_baixados:
            print(f"✓ {len(arquivos_baixados)} arquivos baixados:")
            for arquivo in arquivos_baixados:
                print(f"  - {arquivo['name']} ({arquivo['file_size_bytes']} bytes)")
            
            return arquivos_baixados, diretorio_download
        else:
            print("ℹ Projeto não tem arquivos disponíveis")
            return [], diretorio_download
            
    except Exception as e:
        print(f"✗ Erro no download: {e}")
        return [], diretorio_download

def analisar_arquivos(arquivos_baixados, diretorio):
    """Etapa 3: Analisar os arquivos baixados"""
    print(f"\n📊 ETAPA 3: Analisando arquivos")
    print("=" * 50)
    
    dados_analisados = []
    
    for arquivo in arquivos_baixados:
        caminho_arquivo = arquivo['local_path']
        nome_arquivo = arquivo['name']
        
        print(f"Analisando: {nome_arquivo}")
        
        try:
            # Tentar diferentes tipos de arquivo
            dados = None
            tipo_dados = None
            
            # CSV
            if caminho_arquivo.lower().endswith('.csv'):
                dados = pd.read_csv(caminho_arquivo)
                tipo_dados = 'CSV'
                
            # Excel
            elif caminho_arquivo.lower().endswith(('.xlsx', '.xls')):
                dados = pd.read_excel(caminho_arquivo)
                tipo_dados = 'Excel'
                
            # JSON
            elif caminho_arquivo.lower().endswith('.json'):
                with open(caminho_arquivo, 'r') as f:
                    json_data = json.load(f)
                if isinstance(json_data, list):
                    dados = pd.DataFrame(json_data)
                    tipo_dados = 'JSON'
                    
            # ZIP (tentar extrair e analisar conteúdo)
            elif caminho_arquivo.lower().endswith('.zip'):
                print(f"  Arquivo ZIP detectado - extraindo...")
                try:
                    with zipfile.ZipFile(caminho_arquivo, 'r') as zip_ref:
                        # Listar conteúdo
                        arquivos_zip = zip_ref.namelist()
                        print(f"  Conteúdo do ZIP: {len(arquivos_zip)} arquivos")
                        
                        # Procurar por arquivos de dados
                        for arquivo_interno in arquivos_zip[:5]:  # Apenas os primeiros 5
                            if arquivo_interno.lower().endswith(('.csv', '.xlsx', '.json')):
                                print(f"    - {arquivo_interno}")
                                
                                # Extrair e tentar ler
                                zip_ref.extract(arquivo_interno, diretorio)
                                caminho_extraido = os.path.join(diretorio, arquivo_interno)
                                
                                if arquivo_interno.lower().endswith('.csv'):
                                    dados = pd.read_csv(caminho_extraido)
                                    tipo_dados = 'CSV (do ZIP)'
                                    break
                                elif arquivo_interno.lower().endswith(('.xlsx', '.xls')):
                                    dados = pd.read_excel(caminho_extraido)
                                    tipo_dados = 'Excel (do ZIP)'
                                    break
                except Exception as e:
                    print(f"    ✗ Erro ao extrair ZIP: {e}")
            
            if dados is not None:
                print(f"  ✓ Dados carregados ({tipo_dados})")
                print(f"    Formato: {dados.shape[0]} linhas × {dados.shape[1]} colunas")
                print(f"    Colunas: {list(dados.columns[:5])}{'...' if len(dados.columns) > 5 else ''}")
                
                dados_analisados.append({
                    'nome_arquivo': nome_arquivo,
                    'dados': dados,
                    'tipo': tipo_dados,
                    'formato': dados.shape
                })
            else:
                print(f"  ⚠ Tipo de arquivo não suportado para análise")
                
        except Exception as e:
            print(f"  ✗ Erro ao analisar {nome_arquivo}: {e}")
    
    return dados_analisados

def gerar_graficos(dados_analisados):
    """Etapa 4: Gerar gráficos dos dados"""
    print(f"\n📈 ETAPA 4: Gerando gráficos")
    print("=" * 50)
    
    if not dados_analisados:
        print("Nenhum dado disponível para gráficos")
        return
    
    # Criar diretório para gráficos
    os.makedirs("pipeline_graficos", exist_ok=True)
    
    for i, item in enumerate(dados_analisados):
        nome_arquivo = item['nome_arquivo']
        dados = item['dados']
        
        print(f"Gerando gráficos para: {nome_arquivo}")
        
        try:
            # Configurar figura
            fig, axes = plt.subplots(2, 2, figsize=(15, 12))
            fig.suptitle(f'Análise de Dados: {nome_arquivo[:40]}...', fontsize=16)
            
            # Gráfico 1: Informações gerais
            ax1 = axes[0, 0]
            info_texto = f"""
Arquivo: {nome_arquivo}
Tipo: {item['tipo']}
Linhas: {dados.shape[0]:,}
Colunas: {dados.shape[1]:,}
Memória: {dados.memory_usage(deep=True).sum() / 1024:.1f} KB

Tipos de dados:
{dados.dtypes.value_counts().to_string()}
            """
            ax1.text(0.1, 0.9, info_texto, transform=ax1.transAxes, fontsize=10,
                    verticalalignment='top', fontfamily='monospace')
            ax1.set_title('Informações do Dataset')
            ax1.axis('off')
            
            # Gráfico 2: Distribuição de tipos de colunas
            ax2 = axes[0, 1]
            tipos_colunas = dados.dtypes.value_counts()
            ax2.pie(tipos_colunas.values, labels=tipos_colunas.index, autopct='%1.1f%%')
            ax2.set_title('Distribuição de Tipos de Dados')
            
            # Gráfico 3: Valores ausentes
            ax3 = axes[1, 0]
            valores_ausentes = dados.isnull().sum()
            if valores_ausentes.sum() > 0:
                valores_ausentes = valores_ausentes[valores_ausentes > 0][:10]  # Top 10
                ax3.bar(range(len(valores_ausentes)), valores_ausentes.values)
                ax3.set_xticks(range(len(valores_ausentes)))
                ax3.set_xticklabels(valores_ausentes.index, rotation=45, ha='right')
                ax3.set_title('Valores Ausentes por Coluna')
                ax3.set_ylabel('Quantidade')
            else:
                ax3.text(0.5, 0.5, 'Nenhum valor ausente encontrado!', 
                        transform=ax3.transAxes, ha='center', va='center', fontsize=12)
                ax3.set_title('Valores Ausentes')
                ax3.axis('off')
            
            # Gráfico 4: Distribuição de uma coluna numérica (se houver)
            ax4 = axes[1, 1]
            colunas_numericas = dados.select_dtypes(include=[np.number]).columns
            
            if len(colunas_numericas) > 0:
                coluna_escolhida = colunas_numericas[0]
                dados_coluna = dados[coluna_escolhida].dropna()
                
                if len(dados_coluna) > 0:
                    ax4.hist(dados_coluna, bins=min(30, len(dados_coluna.unique())), alpha=0.7)
                    ax4.set_title(f'Distribuição: {coluna_escolhida}')
                    ax4.set_xlabel(coluna_escolhida)
                    ax4.set_ylabel('Frequência')
                else:
                    ax4.text(0.5, 0.5, 'Coluna numérica vazia', 
                            transform=ax4.transAxes, ha='center', va='center')
                    ax4.set_title('Distribuição Numérica')
            else:
                ax4.text(0.5, 0.5, 'Nenhuma coluna numérica encontrada', 
                        transform=ax4.transAxes, ha='center', va='center')
                ax4.set_title('Distribuição Numérica')
                ax4.axis('off')
            
            # Salvar gráfico
            nome_grafico = f"pipeline_graficos/analise_{i+1}_{nome_arquivo[:20]}.png"
            plt.tight_layout()
            plt.savefig(nome_grafico, dpi=300, bbox_inches='tight')
            plt.close()
            
            print(f"  ✓ Gráfico salvo: {nome_grafico}")
            
            # Gerar estatísticas descritivas se houver dados numéricos
            if len(colunas_numericas) > 0:
                print(f"  📊 Estatísticas descritivas:")
                stats = dados[colunas_numericas].describe()
                print(f"    {stats.to_string()}")
            
        except Exception as e:
            print(f"  ✗ Erro ao gerar gráfico para {nome_arquivo}: {e}")

def main():
    """Pipeline principal"""
    print("🚀 PIPELINE COMPLETO OSF.io")
    print("Busca → Download → Análise → Gráficos")
    print("=" * 60)
    
    # Etapa 1: Buscar projetos
    projetos = buscar_projetos_com_dados()
    
    if not projetos:
        print("❌ Nenhum projeto encontrado. Encerrando.")
        return
    
    # Tentar múltiplos projetos até encontrar dados analisáveis
    dados_analisados = []
    arquivos_finais = []
    diretorio_final = ""
    projeto_escolhido = None

    for i, projeto in enumerate(projetos[:3]):  # Tentar até 3 projetos
        print(f"\n🔄 Tentativa {i+1}/{min(3, len(projetos))}")

        # Etapa 2: Download
        arquivos, diretorio = fazer_download_projeto(projeto)

        if arquivos:
            # Etapa 3: Análise
            dados_temp = analisar_arquivos(arquivos, diretorio)

            if dados_temp:  # Se encontrou dados analisáveis
                dados_analisados = dados_temp
                arquivos_finais = arquivos
                diretorio_final = diretorio
                projeto_escolhido = projeto
                print(f"✅ Dados analisáveis encontrados no projeto: {projeto['titulo'][:50]}...")
                break
            else:
                print(f"⚠ Projeto {projeto['titulo'][:30]}... não tem dados analisáveis")
        else:
            print(f"⚠ Projeto {projeto['titulo'][:30]}... não tem arquivos")

    if not dados_analisados:
        print("❌ Nenhum projeto com dados analisáveis encontrado.")
        print("💡 Tentando com projeto conhecido que tem dados...")

        # Fallback: usar projeto conhecido
        projeto_conhecido = {
            'titulo': 'Machine Learning ICH Prediction (projeto conhecido)',
            'url': 'https://osf.io/j4bv6/',
            'tipo': 'Project'
        }

        arquivos_finais, diretorio_final = fazer_download_projeto(projeto_conhecido)
        if arquivos_finais:
            dados_analisados = analisar_arquivos(arquivos_finais, diretorio_final)
            projeto_escolhido = projeto_conhecido
    
    if not dados_analisados:
        print("❌ Nenhum dado disponível para análise.")
        return

    # Etapa 4: Gráficos
    gerar_graficos(dados_analisados)
    
    # Resumo final
    print(f"\n🎉 PIPELINE CONCLUÍDO!")
    print("=" * 60)
    print(f"📁 Projeto analisado: {projeto_escolhido['titulo'][:50]}...")
    print(f"📥 Arquivos baixados: {len(arquivos_finais)}")
    print(f"📊 Datasets analisados: {len(dados_analisados)}")
    print(f"📈 Gráficos gerados: {len(dados_analisados)}")
    print(f"📂 Diretório de downloads: {diretorio_final}")
    print(f"📂 Diretório de gráficos: pipeline_graficos/")
    
    if dados_analisados:
        print(f"\n📋 Resumo dos dados:")
        for item in dados_analisados:
            print(f"  - {item['nome_arquivo']}: {item['formato'][0]} linhas × {item['formato'][1]} colunas")

if __name__ == "__main__":
    main()
