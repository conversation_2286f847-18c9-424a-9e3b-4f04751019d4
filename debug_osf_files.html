<html lang="en">
 <head>
  <meta charset="utf-8"/>
  <meta content="IE=edge" http-equiv="X-UA-Compatible"/>
  <title>
   OSF
  </title>
  <meta content="" name="description"/>
  <meta content="width=device-width, initial-scale=1" name="viewport"/>
  <link href="https://fonts.googleapis.com/css?family=Open+Sans:400,600,300,700" rel="stylesheet" type="text/css"/>
  <link class="notranslate" href="/ember_osf_web/assets/vendor-6abbbe9cc7717da142b73ab60ba01ae5.css" rel="stylesheet"/>
  <link class="notranslate" href="/ember_osf_web/assets/ember-osf-web-5a21c32d25acc039311901559428a450.css" rel="stylesheet"/>
  <script type="text/x-mathjax-config;executed=true">
   MathJax.Hub.Config({
          tex2jax: {inlineMath: [['$','$'], ['\\(','\\)']], processEscapes: true},
          skipStartupTypeset: true
      });
  </script>
  <script async="" src="//cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.1/MathJax.js?config=TeX-AMS-MML_HTMLorMML" type="text/javascript">
  </script>
  <meta content="%7B%22modulePrefix%22%3A%22ember-osf-web%22%2C%22cedarConfig%22%3A%7B%22viewerConfig%22%3A%7B%22showHeader%22%3Afalse%2C%22showFooter%22%3Afalse%2C%22expandedSampleTemplateLinks%22%3Afalse%2C%22showSampleTemplateLinks%22%3Afalse%2C%22defaultLanguage%22%3A%22en%22%2C%22showTemplateData%22%3Afalse%2C%22showInstanceData%22%3Afalse%7D%2C%22editorConfig%22%3A%7B%22defaultLanguage%22%3A%22en%22%2C%22expandedSampleTemplateLinks%22%3Atrue%2C%22showTemplateRenderingRepresentation%22%3Afalse%2C%22showMultiInstanceInfo%22%3Afalse%2C%22expandedInstanceDataFull%22%3Afalse%2C%22expandedInstanceDataCore%22%3Afalse%2C%22expandedMultiInstanceInfo%22%3Afalse%2C%22expandedTemplateRenderingRepresentation%22%3Afalse%2C%22expandedTemplateSourceData%22%3Afalse%2C%22collapseStaticComponents%22%3Afalse%2C%22showStaticText%22%3Afalse%2C%22showInstanceDataCore%22%3Afalse%2C%22showHeader%22%3Afalse%2C%22showFooter%22%3Afalse%2C%22terminologyIntegratedSearchUrl%22%3A%22https%3A//terminology.metadatacenter.org/bioportal/integrated-search%22%2C%22showInstanceDataFull%22%3Afalse%2C%22showTemplateSourceData%22%3Afalse%7D%7D%2C%22WATER_BUTLER_ENABLED%22%3Atrue%2C%22plauditWidgetUrl%22%3A%22https%3A//plaudit.pub/embed/endorsements.js%22%2C%22environment%22%3A%22production%22%2C%22lintOnBuild%22%3Afalse%2C%22testsEnabled%22%3Afalse%2C%22sourcemapsEnabled%22%3Atrue%2C%22showDevBanner%22%3Afalse%2C%22rootURL%22%3A%22/%22%2C%22assetsPrefix%22%3A%22/ember_osf_web/%22%2C%22locationType%22%3A%22auto%22%2C%22sentryDSN%22%3A%22https%3A//<EMAIL>/7%22%2C%22googleTagManagerId%22%3A%22GTM-NDTC398%22%2C%22sentryOptions%22%3A%7B%22release%22%3A%22ce9bb4aeb67d7b1a69abb6a2ff866d1d723f68f5%22%2C%22ignoreErrors%22%3A%5B%22TransitionAborted%22%5D%7D%2C%22EmberENV%22%3A%7B%22FEATURES%22%3A%7B%7D%2C%22EXTEND_PROTOTYPES%22%3A%7B%22Date%22%3Afalse%7D%2C%22_APPLICATION_TEMPLATE_WRAPPER%22%3Afalse%2C%22_DEFAULT_ASYNC_OBSERVERS%22%3Atrue%2C%22_JQUERY_INTEGRATION%22%3Afalse%2C%22_TEMPLATE_ONLY_GLIMMER_COMPONENTS%22%3Atrue%7D%2C%22APP%22%3A%7B%22name%22%3A%22ember-osf-web%22%2C%22version%22%3A%2225.10.0%22%7D%2C%22moment%22%3A%7B%22includeTimezone%22%3A%22all%22%2C%22outputFormat%22%3A%22YYYY-MM-DD%20h%3Amm%20A%20z%22%7D%2C%22metricsAdapters%22%3A%5B%7B%22name%22%3A%22GoogleTagManager%22%2C%22environments%22%3A%5B%22production%22%5D%2C%22config%22%3A%7B%22id%22%3A%22GTM-NDTC398%22%7D%7D%5D%2C%22OSF%22%3A%7B%22apiNamespace%22%3A%22v2%22%2C%22backend%22%3A%22prod%22%2C%22url%22%3A%22https%3A//osf.io/%22%2C%22apiUrl%22%3A%22https%3A//api.osf.io%22%2C%22apiVersion%22%3A%222.20%22%2C%22apiHeaders%22%3A%7B%22Accept%22%3A%22application/vnd.api+json%3B%20version%3D2.20%22%7D%2C%22learnMoreUrl%22%3A%22https%3A//cos.io/our-products/osf/%22%2C%22donateUrl%22%3A%22https%3A//cos.io/donate%22%2C%22renderUrl%22%3A%22https%3A//mfr.osf.io/render%22%2C%22mfrUrl%22%3A%22https%3A//mfr.osf.io/%22%2C%22waterbutlerUrl%22%3A%22https%3A//files.osf.io/%22%2C%22addonServiceUrl%22%3A%22https%3A//addons.osf.io/%22%2C%22helpUrl%22%3A%22http%3A//help.osf.io%22%2C%22shareBaseUrl%22%3A%22https%3A//share.osf.io/%22%2C%22shareSearchUrl%22%3A%22https%3A//share.osf.io/api/v2/search/creativeworks/_search%22%2C%22devMode%22%3Afalse%2C%22metricsStartDate%22%3A%222019-01-01%22%2C%22cookieDomain%22%3Anull%2C%22authenticator%22%3A%22authenticator%3Aosf-cookie%22%2C%22cookies%22%3A%7B%22status%22%3A%22osf_status%22%2C%22keenUserId%22%3A%22keenUserId%22%2C%22keenSessionId%22%3A%22keenSessionId%22%2C%22cookieConsent%22%3A%22osf_cookieconsent%22%2C%22newFeaturePopover%22%3A%22metadataFeaturePopover%22%2C%22maintenance%22%3A%22maintenance%22%2C%22csrf%22%3A%22api-csrf%22%2C%22authSession%22%3A%22embosf-auth-session%22%7D%2C%22localStorageKeys%22%3A%7B%22authSession%22%3A%22embosf-auth-session%22%2C%22joinBannerDismissed%22%3A%22slide%22%7D%2C%22casUrl%22%3A%22https%3A//accounts.osf.io%22%2C%22analyticsAttrs%22%3A%7B%22name%22%3A%22data-analytics-name%22%2C%22scope%22%3A%22data-analytics-scope%22%2C%22extra%22%3A%22data-analytics-extra%22%2C%22category%22%3A%22data-analytics-category%22%2C%22action%22%3A%22data-analytics-action%22%7D%2C%22doiUrlPrefix%22%3A%22https%3A//doi.org/%22%2C%22dataciteTrackerRepoId%22%3A%22da-calhaql%22%2C%22dataCiteTrackerUrl%22%3A%22https%3A//analytics.datacite.org/api/metric%22%2C%22cookieLoginUrl%22%3A%22https%3A//accounts.osf.io/login%22%2C%22oauthUrl%22%3A%22https%3A//accounts.osf.io/oauth2/authorize%22%2C%22shareApiUrl%22%3A%22https%3A//share.osf.io/api/v2%22%2C%22isLocal%22%3Afalse%2C%22keenProjectId%22%3A%225797b58fbcb79c2c0fa1a705%22%2C%22orcidClientId%22%3A%22APP-VGLDMYSOHONDWCV8%22%7D%2C%22social%22%3A%7B%22twitter%22%3A%7B%22viaHandle%22%3A%22OSFramework%22%7D%7D%2C%22signUpPolicy%22%3A%7B%22termsLink%22%3A%22https%3A//osf.io/terms_of_use/%22%2C%22privacyPolicyLink%22%3A%22https%3A//osf.io/privacy_policy/%22%2C%22cookiesLink%22%3A%22https%3A//osf.io/privacy_policy/%23f-cookies%22%7D%2C%22footerLinks%22%3A%7B%22cos%22%3A%22https%3A//cos.io%22%2C%22statusPage%22%3A%22https%3A//status.cos.io/%22%2C%22apiDocs%22%3A%22https%3A//developer.osf.io/%22%2C%22topGuidelines%22%3A%22http%3A//cos.io/top/%22%2C%22rpp%22%3A%22https%3A//osf.io/ezcuj/wiki/home/<USER>//osf.io/collections/rpcb/discover%22%2C%22twitter%22%3A%22http%3A//twitter.com/OSFramework%22%2C%22facebook%22%3A%22https%3A//www.facebook.com/CenterForOpenScience/%22%2C%22googleGroup%22%3A%22https%3A//groups.google.com/forum/%23%21forum/openscienceframework%22%2C%22github%22%3A%22https%3A//www.github.com/centerforopenscience%22%7D%2C%22support%22%3A%7B%22preregUrl%22%3A%22https%3A//cos.io/prereg/%22%2C%22statusPageUrl%22%3A%22https%3A//status.cos.io%22%2C%22faqPageUrl%22%3A%22https%3A//<EMAIL><EMAIL>%22%2C%22consultationUrl%22%3A%22https%3A//cos.io/stats_consulting/%22%2C%22twitterUrl%22%3A%22https%3A//twitter.com/OSFSupport%22%2C%22mailingUrl%22%3A%22https%3A//groups.google.com/forum/%23%21forum/openscienceframework%22%2C%22facebookUrl%22%3A%22https%3A//www.facebook.com/CenterForOpenScience/%22%2C%22githubUrl%22%3A%22https%3A//github.com/centerforopenscience%22%7D%2C%22helpLinks%22%3A%7B%22linkToAProject%22%3A%22https%3A//help.osf.io/hc/en-us/articles/************-Link-to-a-Project%22%7D%2C%22dashboard%22%3A%7B%22popularNode%22%3A%2257tnq%22%2C%22noteworthyNode%22%3A%22z3sg2%22%7D%2C%22featureFlagNames%22%3A%7B%22routes%22%3A%7B%22collections.moderation%22%3A%22collections_moderation%22%2C%22registries.branded%22%3A%22branded_registries%22%2C%22registries.branded.discover%22%3A%22branded_registries%22%2C%22guid-node.index%22%3A%22ember_project_detail_page%22%2C%22guid-node.drafts.index%22%3A%22ember_edit_draft_registration_page%22%2C%22guid-node.drafts.register%22%3A%22ember_edit_draft_registration_page%22%2C%22guid-node.addons%22%3A%22gravy_waffle%22%2C%22guid-user.index%22%3A%22ember_user_profile_page%22%2C%22guid-registration.index%22%3A%22ember_old_registration_detail_page%22%2C%22settings%22%3A%22ember_user_settings_page%22%2C%22settings.profile%22%3A%22ember_user_settings_page%22%2C%22settings.profile.education%22%3A%22ember_user_settings_page%22%2C%22settings.profile.employment%22%3A%22ember_user_settings_page%22%2C%22settings.profile.name%22%3A%22ember_user_settings_page%22%2C%22settings.profile.social%22%3A%22ember_user_settings_page%22%2C%22settings.account%22%3A%22ember_user_settings_account_page%22%2C%22settings.tokens%22%3A%22ember_user_settings_tokens_page%22%2C%22settings.tokens.index%22%3A%22ember_user_settings_tokens_page%22%2C%22settings.tokens.create%22%3A%22ember_user_settings_tokens_page%22%2C%22settings.tokens.edit%22%3A%22ember_user_settings_tokens_page%22%2C%22settings.developer-apps%22%3A%22ember_user_settings_apps_page%22%2C%22settings.developer-apps.index%22%3A%22ember_user_settings_apps_page%22%2C%22settings.developer-apps.create%22%3A%22ember_user_settings_apps_page%22%2C%22settings.developer-apps.edit%22%3A%22ember_user_settings_apps_page%22%2C%22settings.addons%22%3A%22gravy_waffle%22%2C%22register%22%3A%22ember_auth_register%22%2C%22registries.overview%22%3A%22ember_registries_detail_page%22%2C%22registries.overview.index%22%3A%22ember_registries_detail_page%22%2C%22registries.overview.comments%22%3A%22ember_registries_detail_page%22%2C%22registries.overview.contributors%22%3A%22ember_registries_detail_page%22%2C%22registries.overview.children%22%3A%22ember_registries_detail_page%22%2C%22registries.overview.links%22%3A%22ember_registries_detail_page%22%2C%22registries.start%22%3A%22ember_registries_submission_start%22%2C%22registries.drafts%22%3A%22ember_registries_submission_drafts%22%2C%22registries.drafts.index%22%3A%22ember_registries_submission_drafts%22%2C%22registries.drafts.draft.metadata%22%3A%22ember_edit_draft_registration_page%22%2C%22registries.drafts.draft.page%22%3A%22ember_edit_draft_registration_page%22%2C%22registries.drafts.draft.review%22%3A%22ember_edit_draft_registration_page%22%2C%22registries.forms%22%3A%22ember_registries_submission_forms%22%2C%22registries.forms.help%22%3A%22ember_registries_submission_forms%22%2C%22meetings.index%22%3A%22ember_meetings_page%22%2C%22meetings.detail%22%3A%22ember_meeting_detail_page%22%7D%2C%22navigation%22%3A%7B%22institutions%22%3A%22institutions_nav_bar%22%7D%2C%22storageI18n%22%3A%22storage_i18n%22%2C%22gravyWaffle%22%3A%22gravy_waffle%22%2C%22enableInactiveSchemas%22%3A%22enable_inactive_schemas%22%2C%22verifyEmailModals%22%3A%22ember_verify_email_modals%22%2C%22ABTesting%22%3A%7B%22homePageHeroTextVersionB%22%3A%22ab_testing_home_page_hero_text_version_b%22%7D%2C%22registrationFilesPage%22%3A%22ember_registration_files_page%22%2C%22egapAdmins%22%3A%22egap_admins%22%2C%22shareDownload%22%3A%22share_download%22%7D%2C%22gReCaptcha%22%3A%7B%22siteKey%22%3A%226Ld2hikTAAAAAAhExltkBtTplSAJCSwAjpvMnOhc%22%2C%22jsUrl%22%3A%22https%3A//recaptcha.net/recaptcha/api.js%3Frender%3Dexplicit%22%7D%2C%22home%22%3A%7B%22youtubeId%22%3A%222TV21gOzfhw%22%7D%2C%22secondaryNavbarId%22%3A%22__secondaryOSFNavbar__%22%2C%22engines%22%3A%7B%22collections%22%3A%7B%22enabled%22%3Atrue%7D%2C%22registries%22%3A%7B%22enabled%22%3Atrue%7D%2C%22handbook%22%3A%7B%22enabled%22%3Afalse%2C%22docGenerationEnabled%22%3Afalse%7D%7D%2C%22ember-cli-tailwind%22%3A%7B%22shouldIncludeStyleguide%22%3Afalse%7D%2C%22ember-cli-mirage%22%3A%7B%22enabled%22%3Afalse%2C%22usingProxy%22%3Afalse%2C%22useDefaultPassthroughs%22%3Atrue%7D%2C%22changeset-validations%22%3A%7B%22rawOutput%22%3Atrue%7D%2C%22mirageScenarios%22%3A%5B%22cedar%22%2C%22collections%22%2C%22dashboard%22%2C%22forks%22%2C%22loggedIn%22%2C%22meetings%22%2C%22preprints%22%2C%22registrations%22%2C%22settings%22%5D%2C%22defaultProvider%22%3A%22osf%22%2C%22pageTitle%22%3A%7B%22prepend%22%3Afalse%7D%2C%22exportApplicationGlobal%22%3Afalse%2C%22ember-faker%22%3A%7B%7D%2C%22FB_APP_ID%22%3A%221022273774556662%22%2C%22microfeedback%22%3A%7B%22url%22%3A%22https%3A//microfeedback-osf.herokuapp.com/13209/3%22%2C%22pageParams%22%3A%7B%22QuickFiles%22%3A%7B%22componentID%22%3A%2213836%22%2C%22priorityID%22%3A%2210100%22%7D%7D%7D%7D" name="ember-osf-web/config/environment"/>
  <meta content="%7B%22modulePrefix%22%3A%22analytics-page%22%2C%22environment%22%3A%22production%22%7D" name="analytics-page/config/environment"/>
  <meta content="%7B%22environment%22%3A%22production%22%2C%22hostAppName%22%3A%22collections%22%2C%22modulePrefix%22%3A%22collections%22%2C%22OSF%22%3A%7B%22url%22%3A%22http%3A//localhost%3A5000/%22%7D%2C%22whiteListedProviders%22%3A%5B%22arXiv%22%2C%22bioRxiv%22%2C%22Cogprints%22%2C%22PeerJ%22%2C%22Research%20Papers%20in%20Economics%22%2C%22Preprints.org%22%5D%2C%22APP%22%3A%7B%7D%7D" name="collections/config/environment"/>
  <meta content="%7B%22environment%22%3A%22production%22%2C%22EmberENV%22%3A%7B%22FEATURES%22%3A%7B%7D%2C%22EXTEND_PROTOTYPES%22%3A%7B%22Date%22%3Afalse%7D%7D%2C%22APP%22%3A%7B%7D%2C%22hostAppName%22%3A%22registries%22%2C%22modulePrefix%22%3A%22registries%22%2C%22ember-cli-mirage%22%3A%7B%22enabled%22%3Afalse%7D%2C%22shareBaseUrl%22%3A%22http%3A//localhost%3A8003/%22%2C%22shareSearchUrl%22%3A%22http%3A//localhost%3A8003/api/v2/search/creativeworks/_search%22%2C%22indexPageRegistrationIds%22%3A%5B%226tsnj%22%2C%22aurjt%22%2C%22e94t8%22%2C%222tpy9%22%2C%222ds52%22%5D%2C%22externalRegistries%22%3A%5B%7B%22https%22%3Atrue%2C%22name%22%3A%22ClinicalTrials.gov%22%2C%22urlRegex%22%3A%22%5Ehttps%3F%3A//.*clinicaltrials%5C%5C.gov.*%24%22%7D%2C%7B%22https%22%3Atrue%2C%22name%22%3A%22Research%20Registry%22%2C%22urlRegex%22%3A%22%5Ehttps%3F%3A//.*researchregistry%5C%5C.com.*%24%22%7D%5D%2C%22externalLinks%22%3A%7B%22help%22%3A%22https%3A//help.osf.io%22%2C%22donate%22%3A%22https%3A//cos.io/donate%22%7D%2C%22defaultProviderId%22%3A%22osf%22%2C%22shareBaseURL%22%3A%22https%3A//share.osf.io%22%2C%22shareSearchBaseURL%22%3A%22https%3A//share.osf.io/api/v2/search%22%2C%22indexPageRegistrationsQuery%22%3A%226tsnj%20OR%20aurjt%20OR%20e94t8%20OR%202tpy9%20OR%202ds52%22%7D" name="registries/config/environment"/>
  <meta content="%7B%22bundles%22%3A%7B%22analytics-page%22%3A%7B%22assets%22%3A%5B%7B%22uri%22%3A%22/ember_osf_web/engines-dist/analytics-page/assets/engine-9668e4fa43feca5bc70e12d49fe985ba.js%22%2C%22type%22%3A%22js%22%7D%2C%7B%22uri%22%3A%22/ember_osf_web/engines-dist/analytics-page/assets/engine-a0be585cad6159bb64f0e5a4544b83de.css%22%2C%22type%22%3A%22css%22%7D%2C%7B%22uri%22%3A%22/ember_osf_web/engines-dist/analytics-page/assets/engine-vendor-71675773337fa332d53d4305af285c6c.js%22%2C%22type%22%3A%22js%22%7D%2C%7B%22uri%22%3A%22/ember_osf_web/engines-dist/analytics-page/assets/engine-vendor-9fd291a4fa2be50d9dbc2b116bfd16bb.css%22%2C%22type%22%3A%22css%22%7D%5D%7D%2C%22collections%22%3A%7B%22assets%22%3A%5B%7B%22uri%22%3A%22/ember_osf_web/engines-dist/collections/assets/engine-8b40c8c8eb69ea335e52514c2b4964d0.css%22%2C%22type%22%3A%22css%22%7D%2C%7B%22uri%22%3A%22/ember_osf_web/engines-dist/collections/assets/engine-e299731530657d52ec23e7abc814125e.js%22%2C%22type%22%3A%22js%22%7D%2C%7B%22uri%22%3A%22/ember_osf_web/engines-dist/collections/assets/engine-vendor-e7319fe74bd3249883e5ed9e77f8946c.js%22%2C%22type%22%3A%22js%22%7D%5D%7D%2C%22registries%22%3A%7B%22assets%22%3A%5B%7B%22uri%22%3A%22/ember_osf_web/engines-dist/registries/assets/engine-44ce030014d31f0177aaa0f1894e7e79.css%22%2C%22type%22%3A%22css%22%7D%2C%7B%22uri%22%3A%22/ember_osf_web/engines-dist/registries/assets/engine-4faaa823d5ef2202478285ea3e319f32.js%22%2C%22type%22%3A%22js%22%7D%2C%7B%22uri%22%3A%22/ember_osf_web/engines-dist/registries/assets/engine-vendor-277e371bb158083bbda5e1461a04ba28.js%22%2C%22type%22%3A%22js%22%7D%5D%7D%7D%7D" name="ember-osf-web/config/asset-manifest"/>
  <style type="text/css">
   .MathJax_Hover_Frame {border-radius: .25em; -webkit-border-radius: .25em; -moz-border-radius: .25em; -khtml-border-radius: .25em; box-shadow: 0px 0px 15px #83A; -webkit-box-shadow: 0px 0px 15px #83A; -moz-box-shadow: 0px 0px 15px #83A; -khtml-box-shadow: 0px 0px 15px #83A; border: 1px solid #A6D ! important; display: inline-block; position: absolute}
.MathJax_Menu_Button .MathJax_Hover_Arrow {position: absolute; cursor: pointer; display: inline-block; border: 2px solid #AAA; border-radius: 4px; -webkit-border-radius: 4px; -moz-border-radius: 4px; -khtml-border-radius: 4px; font-family: 'Courier New',Courier; font-size: 9px; color: #F0F0F0}
.MathJax_Menu_Button .MathJax_Hover_Arrow span {display: block; background-color: #AAA; border: 1px solid; border-radius: 3px; line-height: 0; padding: 4px}
.MathJax_Hover_Arrow:hover {color: white!important; border: 2px solid #CCC!important}
.MathJax_Hover_Arrow:hover span {background-color: #CCC!important}
  </style>
  <style type="text/css">
   #MathJax_About {position: fixed; left: 50%; width: auto; text-align: center; border: 3px outset; padding: 1em 2em; background-color: #DDDDDD; color: black; cursor: default; font-family: message-box; font-size: 120%; font-style: normal; text-indent: 0; text-transform: none; line-height: normal; letter-spacing: normal; word-spacing: normal; word-wrap: normal; white-space: nowrap; float: none; z-index: 201; border-radius: 15px; -webkit-border-radius: 15px; -moz-border-radius: 15px; -khtml-border-radius: 15px; box-shadow: 0px 10px 20px #808080; -webkit-box-shadow: 0px 10px 20px #808080; -moz-box-shadow: 0px 10px 20px #808080; -khtml-box-shadow: 0px 10px 20px #808080; filter: progid:DXImageTransform.Microsoft.dropshadow(OffX=2, OffY=2, Color='gray', Positive='true')}
#MathJax_About.MathJax_MousePost {outline: none}
.MathJax_Menu {position: absolute; background-color: white; color: black; width: auto; padding: 5px 0px; border: 1px solid #CCCCCC; margin: 0; cursor: default; font: menu; text-align: left; text-indent: 0; text-transform: none; line-height: normal; letter-spacing: normal; word-spacing: normal; word-wrap: normal; white-space: nowrap; float: none; z-index: 201; border-radius: 5px; -webkit-border-radius: 5px; -moz-border-radius: 5px; -khtml-border-radius: 5px; box-shadow: 0px 10px 20px #808080; -webkit-box-shadow: 0px 10px 20px #808080; -moz-box-shadow: 0px 10px 20px #808080; -khtml-box-shadow: 0px 10px 20px #808080; filter: progid:DXImageTransform.Microsoft.dropshadow(OffX=2, OffY=2, Color='gray', Positive='true')}
.MathJax_MenuItem {padding: 1px 2em; background: transparent}
.MathJax_MenuArrow {position: absolute; right: .5em; padding-top: .25em; color: #666666; font-size: .75em}
.MathJax_MenuActive .MathJax_MenuArrow {color: white}
.MathJax_MenuArrow.RTL {left: .5em; right: auto}
.MathJax_MenuCheck {position: absolute; left: .7em}
.MathJax_MenuCheck.RTL {right: .7em; left: auto}
.MathJax_MenuRadioCheck {position: absolute; left: .7em}
.MathJax_MenuRadioCheck.RTL {right: .7em; left: auto}
.MathJax_MenuLabel {padding: 1px 2em 3px 1.33em; font-style: italic}
.MathJax_MenuRule {border-top: 1px solid #DDDDDD; margin: 4px 3px}
.MathJax_MenuDisabled {color: GrayText}
.MathJax_MenuActive {background-color: #606872; color: white}
.MathJax_MenuDisabled:focus, .MathJax_MenuLabel:focus {background-color: #E8E8E8}
.MathJax_ContextMenu:focus {outline: none}
.MathJax_ContextMenu .MathJax_MenuItem:focus {outline: none}
#MathJax_AboutClose {top: .2em; right: .2em}
.MathJax_Menu .MathJax_MenuClose {top: -10px; left: -10px}
.MathJax_MenuClose {position: absolute; cursor: pointer; display: inline-block; border: 2px solid #AAA; border-radius: 18px; -webkit-border-radius: 18px; -moz-border-radius: 18px; -khtml-border-radius: 18px; font-family: 'Courier New',Courier; font-size: 24px; color: #F0F0F0}
.MathJax_MenuClose span {display: block; background-color: #AAA; border: 1.5px solid; border-radius: 18px; -webkit-border-radius: 18px; -moz-border-radius: 18px; -khtml-border-radius: 18px; line-height: 0; padding: 8px 0 6px}
.MathJax_MenuClose:hover {color: white!important; border: 2px solid #CCC!important}
.MathJax_MenuClose:hover span {background-color: #CCC!important}
.MathJax_MenuClose:hover:focus {outline: none}
  </style>
  <style type="text/css">
   .MathJax_Preview .MJXf-math {color: inherit!important}
  </style>
  <style type="text/css">
   .MJX_Assistive_MathML {position: absolute!important; top: 0; left: 0; clip: rect(1px, 1px, 1px, 1px); padding: 1px 0 0 0!important; border: 0!important; height: 1px!important; width: 1px!important; overflow: hidden!important; display: block!important; -webkit-touch-callout: none; -webkit-user-select: none; -khtml-user-select: none; -moz-user-select: none; -ms-user-select: none; user-select: none}
.MJX_Assistive_MathML.MJX_Assistive_MathML_Block {width: 100%!important}
  </style>
  <style type="text/css">
   #MathJax_Zoom {position: absolute; background-color: #F0F0F0; overflow: auto; display: block; z-index: 301; padding: .5em; border: 1px solid black; margin: 0; font-weight: normal; font-style: normal; text-align: left; text-indent: 0; text-transform: none; line-height: normal; letter-spacing: normal; word-spacing: normal; word-wrap: normal; white-space: nowrap; float: none; -webkit-box-sizing: content-box; -moz-box-sizing: content-box; box-sizing: content-box; box-shadow: 5px 5px 15px #AAAAAA; -webkit-box-shadow: 5px 5px 15px #AAAAAA; -moz-box-shadow: 5px 5px 15px #AAAAAA; -khtml-box-shadow: 5px 5px 15px #AAAAAA; filter: progid:DXImageTransform.Microsoft.dropshadow(OffX=2, OffY=2, Color='gray', Positive='true')}
#MathJax_ZoomOverlay {position: absolute; left: 0; top: 0; z-index: 300; display: inline-block; width: 100%; height: 100%; border: 0; padding: 0; margin: 0; background-color: white; opacity: 0; filter: alpha(opacity=0)}
#MathJax_ZoomFrame {position: relative; display: inline-block; height: 0; width: 0}
#MathJax_ZoomEventTrap {position: absolute; left: 0; top: 0; z-index: 302; display: inline-block; border: 0; padding: 0; margin: 0; background-color: white; opacity: 0; filter: alpha(opacity=0)}
  </style>
  <style type="text/css">
   .MathJax_Preview {color: #888}
#MathJax_Message {position: fixed; left: 1em; bottom: 1.5em; background-color: #E6E6E6; border: 1px solid #959595; margin: 0px; padding: 2px 8px; z-index: 102; color: black; font-size: 80%; width: auto; white-space: nowrap}
#MathJax_MSIE_Frame {position: absolute; top: 0; left: 0; width: 0px; z-index: 101; border: 0px; margin: 0px; padding: 0px}
.MathJax_Error {color: #CC0000; font-style: italic}
  </style>
  <style id="inert-style">
   [inert] {
  pointer-events: none;
  cursor: default;
}

[inert], [inert] * {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
  </style>
  <style type="text/css">
  </style>
  <style type="text/css">
  </style>
  <style type="text/css">
   .MJXp-script {font-size: .8em}
.MJXp-right {-webkit-transform-origin: right; -moz-transform-origin: right; -ms-transform-origin: right; -o-transform-origin: right; transform-origin: right}
.MJXp-bold {font-weight: bold}
.MJXp-italic {font-style: italic}
.MJXp-scr {font-family: MathJax_Script,'Times New Roman',Times,STIXGeneral,serif}
.MJXp-frak {font-family: MathJax_Fraktur,'Times New Roman',Times,STIXGeneral,serif}
.MJXp-sf {font-family: MathJax_SansSerif,'Times New Roman',Times,STIXGeneral,serif}
.MJXp-cal {font-family: MathJax_Caligraphic,'Times New Roman',Times,STIXGeneral,serif}
.MJXp-mono {font-family: MathJax_Typewriter,'Times New Roman',Times,STIXGeneral,serif}
.MJXp-largeop {font-size: 150%}
.MJXp-largeop.MJXp-int {vertical-align: -.2em}
.MJXp-math {display: inline-block; line-height: 1.2; text-indent: 0; font-family: 'Times New Roman',Times,STIXGeneral,serif; white-space: nowrap; border-collapse: collapse}
.MJXp-display {display: block; text-align: center; margin: 1em 0}
.MJXp-math span {display: inline-block}
.MJXp-box {display: block!important; text-align: center}
.MJXp-box:after {content: " "}
.MJXp-rule {display: block!important; margin-top: .1em}
.MJXp-char {display: block!important}
.MJXp-mo {margin: 0 .15em}
.MJXp-mfrac {margin: 0 .125em; vertical-align: .25em}
.MJXp-denom {display: inline-table!important; width: 100%}
.MJXp-denom > * {display: table-row!important}
.MJXp-surd {vertical-align: top}
.MJXp-surd > * {display: block!important}
.MJXp-script-box > *  {display: table!important; height: 50%}
.MJXp-script-box > * > * {display: table-cell!important; vertical-align: top}
.MJXp-script-box > *:last-child > * {vertical-align: bottom}
.MJXp-script-box > * > * > * {display: block!important}
.MJXp-mphantom {visibility: hidden}
.MJXp-munderover {display: inline-table!important}
.MJXp-over {display: inline-block!important; text-align: center}
.MJXp-over > * {display: block!important}
.MJXp-munderover > * {display: table-row!important}
.MJXp-mtable {vertical-align: .25em; margin: 0 .125em}
.MJXp-mtable > * {display: inline-table!important; vertical-align: middle}
.MJXp-mtr {display: table-row!important}
.MJXp-mtd {display: table-cell!important; text-align: center; padding: .5em 0 0 .5em}
.MJXp-mtr > .MJXp-mtd:first-child {padding-left: 0}
.MJXp-mtr:first-child > .MJXp-mtd {padding-top: 0}
.MJXp-mlabeledtr {display: table-row!important}
.MJXp-mlabeledtr > .MJXp-mtd:first-child {padding-left: 0}
.MJXp-mlabeledtr:first-child > .MJXp-mtd {padding-top: 0}
.MJXp-merror {background-color: #FFFF88; color: #CC0000; border: 1px solid #CC0000; padding: 1px 3px; font-style: normal; font-size: 90%}
.MJXp-scale0 {-webkit-transform: scaleX(.0); -moz-transform: scaleX(.0); -ms-transform: scaleX(.0); -o-transform: scaleX(.0); transform: scaleX(.0)}
.MJXp-scale1 {-webkit-transform: scaleX(.1); -moz-transform: scaleX(.1); -ms-transform: scaleX(.1); -o-transform: scaleX(.1); transform: scaleX(.1)}
.MJXp-scale2 {-webkit-transform: scaleX(.2); -moz-transform: scaleX(.2); -ms-transform: scaleX(.2); -o-transform: scaleX(.2); transform: scaleX(.2)}
.MJXp-scale3 {-webkit-transform: scaleX(.3); -moz-transform: scaleX(.3); -ms-transform: scaleX(.3); -o-transform: scaleX(.3); transform: scaleX(.3)}
.MJXp-scale4 {-webkit-transform: scaleX(.4); -moz-transform: scaleX(.4); -ms-transform: scaleX(.4); -o-transform: scaleX(.4); transform: scaleX(.4)}
.MJXp-scale5 {-webkit-transform: scaleX(.5); -moz-transform: scaleX(.5); -ms-transform: scaleX(.5); -o-transform: scaleX(.5); transform: scaleX(.5)}
.MJXp-scale6 {-webkit-transform: scaleX(.6); -moz-transform: scaleX(.6); -ms-transform: scaleX(.6); -o-transform: scaleX(.6); transform: scaleX(.6)}
.MJXp-scale7 {-webkit-transform: scaleX(.7); -moz-transform: scaleX(.7); -ms-transform: scaleX(.7); -o-transform: scaleX(.7); transform: scaleX(.7)}
.MJXp-scale8 {-webkit-transform: scaleX(.8); -moz-transform: scaleX(.8); -ms-transform: scaleX(.8); -o-transform: scaleX(.8); transform: scaleX(.8)}
.MJXp-scale9 {-webkit-transform: scaleX(.9); -moz-transform: scaleX(.9); -ms-transform: scaleX(.9); -o-transform: scaleX(.9); transform: scaleX(.9)}
.MathJax_PHTML .noError {vertical-align: ; font-size: 90%; text-align: left; color: black; padding: 1px 3px; border: 1px solid}
  </style>
 </head>
 <body class="ember-application">
  <div id="MathJax_Message" style="display: none;">
  </div>
  <script>
   var configJson = document.head.querySelector("meta[name$='/config/environment']").content;
var configObject = JSON.parse(unescape(configJson));
if (configObject.googleTagManagerId) {
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());
     gtag('config', configObject.googleTagManagerId);
}
  </script>
  <noscript>
   <p>
    For full functionality of this site it is necessary to enable JavaScript.
        Here are
    <a href="https://www.enable-javascript.com/" target="_blank">
     instructions for enabling JavaScript in your web browser
    </a>
    .
   </p>
  </noscript>
  <script>
   window.prerenderReady = false;
  </script>
  <script src="/ember_osf_web/assets/vendor-953889fd72b707c679b4300836fc8ffa.js">
  </script>
  <script src="/ember_osf_web/assets/chunk.475.5e0c22b78c056cfd9ee1.js">
  </script>
  <script src="/ember_osf_web/assets/chunk.143.0cd84cc60a0d8e631102.js">
  </script>
  <script src="/ember_osf_web/assets/ember-osf-web-a9451c28ce56801c14728084ff66e2cc.js">
  </script>
  <script src="https://cdn.ravenjs.com/3.22.1/ember/raven.min.js">
  </script>
  <script>
   var encodedConfig = document.head.querySelector("meta[name$='/config/environment']").content;
    var config = JSON.parse(unescape(encodedConfig));
    if (config.sentryDSN) {
        Raven.config(config.sentryDSN, config.sentryOptions || {}).install();
    }
  </script>
  <div id="ember-basic-dropdown-wormhole">
  </div>
 </body>
</html>
