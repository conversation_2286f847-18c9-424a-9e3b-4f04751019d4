# 🤖🔬 Integração Gemini AI + OSF.io no Servidor MCP

## 🎉 Funcionalidades Integradas

O servidor MCP agora inclui **integração completa entre Gemini AI e OSF.io**, permitindo discussões interativas sobre dados de pesquisa científica!

### 🌟 **Nova Aba Completa: "🔬 OSF.io Research Data + 🤖 Gemini"**

#### 📋 **4 Sub-abas com IA Integrada:**

### 1. 🔍 **Buscar Projetos + 🤖 Gemini**
- **Busca automática** no OSF.io
- **Sugestões inteligentes** de termos de busca via Gemini
- **Análise automática** dos resultados encontrados
- **Chat interativo** para discutir relevância e qualidade dos projetos
- **Interpretação especializada** de metodologias de pesquisa

### 2. 📥 **Download de Arquivos** (Mantido)
- **Download automático** de arquivos de projetos
- **Verificação de integridade** dos arquivos
- **Suporte a múltiplos formatos**

### 3. 📊 **Análise de Dados + 🤖 Gemini**
- **Análise automática** de dados baixados
- **Discussão interativa** sobre estatísticas e resultados
- **Interpretação especializada** de gráficos e métricas
- **Sugestões de análises** adicionais
- **Explicações metodológicas** detalhadas

### 4. 🚀 **Pipeline Completo + 🤖 Gemini**
- **Automação total**: Busca → Download → Análise → Discussão
- **Chat integrado** para discussão de todo o processo
- **Interpretação completa** dos resultados
- **Sugestões de próximos passos**

## 🛠️ **Funcionalidades do Gemini AI**

### 🧠 **Assistente Especializado**
- **Conhecimento científico** sobre metodologias de pesquisa
- **Interpretação estatística** de dados e gráficos
- **Sugestões inteligentes** de termos de busca
- **Análise crítica** da qualidade dos dados
- **Explicações didáticas** de conceitos complexos

### 💬 **Chat Interativo Contextual**
- **Contexto automático** baseado nos dados atuais
- **Histórico de conversação** mantido por sessão
- **Respostas especializadas** para cada tipo de análise
- **Sugestões proativas** de análises adicionais

### 🎯 **Exemplos de Perguntas para o Gemini**

#### Sobre Resultados de Busca:
- "Qual projeto parece mais relevante para minha pesquisa?"
- "Como avaliar a qualidade metodológica destes estudos?"
- "Que critérios usar para escolher entre estes datasets?"

#### Sobre Análise de Dados:
- "O que significam essas estatísticas descritivas?"
- "Como interpretar esta distribuição de dados?"
- "Há evidências de outliers ou problemas nos dados?"
- "Que análises adicionais você recomenda?"

#### Sobre Metodologia:
- "Como validar a qualidade deste dataset?"
- "Quais são as limitações metodológicas aparentes?"
- "Como comparar diferentes estudos na mesma área?"

## 🚀 **Como Usar**

### 1. **Configuração Inicial**
```bash
# Instalar dependência do Gemini
uv add google-generativeai

# Configurar API key (criar arquivo .env)
echo "GEMINI_API_KEY=sua_api_key_aqui" > .env

# Iniciar servidor
uv run python src/main.py
```

### 2. **Acessar Interface**
- **URL**: http://localhost:7860
- **Nova aba**: "🔬 OSF.io Research Data"
- **Sub-abas**: Todas agora incluem "🤖 Gemini"

### 3. **Fluxo de Trabalho Recomendado**

#### Opção A: Pipeline Automático com IA
1. Vá para **"🚀 Pipeline Completo + 🤖 Gemini"**
2. Digite termo de busca (ex: "machine learning")
3. Clique **"🚀 Executar Pipeline Completo"**
4. **Discuta os resultados** no chat com Gemini
5. **Faça perguntas** sobre interpretação e próximos passos

#### Opção B: Busca Inteligente
1. Vá para **"🔍 Buscar Projetos + 🤖 Gemini"**
2. Use **"🤖 Sugerir Termos com Gemini"** para ideias
3. Realize a busca
4. **Discuta os resultados** com Gemini
5. **Escolha o melhor projeto** baseado nas recomendações

## 🎯 **Exemplos Práticos**

### Exemplo 1: Pesquisa em Psicologia
```
1. Área de pesquisa: "psicologia cognitiva"
2. Gemini sugere: "cognitive psychology", "working memory", "attention"
3. Busca retorna 5 projetos relevantes
4. Gemini analisa: "O projeto X tem metodologia mais robusta..."
5. Download e análise do projeto escolhido
6. Gemini interpreta: "Os dados mostram distribuição normal..."
```

### Exemplo 2: Machine Learning
```
1. Pipeline automático: "machine learning"
2. Download de dataset médico (422 pacientes)
3. Análise automática: 35 variáveis, estatísticas descritivas
4. Chat com Gemini: "Como interpretar essas correlações?"
5. Gemini explica: "A correlação entre idade e pressão arterial..."
```

## 🧪 **Capacidades Avançadas do Gemini**

### Análise Metodológica
- **Avaliação de qualidade** dos estudos
- **Identificação de vieses** potenciais
- **Sugestões de controles** estatísticos
- **Interpretação de limitações**

### Interpretação Estatística
- **Explicação de distribuições**
- **Análise de correlações**
- **Identificação de outliers**
- **Sugestões de testes** apropriados

### Sugestões Inteligentes
- **Termos de busca** otimizados
- **Análises complementares**
- **Visualizações adicionais**
- **Próximos passos** de pesquisa

## ⚙️ **Configuração Técnica**

### Modelos Gemini Suportados
- **gemini-1.5-flash** (padrão, mais rápido)
- **gemini-1.5-pro** (fallback, mais avançado)
- **gemini-1.5-flash-latest** (mais recente)

### Contexto Especializado
```python
# O Gemini é configurado como especialista em:
- Análise de dados de pesquisa científica
- Metodologias de pesquisa
- Estatística descritiva e inferencial
- Interpretação de gráficos e visualizações
- Avaliação de qualidade de dados
- Sugestões de análises adicionais
```

### Integração Contextual
- **Busca**: Contexto dos projetos encontrados
- **Análise**: Contexto dos dados e estatísticas
- **Pipeline**: Contexto completo do processo
- **Histórico**: Conversação mantida por sessão

## 🔧 **Troubleshooting**

### Gemini não funciona
```bash
# Verificar instalação
uv add google-generativeai

# Verificar API key
echo $GEMINI_API_KEY

# Verificar arquivo .env
cat .env | grep GEMINI_API_KEY
```

### Respostas lentas
- **Modelo flash** é mais rápido que pro
- **Mensagens menores** respondem mais rápido
- **Contexto menor** melhora performance

### Erros de API
- **Verificar quota** da API Gemini
- **Verificar conectividade** com internet
- **Tentar novamente** após alguns segundos

## 🎉 **Benefícios da Integração**

### Para Pesquisadores
- **Assistente IA especializado** em ciência
- **Interpretação automática** de resultados
- **Sugestões inteligentes** de análises
- **Explicações didáticas** de conceitos

### Para Estudantes
- **Aprendizado interativo** sobre metodologia
- **Explicações detalhadas** de estatísticas
- **Orientação passo-a-passo** em análises
- **Feedback imediato** sobre interpretações

### Para Desenvolvedores
- **API completa** Gemini + OSF
- **Contexto automático** baseado em dados
- **Chat especializado** configurável
- **Integração extensível**

## 🚀 **Status da Integração**

### ✅ **Funcionalidades Implementadas**
- Chat contextual com Gemini
- Sugestões inteligentes de busca
- Análise automática de resultados
- Discussão interativa sobre dados
- Pipeline completo com IA
- Interface integrada no MCP

### 🔄 **Melhorias Futuras**
- **Cache de conversações**
- **Exportação de relatórios** com insights IA
- **Análise comparativa** entre datasets
- **Recomendações personalizadas**

---

## 📞 **Como Usar Agora**

1. **Iniciar servidor**: `uv run python src/main.py`
2. **Acessar**: http://localhost:7860
3. **Nova aba**: "🔬 OSF.io Research Data"
4. **Escolher sub-aba** com "🤖 Gemini"
5. **Começar a explorar** dados com IA!

**Status**: ✅ **Totalmente Funcional e Integrado com Gemini AI!**
