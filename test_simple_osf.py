"""
Teste simples do OSF Scraper
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_import():
    """Testa se consegue importar o módulo"""
    try:
        from osf_scraper import search_osf, OSFScraper
        print("✓ Importação bem-sucedida")
        return True
    except Exception as e:
        print(f"✗ Erro na importação: {e}")
        return False

def test_basic_functionality():
    """Testa funcionalidade básica sem Selenium"""
    try:
        from osf_scraper import search_osf
        
        print("Testando busca básica (sem Selenium)...")
        results = search_osf("python", max_results=2, use_selenium=False)
        
        print(f"Resultados encontrados: {len(results)}")
        
        if results:
            for i, result in enumerate(results, 1):
                print(f"{i}. {result.get('title', 'Sem título')}")
                print(f"   URL: {result.get('url', 'N/A')}")
        
        return True
        
    except Exception as e:
        print(f"✗ Erro no teste básico: {e}")
        return False

def test_selenium_availability():
    """Testa se Selenium está disponível"""
    try:
        from osf_scraper import SELENIUM_AVAILABLE
        if SELENIUM_AVAILABLE:
            print("✓ Selenium disponível")
        else:
            print("⚠ Selenium não disponível")
        return True
    except Exception as e:
        print(f"✗ Erro ao verificar Selenium: {e}")
        return False

if __name__ == "__main__":
    print("=== Teste Simples OSF Scraper ===")
    
    success = True
    success &= test_import()
    success &= test_selenium_availability()
    success &= test_basic_functionality()
    
    if success:
        print("\n✓ Todos os testes passaram!")
    else:
        print("\n✗ Alguns testes falharam.")
