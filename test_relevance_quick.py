#!/usr/bin/env python3
"""
Teste rápido da relevância da busca.
"""

import sys
import time

# Adicionar o diretório atual ao path
sys.path.append('.')

def test_quick_relevance():
    """Teste rápido de relevância."""
    print("🔍 Teste Rápido de Relevância")
    print("=" * 40)
    
    try:
        from gemini_mcp_server import gemini_server
        
        # Testes específicos
        tests = [
            {
                "query": "machine learning",
                "should_find": True,
                "expected_in_title": ["machine", "learning", "deep", "ai"]
            },
            {
                "query": "marcha humana",
                "should_find": True,
                "expected_in_title": ["gait", "movement", "biomechanics"]
            },
            {
                "query": "psychology",
                "should_find": True,
                "expected_in_title": ["psychology", "cognitive", "behavior"]
            },
            {
                "query": "termo inexistente xyz123",
                "should_find": False,
                "expected_in_title": []
            }
        ]
        
        for test in tests:
            query = test["query"]
            should_find = test["should_find"]
            expected = test["expected_in_title"]
            
            print(f"\n🔍 Testando: '{query}'")
            
            # Chamar função diretamente para ver os projetos
            gemini_server.search_osf_with_gemini(query, 3)
            
            if hasattr(gemini_server, 'current_search_results') and gemini_server.current_search_results:
                projects = gemini_server.current_search_results
                print(f"✅ Encontrou {len(projects)} projetos:")
                
                for i, proj in enumerate(projects, 1):
                    title = proj.get('title', 'Sem título')
                    score = proj.get('relevance_score', 0)
                    print(f"   {i}. {title} (score: {score})")
                
                # Verificar relevância
                if should_find:
                    relevant = False
                    for proj in projects:
                        title_lower = proj.get('title', '').lower()
                        if any(word in title_lower for word in expected):
                            relevant = True
                            break
                    
                    if relevant:
                        print("✅ Resultados relevantes encontrados")
                    else:
                        print("⚠️ Resultados não são totalmente relevantes")
                else:
                    print("⚠️ Encontrou resultados quando não deveria")
            else:
                print("❌ Nenhum projeto encontrado")
                if should_find:
                    print("⚠️ Deveria ter encontrado resultados")
                else:
                    print("✅ Correto - não deveria encontrar resultados")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Função principal."""
    print("🚀 TESTE RÁPIDO DE RELEVÂNCIA")
    print("=" * 50)
    
    # Aguardar servidor
    time.sleep(2)
    
    success = test_quick_relevance()
    
    print("\n" + "="*50)
    if success:
        print("✅ Teste concluído!")
        print("\n💡 Agora teste na interface:")
        print("1. Acesse: http://localhost:7861")
        print("2. Teste estas buscas:")
        print("   - 'Busque machine learning' → deve retornar projetos de ML/AI")
        print("   - 'Busque marcha humana' → deve retornar projetos de biomecânica")
        print("   - 'Busque psychology' → deve retornar projetos de psicologia")
        print("   - 'Busque xyz123' → deve dizer que não encontrou nada relevante")
    else:
        print("❌ Teste falhou")

if __name__ == "__main__":
    main()
