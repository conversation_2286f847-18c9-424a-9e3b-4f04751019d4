#!/usr/bin/env python3
"""
Teste simples dos gráficos avançados.
"""

import sys
import os

# Adicionar o diretório atual ao path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Testa todos os imports necessários."""
    print("🚀 Testando imports...")
    
    try:
        import numpy as np
        print("✅ NumPy OK")
    except Exception as e:
        print(f"❌ NumPy: {e}")
        return False

    try:
        import plotly.graph_objects as go
        print("✅ Plotly OK")
    except Exception as e:
        print(f"❌ Plotly: {e}")
        return False

    try:
        from src.quadratic_solver import solve_quadratic
        print("✅ Quadratic Solver OK")
    except Exception as e:
        print(f"❌ Quadratic Solver: {e}")
        return False

    try:
        from src.advanced_plotting import create_interactive_quadratic_plot
        print("✅ Advanced Plotting OK")
    except Exception as e:
        print(f"❌ Advanced Plotting: {e}")
        return False

    return True

def test_basic_functionality():
    """Testa funcionalidade básica."""
    print("\n🧮 Testando funcionalidade básica...")
    
    try:
        from src.quadratic_solver import solve_quadratic
        from src.advanced_plotting import create_interactive_quadratic_plot
        
        # Testar resolução básica
        solution = solve_quadratic(1, -5, 6)
        print(f"✅ Solução: {solution['roots_type']}")
        
        # Testar gráfico interativo
        fig = create_interactive_quadratic_plot(1, -5, 6, solution)
        print("✅ Gráfico interativo criado")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro na funcionalidade: {e}")
        return False

def main():
    """Função principal de teste."""
    print("=" * 50)
    print("🔬 TESTE DOS GRÁFICOS AVANÇADOS")
    print("=" * 50)
    
    # Teste de imports
    if not test_imports():
        print("\n❌ Falha nos imports. Abortando.")
        return
    
    # Teste de funcionalidade
    if not test_basic_functionality():
        print("\n❌ Falha na funcionalidade. Abortando.")
        return
    
    print("\n🎉 Todos os testes passaram!")
    print("✅ Sistema pronto para usar gráficos avançados!")

if __name__ == "__main__":
    main()
