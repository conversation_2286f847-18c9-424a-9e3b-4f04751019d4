#!/usr/bin/env python3
"""
Teste com projeto OSF que sabemos que tem arquivos
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from osf_scraper import download_osf_files

def test_projeto_conhecido():
    """Testa download do projeto conhecido com arquivos"""
    print("=== Teste com Projeto Conhecido ===")
    
    project_url = "https://osf.io/2zfu4/"
    download_dir = "downloads_projeto_conhecido"
    
    print(f"Projeto: {project_url}")
    print(f"Diretório de download: {download_dir}")
    print()
    
    try:
        print("Iniciando download...")
        downloaded_files = download_osf_files(project_url, download_dir)
        
        if downloaded_files:
            print(f"✓ {len(downloaded_files)} arquivos baixados com sucesso!")
            print()
            
            total_bytes = 0
            for i, file_info in enumerate(downloaded_files, 1):
                print(f"{i}. {file_info['name']}")
                print(f"   URL original: {file_info['original_url']}")
                print(f"   Arquivo local: {file_info['local_path']}")
                print(f"   Tamanho: {file_info.get('size', 'Unknown')}")
                print(f"   Bytes: {file_info['file_size_bytes']}")
                print(f"   Data: {file_info.get('date', 'Unknown')}")
                
                total_bytes += file_info['file_size_bytes']
                
                # Verificar se o arquivo existe no disco
                if os.path.exists(file_info['local_path']):
                    actual_size = os.path.getsize(file_info['local_path'])
                    print(f"   ✓ Arquivo salvo (tamanho real: {actual_size} bytes)")
                else:
                    print(f"   ✗ Arquivo não encontrado no disco!")
                
                print()
            
            print(f"Total baixado: {total_bytes} bytes ({total_bytes/1024:.1f} KB)")
            
        else:
            print("✗ Nenhum arquivo foi baixado")
            print("Possíveis causas:")
            print("- Projeto não tem arquivos públicos")
            print("- Erro na extração de links")
            print("- Problema de conectividade")
            
    except Exception as e:
        print(f"✗ Erro durante o download: {e}")
        import traceback
        traceback.print_exc()

def debug_projeto_conhecido():
    """Debug específico do projeto conhecido"""
    print("\n=== Debug do Projeto Conhecido ===")
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.chrome.service import Service
        from webdriver_manager.chrome import ChromeDriverManager
        from bs4 import BeautifulSoup
        import time
        
        # Configurar Chrome
        chrome_options = Options()
        chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)
        
        try:
            # Acessar página de arquivos
            files_url = "https://osf.io/2zfu4/files/osfstorage"
            print(f"Acessando: {files_url}")
            
            driver.get(files_url)
            time.sleep(10)  # Aguardar carregamento
            
            print(f"URL atual: {driver.current_url}")
            
            # Analisar HTML
            soup = BeautifulSoup(driver.page_source, 'html.parser')
            
            # Verificar se carregou corretamente
            page_text = soup.get_text().lower()
            if 'javascript' in page_text and 'enable' in page_text:
                print("⚠ Página ainda requer JavaScript")
            else:
                print("✓ Página carregou com JavaScript")
            
            # Procurar por todos os links
            all_links = soup.find_all('a', href=True)
            print(f"Total de links encontrados: {len(all_links)}")
            
            # Filtrar links de arquivo
            file_links = []
            for link in all_links:
                href = link.get('href', '')
                text = link.get_text(strip=True)
                
                if ('/files/osfstorage/' in href or 
                    'download' in href.lower() or
                    any(ext in href.lower() for ext in ['.pdf', '.csv', '.xlsx', '.txt', '.zip'])):
                    
                    if len(text) > 3:
                        file_links.append({
                            'text': text,
                            'href': href,
                            'full_url': href if href.startswith('http') else f"https://osf.io{href}"
                        })
            
            print(f"Links de arquivo encontrados: {len(file_links)}")
            
            for i, link in enumerate(file_links, 1):
                print(f"{i}. '{link['text']}'")
                print(f"   Href: {link['href']}")
                print(f"   URL completa: {link['full_url']}")
                print()
            
            # Procurar por elementos com classes específicas
            print("Procurando elementos com classes de arquivo...")
            
            file_elements = soup.find_all(class_=lambda x: x and any(
                keyword in str(x).lower() for keyword in ['file', 'item', 'row', 'list'] if x
            ))
            
            print(f"Elementos com classes relacionadas a arquivo: {len(file_elements)}")
            
            for i, elem in enumerate(file_elements[:5]):  # Mostrar apenas os primeiros 5
                classes = elem.get('class', [])
                text = elem.get_text(strip=True)[:100]
                print(f"{i+1}. Classes: {classes}")
                print(f"   Texto: {text}...")
                
                # Procurar links dentro do elemento
                elem_links = elem.find_all('a', href=True)
                for link in elem_links:
                    href = link.get('href', '')
                    if '/files/' in href or 'download' in href:
                        print(f"   Link interno: {href}")
                print()
            
        finally:
            driver.quit()
            
    except Exception as e:
        print(f"Erro durante debug: {e}")
        import traceback
        traceback.print_exc()

def main():
    """Função principal"""
    print("Teste com Projeto OSF Conhecido")
    print("=" * 50)
    print("Projeto: https://osf.io/2zfu4/")
    print("(Projeto indicado pelo usuário que tem arquivos)")
    print()
    
    # Executar teste
    test_projeto_conhecido()
    
    # Executar debug se necessário
    resposta = input("\nDeseja executar debug detalhado? (s/n): ").strip().lower()
    if resposta in ['s', 'sim', 'y', 'yes']:
        debug_projeto_conhecido()
    
    print("\n" + "=" * 50)
    print("Teste concluído!")
    
    # Verificar arquivos baixados
    download_dir = "downloads_projeto_conhecido"
    if os.path.exists(download_dir):
        files = os.listdir(download_dir)
        if files:
            print(f"\nArquivos baixados em {download_dir}:")
            for file in files:
                file_path = os.path.join(download_dir, file)
                size = os.path.getsize(file_path)
                print(f"  📁 {file} ({size} bytes)")
        else:
            print(f"\nDiretório {download_dir} está vazio")
    else:
        print(f"\nDiretório {download_dir} não foi criado")

if __name__ == "__main__":
    main()
