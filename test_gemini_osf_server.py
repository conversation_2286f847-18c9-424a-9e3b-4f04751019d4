#!/usr/bin/env python3
"""
Script de teste para o servidor Gemini + OSF MCP.
Testa as principais funcionalidades do servidor.
"""

import requests
import json
import time

def test_mcp_server():
    """Testa as funcionalidades do servidor MCP."""
    base_url = "http://localhost:7861"
    
    print("🧪 Testando Servidor Gemini + OSF MCP")
    print("=" * 50)
    
    # Verificar se o servidor está rodando
    try:
        response = requests.get(f"{base_url}/", timeout=5)
        if response.status_code == 200:
            print("✅ Servidor está rodando")
        else:
            print("❌ Servidor não está respondendo corretamente")
            return
    except requests.exceptions.RequestException:
        print("❌ Servidor não está acessível. Certifique-se de que está rodando.")
        return
    
    # Teste 1: Busca OSF
    print("\n🔍 Teste 1: Busca no OSF")
    test_search_osf()
    
    # Teste 2: Processamento de solicitação
    print("\n🤖 Teste 2: Processamento com Gemini")
    test_process_request()
    
    # Teste 3: Chat interativo
    print("\n💬 Teste 3: Chat interativo")
    test_interactive_chat()
    
    print("\n✅ Testes concluídos!")

def test_search_osf():
    """Testa a funcionalidade de busca OSF."""
    try:
        # Simular busca através da interface Gradio
        # Nota: Em um teste real, você usaria a API do Gradio
        print("   • Simulando busca por 'machine learning'...")
        print("   • Funcionalidade disponível através da interface web")
        print("   • URL: http://localhost:7861")
        
    except Exception as e:
        print(f"   ❌ Erro no teste de busca: {e}")

def test_process_request():
    """Testa o processamento de solicitações."""
    try:
        print("   • Testando processamento de linguagem natural...")
        print("   • Exemplos de comandos:")
        print("     - 'Buscar projetos sobre machine learning'")
        print("     - 'Download https://osf.io/j4bv6/'")
        print("     - 'Analisar dados baixados'")
        print("     - 'Criar gráfico dos dados'")
        
    except Exception as e:
        print(f"   ❌ Erro no teste de processamento: {e}")

def test_interactive_chat():
    """Testa o chat interativo."""
    try:
        print("   • Chat interativo com Gemini disponível")
        print("   • Perguntas científicas e metodológicas")
        print("   • Interpretação de resultados")
        
    except Exception as e:
        print(f"   ❌ Erro no teste de chat: {e}")

def show_usage_examples():
    """Mostra exemplos de uso do servidor."""
    print("\n📋 Exemplos de Uso:")
    print("-" * 30)
    
    examples = [
        {
            "comando": "Buscar machine learning",
            "descrição": "Busca projetos relacionados a machine learning no OSF"
        },
        {
            "comando": "Download https://osf.io/j4bv6/",
            "descrição": "Faz download dos arquivos do projeto especificado"
        },
        {
            "comando": "Analisar dados baixados",
            "descrição": "Analisa os dados baixados com estatísticas descritivas"
        },
        {
            "comando": "Criar histograma dos dados",
            "descrição": "Cria visualizações dos dados analisados"
        },
        {
            "comando": "Como interpretar estes resultados?",
            "descrição": "Chat científico para discussão dos resultados"
        }
    ]
    
    for i, example in enumerate(examples, 1):
        print(f"{i}. **{example['comando']}**")
        print(f"   → {example['descrição']}\n")

def check_dependencies():
    """Verifica as dependências do sistema."""
    print("\n🔍 Verificando Dependências:")
    print("-" * 30)
    
    dependencies = [
        ("gradio", "Interface web"),
        ("google.generativeai", "Gemini AI"),
        ("pandas", "Análise de dados"),
        ("matplotlib", "Gráficos"),
        ("requests", "Requisições HTTP"),
        ("beautifulsoup4", "Web scraping"),
        ("python-dotenv", "Variáveis de ambiente")
    ]
    
    for dep, desc in dependencies:
        try:
            __import__(dep.replace("-", "_"))
            print(f"✅ {dep} - {desc}")
        except ImportError:
            print(f"❌ {dep} - {desc} (não instalado)")

def main():
    """Função principal."""
    print("🚀 Teste do Servidor Gemini + OSF MCP")
    print("=" * 60)
    
    # Verificar dependências
    check_dependencies()
    
    # Testar servidor
    test_mcp_server()
    
    # Mostrar exemplos
    show_usage_examples()
    
    print("\n🎯 Para usar o servidor:")
    print("1. Certifique-se de que está rodando: python gemini_mcp_server.py")
    print("2. Acesse: http://localhost:7861")
    print("3. Digite comandos em linguagem natural")
    print("4. Use o chat para discussões científicas")
    
    print("\n📡 Configuração MCP para Claude Desktop:")
    mcp_config = {
        "mcpServers": {
            "gemini-osf-research": {
                "url": "http://localhost:7861/gradio_api/mcp/sse"
            }
        }
    }
    print(json.dumps(mcp_config, indent=2))

if __name__ == "__main__":
    main()
