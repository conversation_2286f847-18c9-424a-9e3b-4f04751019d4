#!/usr/bin/env python3
"""
Exemplo de uso do Gemini com o servidor MCP para resolver equações quadráticas.

Este script demonstra como usar o Google Gemini para interagir com o servidor MCP
e resolver equações quadráticas de forma inteligente.

Uso:
    export GOOGLE_API_KEY="sua_chave_aqui"
    python gemini_mcp_example.py
"""

import os
import json
import requests
import google.generativeai as genai
from typing import Dict, Any


class MCPQuadraticClient:
    """Cliente para interagir com o servidor MCP de equações quadráticas."""
    
    def __init__(self, mcp_url: str = "http://localhost:7861"):
        self.mcp_url = mcp_url
        self.base_api_url = f"{mcp_url}/gradio_api"
    
    def solve_quadratic(self, a: float, b: float, c: float) -> Dict[str, Any]:
        """
        Resolve uma equação quadrática usando o servidor MCP.
        
        Args:
            a, b, c: Coeficientes da equação ax² + bx + c = 0
        
        Returns:
            Resultado da resolução em formato JSON
        """
        try:
            # Fazer requisição para o servidor MCP
            response = requests.post(
                f"{self.base_api_url}/predict",
                json={
                    "fn_index": 0,  # Índice da função solve_quadratic_equation_mcp
                    "data": [str(a), str(b), str(c)]
                },
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                if "data" in result and len(result["data"]) > 0:
                    return json.loads(result["data"][0])
                else:
                    return {"error": "Resposta inválida do servidor"}
            else:
                return {"error": f"Erro HTTP {response.status_code}"}
                
        except Exception as e:
            return {"error": f"Erro na comunicação: {str(e)}"}
    
    def analyze_function(self, a: float, b: float, c: float) -> Dict[str, Any]:
        """
        Analisa uma função quadrática usando o servidor MCP.
        
        Args:
            a, b, c: Coeficientes da função f(x) = ax² + bx + c
        
        Returns:
            Análise da função em formato JSON
        """
        try:
            response = requests.post(
                f"{self.base_api_url}/predict",
                json={
                    "fn_index": 1,  # Índice da função analyze_quadratic_function
                    "data": [str(a), str(b), str(c)]
                },
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                if "data" in result and len(result["data"]) > 0:
                    return json.loads(result["data"][0])
                else:
                    return {"error": "Resposta inválida do servidor"}
            else:
                return {"error": f"Erro HTTP {response.status_code}"}
                
        except Exception as e:
            return {"error": f"Erro na comunicação: {str(e)}"}


class GeminiMCPAssistant:
    """Assistente que combina Gemini com o servidor MCP."""
    
    def __init__(self, api_key: str, mcp_url: str = "http://localhost:7861"):
        # Configurar Gemini
        genai.configure(api_key=api_key)
        self.model = genai.GenerativeModel('gemini-pro')
        
        # Configurar cliente MCP
        self.mcp_client = MCPQuadraticClient(mcp_url)
        
        # Prompt do sistema
        self.system_prompt = """
Você é um assistente matemático especializado em equações quadráticas. 
Você tem acesso a um servidor MCP que pode resolver equações quadráticas e analisar funções.

Quando o usuário pedir para resolver uma equação quadrática ou analisar uma função quadrática:
1. Extraia os coeficientes a, b, c da equação/função
2. Use as ferramentas MCP para obter a solução/análise
3. Explique os resultados de forma didática
4. Forneça insights matemáticos adicionais

Sempre seja educativo e explique os conceitos matemáticos envolvidos.
"""
    
    def extract_coefficients(self, text: str) -> tuple:
        """
        Extrai coeficientes de uma equação quadrática do texto usando Gemini.
        
        Args:
            text: Texto contendo a equação
        
        Returns:
            Tupla (a, b, c) com os coeficientes
        """
        extraction_prompt = f"""
Extraia os coeficientes a, b, c da equação quadrática no seguinte texto:
"{text}"

A equação deve estar na forma ax² + bx + c = 0 ou f(x) = ax² + bx + c

Responda APENAS com três números separados por vírgula, na ordem a,b,c
Exemplo: 1,-5,6

Texto: {text}
"""
        
        try:
            response = self.model.generate_content(extraction_prompt)
            coeffs = response.text.strip().split(',')
            
            if len(coeffs) == 3:
                a = float(coeffs[0].strip())
                b = float(coeffs[1].strip()) 
                c = float(coeffs[2].strip())
                return (a, b, c)
            else:
                raise ValueError("Não foi possível extrair 3 coeficientes")
                
        except Exception as e:
            print(f"Erro ao extrair coeficientes: {e}")
            return None
    
    def solve_equation(self, equation_text: str) -> str:
        """
        Resolve uma equação quadrática descrita em texto natural.
        
        Args:
            equation_text: Descrição da equação em linguagem natural
        
        Returns:
            Explicação completa da solução
        """
        # Extrair coeficientes
        coeffs = self.extract_coefficients(equation_text)
        if not coeffs:
            return "❌ Não consegui identificar os coeficientes da equação. Por favor, forneça a equação na forma ax² + bx + c = 0"
        
        a, b, c = coeffs
        
        # Resolver usando MCP
        mcp_result = self.mcp_client.solve_quadratic(a, b, c)
        
        if "error" in mcp_result:
            return f"❌ Erro ao resolver: {mcp_result['error']}"
        
        # Gerar explicação com Gemini
        explanation_prompt = f"""
Com base na seguinte solução de equação quadrática, forneça uma explicação didática completa:

Equação original: {equation_text}
Coeficientes extraídos: a={a}, b={b}, c={c}

Resultado do servidor MCP:
{json.dumps(mcp_result, indent=2, ensure_ascii=False)}

Forneça uma explicação que inclua:
1. A equação na forma padrão
2. Explicação do discriminante e seu significado
3. Interpretação das raízes encontradas
4. Conceitos matemáticos relevantes
5. Aplicações práticas (se houver)

Seja didático e educativo, explicando cada passo.
"""
        
        try:
            response = self.model.generate_content(explanation_prompt)
            return response.text
        except Exception as e:
            return f"Solução encontrada, mas erro na explicação: {str(e)}\n\nResultado bruto: {json.dumps(mcp_result, indent=2, ensure_ascii=False)}"
    
    def analyze_function(self, function_text: str) -> str:
        """
        Analisa uma função quadrática descrita em texto natural.
        
        Args:
            function_text: Descrição da função em linguagem natural
        
        Returns:
            Análise completa da função
        """
        # Extrair coeficientes
        coeffs = self.extract_coefficients(function_text)
        if not coeffs:
            return "❌ Não consegui identificar os coeficientes da função. Por favor, forneça a função na forma f(x) = ax² + bx + c"
        
        a, b, c = coeffs
        
        # Analisar usando MCP
        mcp_result = self.mcp_client.analyze_function(a, b, c)
        
        if "error" in mcp_result:
            return f"❌ Erro na análise: {mcp_result['error']}"
        
        # Gerar explicação com Gemini
        analysis_prompt = f"""
Com base na seguinte análise de função quadrática, forneça uma explicação didática completa:

Função original: {function_text}
Coeficientes extraídos: a={a}, b={b}, c={c}

Resultado da análise MCP:
{json.dumps(mcp_result, indent=2, ensure_ascii=False)}

Forneça uma explicação que inclua:
1. Propriedades da função (domínio, imagem, vértice)
2. Comportamento da função (crescimento/decrescimento)
3. Interceptações com os eixos
4. Concavidade e seu significado
5. Aplicações práticas da função

Seja didático e educativo, explicando cada conceito.
"""
        
        try:
            response = self.model.generate_content(analysis_prompt)
            return response.text
        except Exception as e:
            return f"Análise encontrada, mas erro na explicação: {str(e)}\n\nResultado bruto: {json.dumps(mcp_result, indent=2, ensure_ascii=False)}"


def main():
    """Função principal para demonstrar o uso."""
    print("🤖 Assistente Gemini + MCP para Equações Quadráticas")
    print("=" * 60)
    
    # Verificar chave da API
    api_key = os.getenv("GOOGLE_API_KEY")
    if not api_key:
        print("❌ Erro: Defina a variável GOOGLE_API_KEY")
        print("💡 Execute: export GOOGLE_API_KEY='sua_chave_aqui'")
        return
    
    # Criar assistente
    try:
        assistant = GeminiMCPAssistant(api_key)
        print("✅ Assistente Gemini + MCP inicializado com sucesso!")
    except Exception as e:
        print(f"❌ Erro ao inicializar assistente: {e}")
        return
    
    # Exemplos de uso
    examples = [
        "Resolva a equação x² - 5x + 6 = 0",
        "Analise a função f(x) = 2x² - 4x + 1",
        "Encontre as raízes de x² + x + 1 = 0",
        "Estude o comportamento da função y = -x² + 4x - 3"
    ]
    
    print("\n📚 Exemplos de uso:")
    for i, example in enumerate(examples, 1):
        print(f"{i}. {example}")
    
    print("\n" + "="*60)
    
    # Loop interativo
    while True:
        try:
            print("\n💬 Digite sua pergunta sobre equações quadráticas (ou 'sair' para terminar):")
            user_input = input("> ").strip()
            
            if user_input.lower() in ['sair', 'exit', 'quit']:
                print("👋 Até logo!")
                break
            
            if not user_input:
                continue
            
            print("\n🔄 Processando...")
            
            # Determinar se é resolução ou análise
            if any(word in user_input.lower() for word in ['resolva', 'resolve', 'raízes', 'raizes', 'solução']):
                result = assistant.solve_equation(user_input)
            elif any(word in user_input.lower() for word in ['analise', 'análise', 'estude', 'comportamento', 'função']):
                result = assistant.analyze_function(user_input)
            else:
                # Tentar resolver por padrão
                result = assistant.solve_equation(user_input)
            
            print("\n📊 Resultado:")
            print("-" * 40)
            print(result)
            print("-" * 40)
            
        except KeyboardInterrupt:
            print("\n👋 Interrompido pelo usuário. Até logo!")
            break
        except Exception as e:
            print(f"\n❌ Erro: {e}")


if __name__ == "__main__":
    main()
