# 🤖 Guia de Integração Gemini + MCP

Este guia explica como usar o Google Gemini com o servidor MCP para resolver equações quadráticas de forma inteligente.

## 🚀 Configuração Inicial

### 1. Dependências Instaladas

O projeto já inclui todas as dependências necessárias:

```toml
# pyproject.toml
dependencies = [
    "numpy>=1.21.0",
    "gradio[mcp]>=4.0.0", 
    "matplotlib>=3.5.0",
    "google-generativeai>=0.3.0",
]
```

### 2. Obter Chave da API do Gemini

1. Acesse [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Crie uma nova chave de API
3. Configure a variável de ambiente:

```bash
# Linux/macOS
export GOOGLE_API_KEY="sua_chave_aqui"

# Windows
set GOOGLE_API_KEY=sua_chave_aqui
```

## 🔧 Componentes do Sistema

### 1. Servidor MCP (`mcp_server.py`)

Expõe ferramentas matemáticas via Model Context Protocol:

- **`solve_quadratic_equation_mcp`**: Resolve equações quadráticas
- **`analyze_quadratic_function`**: Analisa propriedades da função
- **`find_quadratic_from_roots`**: Constrói equação a partir das raízes

### 2. Cliente Gemini (`gemini_mcp_example.py`)

Combina inteligência do Gemini com precisão do servidor MCP:

- **Extração de coeficientes**: Gemini interpreta linguagem natural
- **Cálculos precisos**: MCP server executa matemática exata
- **Explicações didáticas**: Gemini gera explicações educativas

## 🎯 Como Usar

### Passo 1: Iniciar o Servidor MCP

```bash
cd /home/<USER>/mcp
python mcp_server.py
```

**Saída esperada:**
```
🚀 Iniciando Servidor MCP - Equações Quadráticas...
✅ Módulo quadratic_solver carregado com sucesso
📡 URL: http://localhost:7861/gradio_api/mcp/sse
🌐 Interface Web: http://localhost:7861
```

### Passo 2: Executar o Assistente Gemini

```bash
# Configurar chave da API
export GOOGLE_API_KEY="sua_chave_aqui"

# Executar assistente
python gemini_mcp_example.py
```

### Passo 3: Interagir com o Assistente

**Exemplos de perguntas:**

```
> Resolva a equação x² - 5x + 6 = 0
> Analise a função f(x) = 2x² - 4x + 1  
> Encontre as raízes de x² + x + 1 = 0
> Estude o comportamento da função y = -x² + 4x - 3
```

## 🔄 Fluxo de Funcionamento

```mermaid
graph TD
    A[Usuário digita pergunta] --> B[Gemini extrai coeficientes]
    B --> C[Cliente MCP faz requisição]
    C --> D[Servidor MCP calcula resultado]
    D --> E[Gemini gera explicação didática]
    E --> F[Resposta completa ao usuário]
```

## 📊 Exemplo de Interação

**Entrada do usuário:**
```
"Resolva a equação x² - 5x + 6 = 0"
```

**Processamento:**
1. **Gemini** extrai: a=1, b=-5, c=6
2. **MCP Server** calcula: raízes x₁=3, x₂=2
3. **Gemini** explica: discriminante, significado das raízes, etc.

**Saída:**
```
📊 Resultado:
----------------------------------------
# Solução da Equação Quadrática x² - 5x + 6 = 0

## Análise dos Coeficientes
- **a = 1**: Coeficiente positivo indica parábola com concavidade para cima
- **b = -5**: Coeficiente linear negativo
- **c = 6**: Termo independente positivo

## Cálculo do Discriminante
Δ = b² - 4ac = (-5)² - 4(1)(6) = 25 - 24 = 1

Como Δ > 0, a equação possui **duas raízes reais distintas**.

## Raízes Encontradas
- **x₁ = 3**
- **x₂ = 2**

## Verificação
- f(3) = 3² - 5(3) + 6 = 9 - 15 + 6 = 0 ✓
- f(2) = 2² - 5(2) + 6 = 4 - 10 + 6 = 0 ✓

## Forma Fatorada
x² - 5x + 6 = (x - 3)(x - 2)

## Interpretação Geométrica
A parábola intercepta o eixo x nos pontos (2, 0) e (3, 0).
O vértice está em x = 2.5, y = -0.25.
----------------------------------------
```

## 🛠️ Personalização

### Modificar Prompts do Sistema

Edite `gemini_mcp_example.py`:

```python
self.system_prompt = """
Seu prompt personalizado aqui...
Defina como o assistente deve se comportar.
"""
```

### Adicionar Novas Ferramentas MCP

1. Crie nova função em `mcp_server.py`
2. Adicione à interface Gradio
3. Atualize o cliente em `gemini_mcp_example.py`

### Usar Outros Modelos Gemini

```python
# Em vez de 'gemini-pro'
self.model = genai.GenerativeModel('gemini-pro-vision')  # Para imagens
self.model = genai.GenerativeModel('gemini-1.5-pro')     # Versão mais avançada
```

## 🔍 Solução de Problemas

### Erro: "GOOGLE_API_KEY não definida"

```bash
# Verificar se a variável está definida
echo $GOOGLE_API_KEY

# Definir temporariamente
export GOOGLE_API_KEY="sua_chave_aqui"

# Definir permanentemente (Linux/macOS)
echo 'export GOOGLE_API_KEY="sua_chave_aqui"' >> ~/.bashrc
source ~/.bashrc
```

### Erro: "Servidor MCP não responde"

1. Verificar se o servidor está rodando:
   ```bash
   curl http://localhost:7861/gradio_api/mcp/schema
   ```

2. Reiniciar o servidor:
   ```bash
   python mcp_server.py
   ```

### Erro: "Não consegui extrair coeficientes"

O Gemini pode ter dificuldade com formatos não padrão. Use formatos claros:
- ✅ "x² - 5x + 6 = 0"
- ✅ "f(x) = 2x² - 4x + 1"
- ❌ "x ao quadrado menos cinco x mais seis igual zero"

## 📚 Recursos Adicionais

- [Documentação Google AI](https://ai.google.dev/)
- [Gradio MCP Guide](https://www.gradio.app/guides/building-mcp-server-with-gradio)
- [Model Context Protocol](https://modelcontextprotocol.io/)

## 🤝 Contribuição

Para adicionar novas funcionalidades:

1. Implemente a lógica matemática no servidor MCP
2. Crie interface no Gradio
3. Adicione integração no cliente Gemini
4. Teste com diferentes tipos de entrada
5. Documente o novo recurso

## 📄 Licença

Este projeto segue a mesma licença do projeto principal.
