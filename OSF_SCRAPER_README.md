# OSF.io Web Scraper

Este módulo fornece funcionalidades para fazer web scraping do site OSF.io (Open Science Framework), permitindo buscar e extrair informações de projetos de pesquisa.

## ⚠️ Importante

O OSF.io requer JavaScript para funcionar completamente. Este scraper usa uma abordagem híbrida:

1. **Primeira tentativa**: Usa `requests` simples (mais r<PERSON>pido, mas pode não funcionar)
2. **Fallback**: Usa `Selenium` com Chrome (mais lento, mas mais confiável)

## Funcionalidades

- **Busca automática** no site OSF.io usando a caixa de busca
- **Extração de informações** dos resultados (título, URL, autores, descrição, etc.)
- **Download de arquivos** de projetos OSF.io
- **Abordagem híbrida**: requests + Selenium como fallback
- **Suporte a modo headless** (sem interface gráfica)
- **Configuração flexível** de timeout e número de resultados
- **Verificação automática** se projetos têm arquivos disponíveis

## Dependências

As seguintes bibliotecas foram instaladas:

```bash
uv add selenium beautifulsoup4 webdriver-manager requests
```

- `requests`: Para requisições HTTP básicas
- `selenium`: Para automação do navegador web
- `beautifulsoup4`: Para parsing do HTML
- `webdriver-manager`: Para gerenciamento automático do ChromeDriver

## Uso Básico

### Função Simples (Recomendado)

```python
from src.osf_scraper import search_osf

# Busca simples com Selenium (mais confiável)
results = search_osf("machine learning", max_results=5, use_selenium=True)

for result in results:
    print(f"Título: {result.get('title')}")
    print(f"URL: {result.get('url')}")
    print(f"Autores: {result.get('authors')}")
    print("-" * 40)
```

### Classe OSFScraper

```python
from src.osf_scraper import OSFScraper

# Criar instância do scraper
scraper = OSFScraper(timeout=15, use_selenium=True)

# Realizar busca
results = scraper.search("data science", max_results=10)

# Processar resultados
for result in results:
    print(result)
```

### Download de Arquivos

```python
from src.osf_scraper import download_osf_files

# Fazer download dos arquivos de um projeto
project_url = "https://osf.io/j4bv6/"
downloaded_files = download_osf_files(project_url, download_dir="meus_downloads")

# Verificar resultados
if downloaded_files:
    for file_info in downloaded_files:
        print(f"Baixado: {file_info['name']}")
        print(f"Local: {file_info['local_path']}")
        print(f"Tamanho: {file_info['file_size_bytes']} bytes")
else:
    print("Projeto não tem arquivos disponíveis")
```

### Demo Interativo

Execute o demo para testar interativamente:

```bash
python demo_osf_scraper.py
```

## Parâmetros

### search_osf()

- `query` (str): Termo de busca
- `max_results` (int): Número máximo de resultados (padrão: 10)
- `timeout` (int): Tempo limite para requisições (padrão: 10 segundos)
- `use_selenium` (bool): Se True, usa Selenium quando requests falhar (padrão: True)

### OSFScraper()

- `timeout` (int): Tempo limite para requisições (segundos)
- `use_selenium` (bool): Se True, usa Selenium como fallback quando requests falhar

### download_osf_files()

- `project_url` (str): URL do projeto OSF (ex: https://osf.io/j4bv6/)
- `download_dir` (str): Diretório onde salvar os arquivos (padrão: "osf_downloads")
- `timeout` (int): Tempo limite para requisições

## Estrutura dos Resultados

### Resultados de Busca

Cada resultado de busca é um dicionário que pode conter:

```python
{
    'title': 'Título do projeto',
    'url': 'https://osf.io/xxxxx/',
    'description': 'Descrição do projeto...',
    'authors': 'Nome dos autores',
    'date': 'Data de publicação',
    'type': 'Tipo do projeto'
}
```

### Arquivos Baixados

Cada arquivo baixado retorna um dicionário com:

```python
{
    'name': 'Nome do arquivo',
    'original_url': 'https://osf.io/xxxxx/files/...',
    'download_url': 'URL de download direto',
    'local_path': 'caminho/local/arquivo.ext',
    'size': 'Tamanho legível (ex: 69.0 kB)',
    'date': 'Data de modificação',
    'file_size_bytes': 12345  # Tamanho em bytes
}
```

## Exemplos de Uso

### 1. Teste simples (Recomendado para começar)

```bash
python test_osf_simple.py
```

### 2. Pipeline completo (Recomendado)

```bash
python exemplo_pipeline_simples.py
```

### 3. Pipeline avançado

```bash
python pipeline_completo_osf.py
```

### 4. Exemplo de download

```bash
python exemplo_download_osf.py
```

### 3. Exemplo prático completo

```bash
python exemplo_uso_osf.py
```

### 3. Demo interativo

```bash
python demo_osf_scraper.py
```

### 4. Uso básico no código

```python
# Importar a função
from src.osf_scraper import search_osf

# Buscar por um termo específico
results = search_osf("python", max_results=3)

# Exibir resultados
for i, result in enumerate(results, 1):
    print(f"{i}. {result.get('title', 'Sem título')}")
    print(f"   URL: {result.get('url', 'N/A')}")
    if result.get('type'):
        print(f"   Tipo: {result['type']}")
    print()
```

### 5. Exemplo com múltiplas buscas

```python
termos = ["machine learning", "psychology", "data science"]

for termo in termos:
    print(f"Buscando: {termo}")
    results = search_osf(termo, max_results=2)

    for result in results:
        print(f"- {result.get('title', 'Sem título')}")
    print()
```

### 6. Download de arquivos de projeto

```python
from src.osf_scraper import download_osf_files

# Download de arquivos de um projeto específico
project_url = "https://osf.io/j4bv6/"
files = download_osf_files(project_url, download_dir="projeto_arquivos")

print(f"Baixados {len(files)} arquivos:")
for file_info in files:
    print(f"- {file_info['name']} ({file_info['file_size_bytes']} bytes)")
```

### 7. Pipeline completo em código

```python
from src.osf_scraper import search_osf, download_osf_files
import pandas as pd
import matplotlib.pyplot as plt

# 1. Buscar projetos
results = search_osf("data", max_results=3)

# 2. Fazer download
project_url = results[0]['url']  # Primeiro resultado
files = download_osf_files(project_url, "meus_dados")

# 3. Analisar dados
for file_info in files:
    if file_info['name'].endswith('.csv'):
        data = pd.read_csv(file_info['local_path'])

        # 4. Gerar gráfico
        plt.figure(figsize=(10, 6))
        data.hist()
        plt.savefig(f"grafico_{file_info['name']}.png")
        print(f"Gráfico salvo para {file_info['name']}")
```

### 8. Busca apenas com requests (mais rápida, mas pode falhar)

```python
# Busca sem Selenium (mais rápida, mas pode não funcionar)
results = search_osf("machine learning", max_results=5, use_selenium=False)
```

## Requisitos do Sistema

- Python 3.9+
- Google Chrome instalado
- Conexão com a internet

## Notas Importantes

1. **ChromeDriver**: O `webdriver-manager` instala automaticamente o ChromeDriver compatível
2. **Modo Headless**: Por padrão, o navegador executa sem interface gráfica para melhor performance
3. **Rate Limiting**: Evite fazer muitas requisições em sequência para não sobrecarregar o servidor
4. **JavaScript**: O OSF.io requer JavaScript, por isso usamos Selenium em vez de requests simples

## Tratamento de Erros

O scraper inclui tratamento de erros para:

- Timeout de elementos
- Problemas de conexão
- Elementos não encontrados
- Erros de parsing

## Limitações

- Depende da estrutura atual do site OSF.io
- Requer Chrome instalado no sistema
- Pode ser afetado por mudanças no layout do site
- Performance limitada pela velocidade de carregamento das páginas

## Troubleshooting

### Chrome não encontrado
```bash
# Ubuntu/Debian
sudo apt-get install google-chrome-stable

# CentOS/RHEL
sudo yum install google-chrome-stable
```

### Problemas de permissão
```bash
# Dar permissão de execução
chmod +x /path/to/chromedriver
```

### Timeout errors
- Aumentar o valor do parâmetro `timeout`
- Verificar conexão com a internet
- Tentar com `headless=False` para debug visual
