# 🚀 Guia R<PERSON>pido - Gemini + MCP

## ⚡ <PERSON><PERSON><PERSON> (3 passos)

### 1. Configurar API Key

```bash
# Copiar arquivo de exemplo
cp .env.example .env

# Editar .env e adicionar sua chave do Gemini
# GOOGLE_API_KEY=sua_chave_aqui
```

### 2. Executar Servidor

```bash
python gemini_mcp_server.py
```

### 3. Acessar Interface

Abra: http://localhost:7861

## 🤖 Como Usar o Assistente Gemini

### Exemplos de Perguntas

```
✅ "Resolva a equação x² - 5x + 6 = 0"
✅ "Encontre as raízes de 2x² + 3x - 1 = 0"  
✅ "Analise a função f(x) = x² - 4x + 4"
✅ "Qual é o vértice da parábola y = -x² + 4x - 3?"
```

### O que o Sistema Faz

1. **Gemini** interpreta sua pergunta em linguagem natural
2. **MCP Server** calcula a solução matemática precisa
3. **Gemini** gera uma explicação didática completa

## 🔧 Configuração MCP para Claude

```json
{
  "mcpServers": {
    "quadratic-solver": {
      "url": "http://localhost:7861/gradio_api/mcp/sse"
    }
  }
}
```

## 📊 Exemplo de Resposta

**Entrada:** "Resolva x² - 5x + 6 = 0"

**Saída:**
```
# 📐 Solução da Equação Quadrática

## Equação Analisada
x² - 5x + 6 = 0

## Coeficientes
- a = 1 (parábola para cima)
- b = -5 (termo linear)  
- c = 6 (termo independente)

## Discriminante
Δ = b² - 4ac = 25 - 24 = 1 > 0
✅ Duas raízes reais distintas

## Raízes
- x₁ = 3
- x₂ = 2

## Verificação
- f(3) = 9 - 15 + 6 = 0 ✓
- f(2) = 4 - 10 + 6 = 0 ✓

## Forma Fatorada
x² - 5x + 6 = (x - 3)(x - 2)
```

## 🛠️ Solução de Problemas

### Erro: "GOOGLE_API_KEY não encontrada"

1. Verifique se o arquivo `.env` existe
2. Confirme se a chave está correta no arquivo
3. Reinicie o servidor

### Erro: "Servidor não responde"

1. Verifique se a porta 7861 está livre
2. Reinicie o servidor
3. Teste: `curl http://localhost:7861`

### Gemini não funciona

1. Verifique sua chave da API do Gemini
2. Confirme se tem créditos disponíveis
3. Teste a chave em: https://makersuite.google.com/

## 📚 Recursos

- [Obter API Key do Gemini](https://makersuite.google.com/app/apikey)
- [Documentação Completa](./GEMINI_INTEGRATION_GUIDE.md)
- [Servidor MCP](./MCP_SERVER_README.md)
