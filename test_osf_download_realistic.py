#!/usr/bin/env python3
"""
Teste realista da funcionalidade de download do OSF Scraper
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from osf_scraper import download_osf_files, search_osf

def find_projects_with_files():
    """Busca projetos que provavelmente têm arquivos"""
    print("=== Buscando Projetos com Arquivos ===")
    
    # Termos que provavelmente retornam projetos com dados/arquivos
    search_terms = ["dataset", "data.csv", "analysis", "code"]
    
    projects_with_files = []
    
    for term in search_terms:
        print(f"Buscando por: '{term}'")
        
        try:
            results = search_osf(term, max_results=3, use_selenium=True)
            
            for result in results:
                url = result.get('url', '')
                title = result.get('title', '')
                result_type = result.get('type', '')
                
                # Filtrar por tipos que provavelmente têm arquivos
                if result_type in ['Project', 'File'] and url:
                    projects_with_files.append({
                        'title': title,
                        'url': url,
                        'type': result_type,
                        'search_term': term
                    })
                    
                    print(f"  ✓ {title} ({result_type})")
                    
        except Exception as e:
            print(f"  ✗ Erro na busca por '{term}': {e}")
    
    return projects_with_files

def test_download_with_validation():
    """Testa download com validação prévia"""
    print("\n=== Teste de Download com Validação ===")
    
    # URLs de projetos conhecidos (alguns podem ter arquivos, outros não)
    test_projects = [
        "https://osf.io/j4bv6/",  # Machine learning project
        "https://osf.io/8r4jz/",  # Python file
        "https://osf.io/nvc6w/",  # Python scripts
    ]
    
    for project_url in test_projects:
        print(f"\nTestando projeto: {project_url}")
        
        try:
            # Tentar fazer download
            downloaded_files = download_osf_files(project_url, download_dir=f"downloads_{project_url.split('/')[-2]}")
            
            if downloaded_files:
                print(f"✓ {len(downloaded_files)} arquivos baixados:")
                for file_info in downloaded_files:
                    print(f"  - {file_info['name']} ({file_info['file_size_bytes']} bytes)")
            else:
                print("ℹ Este projeto não tem arquivos disponíveis para download")
                
        except Exception as e:
            print(f"✗ Erro: {e}")

def check_project_has_files(project_url):
    """Verifica se um projeto tem arquivos antes de tentar baixar"""
    print(f"\n=== Verificando Arquivos em {project_url} ===")
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.chrome.service import Service
        from webdriver_manager.chrome import ChromeDriverManager
        from bs4 import BeautifulSoup
        import time
        
        # Configurar Chrome
        chrome_options = Options()
        chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)
        
        try:
            # Acessar página principal do projeto
            driver.get(project_url)
            time.sleep(5)
            
            # Verificar se há link para Files
            soup = BeautifulSoup(driver.page_source, 'html.parser')
            
            # Procurar por links de Files
            files_links = soup.find_all('a', href=True)
            has_files_tab = False
            
            for link in files_links:
                href = link.get('href', '')
                text = link.get_text(strip=True).lower()
                
                if 'files' in text and '/files/' in href:
                    has_files_tab = True
                    print(f"✓ Aba Files encontrada: {href}")
                    break
            
            if not has_files_tab:
                print("✗ Aba Files não encontrada")
                return False
            
            # Acessar a aba Files
            files_url = project_url.rstrip('/') + '/files/'
            driver.get(files_url)
            time.sleep(8)
            
            # Verificar se há arquivos
            soup_files = BeautifulSoup(driver.page_source, 'html.parser')
            
            # Procurar por indicadores de arquivos
            page_text = soup_files.get_text().lower()
            
            if 'no files' in page_text or 'empty' in page_text:
                print("ℹ Projeto não tem arquivos")
                return False
            
            # Procurar por links de download ou arquivo
            all_links = soup_files.find_all('a', href=True)
            file_count = 0
            
            for link in all_links:
                href = link.get('href', '')
                text = link.get_text(strip=True)
                
                if ('/files/osfstorage/' in href or 
                    'download' in href.lower() or
                    any(ext in href.lower() for ext in ['.pdf', '.csv', '.xlsx', '.txt', '.zip'])):
                    
                    if len(text) > 3 and not any(skip in text.lower() for skip in ['files', 'storage', 'filter']):
                        file_count += 1
                        print(f"  Arquivo encontrado: {text}")
            
            if file_count > 0:
                print(f"✓ {file_count} arquivo(s) encontrado(s)")
                return True
            else:
                print("ℹ Nenhum arquivo específico encontrado")
                return False
                
        finally:
            driver.quit()
            
    except Exception as e:
        print(f"✗ Erro ao verificar arquivos: {e}")
        return False

def main():
    """Função principal de teste"""
    print("OSF Scraper - Teste Realista de Download")
    print("=" * 60)
    
    # 1. Buscar projetos que podem ter arquivos
    projects = find_projects_with_files()
    
    if projects:
        print(f"\n✓ Encontrados {len(projects)} projetos potenciais")
        
        # 2. Testar alguns projetos
        for i, project in enumerate(projects[:2]):  # Testar apenas os primeiros 2
            print(f"\n--- Testando Projeto {i+1} ---")
            print(f"Título: {project['title']}")
            print(f"URL: {project['url']}")
            print(f"Tipo: {project['type']}")
            
            # Verificar se tem arquivos
            has_files = check_project_has_files(project['url'])
            
            if has_files:
                print("Tentando download...")
                try:
                    downloaded = download_osf_files(project['url'], f"download_test_{i+1}")
                    if downloaded:
                        print(f"✓ Download bem-sucedido: {len(downloaded)} arquivos")
                    else:
                        print("ℹ Download não retornou arquivos")
                except Exception as e:
                    print(f"✗ Erro no download: {e}")
            else:
                print("⏭ Pulando projeto sem arquivos")
    else:
        print("✗ Nenhum projeto encontrado")
    
    # 3. Teste com URLs conhecidas
    test_download_with_validation()
    
    print("\n" + "=" * 60)
    print("Teste concluído!")

if __name__ == "__main__":
    main()
