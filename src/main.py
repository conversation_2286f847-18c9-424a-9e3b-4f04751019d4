"""
Aplicação principal usando NumPy e Gradio.

Esta aplicação demonstra várias funcionalidades do NumPy através de uma interface
web interativa criada com Gradio.
"""

import numpy as np
import gradio as gr
import matplotlib.pyplot as plt
from typing import Tuple, List, Union
import io
import base64
import cmath
import json
import math
from datetime import datetime
from src.quadratic_solver import solve_quadratic, format_solution, plot_quadratic
from src.advanced_plotting import (
    create_interactive_quadratic_plot,
    create_3d_surface_plot,
    create_discriminant_analysis,
    create_family_comparison,
    create_roots_histogram,
    create_animated_parabola,
    create_completing_square_visualization,
    create_properties_comparison_chart,
    create_solution_method_visualization
)
from src.osf_scraper import search_osf, download_osf_files
import pandas as pd
import seaborn as sns
import os
import zipfile
import json

# Importar Google Gemini
try:
    import google.generativeai as genai
    HAS_GEMINI = True
except ImportError:
    HAS_GEMINI = False
    print("⚠️  google-generativeai não encontrado. Funcionalidades do Gemini para OSF desabilitadas.")

# Classe para integração Gemini + OSF
class GeminiOSFAssistant:
    """Assistente que combina Gemini com funcionalidades OSF"""

    def __init__(self):
        self.gemini_model = None
        self.chat_session = None
        self.conversation_history = []
        self.current_search_results = None
        self.current_downloaded_files = None
        self.current_analysis = None

        # Configurar Gemini se disponível
        if HAS_GEMINI:
            self._setup_gemini()

    def _setup_gemini(self):
        """Configura o modelo Gemini"""
        try:
            # Tentar carregar API key do arquivo .env
            api_key = os.getenv('GEMINI_API_KEY')
            if not api_key:
                # Tentar carregar de arquivo
                try:
                    with open('.env', 'r') as f:
                        for line in f:
                            if line.startswith('GEMINI_API_KEY='):
                                api_key = line.split('=', 1)[1].strip()
                                break
                except:
                    pass

            if api_key:
                genai.configure(api_key=api_key)
                try:
                    self.gemini_model = genai.GenerativeModel('gemini-1.5-flash')
                    print("✅ Google Gemini configurado para OSF (gemini-1.5-flash)")
                except:
                    try:
                        self.gemini_model = genai.GenerativeModel('gemini-1.5-pro')
                        print("✅ Google Gemini configurado para OSF (gemini-1.5-pro)")
                    except:
                        self.gemini_model = genai.GenerativeModel('models/gemini-1.5-flash-latest')
                        print("✅ Google Gemini configurado para OSF (gemini-1.5-flash-latest)")
        except Exception as e:
            print(f"❌ Erro ao configurar Gemini para OSF: {e}")
            self.gemini_model = None

    def start_chat_session(self):
        """Inicia uma nova sessão de chat para OSF"""
        if not self.gemini_model:
            return False

        try:
            system_prompt = """
            Você é um assistente especializado em análise de dados de pesquisa científica e OSF.io (Open Science Framework).
            Você pode:
            1. Ajudar a interpretar resultados de busca no OSF.io
            2. Sugerir termos de busca relevantes para encontrar dados específicos
            3. Analisar e explicar datasets baixados
            4. Interpretar estatísticas descritivas e gráficos
            5. Sugerir análises adicionais baseadas nos dados
            6. Explicar metodologias de pesquisa
            7. Discutir implicações dos resultados encontrados

            Sempre seja científico, preciso e forneça explicações detalhadas.
            Use emojis para tornar as explicações mais interessantes.
            Quando apropriado, sugira visualizações ou análises adicionais.
            Foque em aspectos metodológicos e científicos dos dados.
            """

            self.chat_session = self.gemini_model.start_chat(history=[])
            self.conversation_history = []

            # Enviar prompt inicial
            self.chat_session.send_message(system_prompt)
            return True

        except Exception as e:
            print(f"Erro ao iniciar chat OSF: {e}")
            return False

    def discuss_search_results(self, message: str, search_results: list) -> str:
        """Discute resultados de busca com Gemini"""
        if not self.gemini_model:
            return "❌ Gemini não está disponível para discussão."

        try:
            # Atualizar contexto atual
            self.current_search_results = search_results

            # Construir contexto dos resultados
            results_context = "Resultados da busca OSF.io:\n"
            for i, result in enumerate(search_results[:5], 1):
                results_context += f"{i}. {result.get('title', 'Sem título')}\n"
                results_context += f"   Tipo: {result.get('type', 'Unknown')}\n"
                results_context += f"   URL: {result.get('url', 'N/A')}\n"
                if result.get('authors'):
                    results_context += f"   Autores: {result['authors']}\n"
                results_context += "\n"

            enhanced_message = f"""
            Contexto atual - {results_context}

            Pergunta do usuário: {message}

            Por favor, analise os resultados da busca e responda à pergunta do usuário.
            Considere aspectos como relevância dos projetos, qualidade dos dados,
            metodologias de pesquisa, e sugira próximos passos se apropriado.
            """

            if not self.chat_session:
                self.start_chat_session()

            response = self.chat_session.send_message(enhanced_message)

            # Salvar na história
            self.conversation_history.append({
                'user': message,
                'assistant': response.text,
                'context': 'search_results',
                'timestamp': datetime.now().strftime('%H:%M:%S')
            })

            return response.text

        except Exception as e:
            return f"❌ Erro na discussão: {str(e)}"

    def discuss_data_analysis(self, message: str, analysis_text: str, datasets: list) -> str:
        """Discute análise de dados com Gemini"""
        if not self.gemini_model:
            return "❌ Gemini não está disponível para discussão."

        try:
            # Atualizar contexto atual
            self.current_analysis = analysis_text

            # Construir contexto da análise
            analysis_context = f"Análise atual dos dados:\n{analysis_text}\n\n"

            if datasets:
                analysis_context += "Datasets disponíveis:\n"
                for dataset in datasets:
                    analysis_context += f"- {dataset['name']}: {dataset['formato']} ({dataset['tipo']})\n"

            enhanced_message = f"""
            Contexto atual - {analysis_context}

            Pergunta do usuário: {message}

            Por favor, analise os dados e responda à pergunta do usuário.
            Considere aspectos estatísticos, metodológicos, limitações dos dados,
            e sugira análises adicionais ou interpretações se apropriado.
            """

            if not self.chat_session:
                self.start_chat_session()

            response = self.chat_session.send_message(enhanced_message)

            # Salvar na história
            self.conversation_history.append({
                'user': message,
                'assistant': response.text,
                'context': 'data_analysis',
                'timestamp': datetime.now().strftime('%H:%M:%S')
            })

            return response.text

        except Exception as e:
            return f"❌ Erro na discussão: {str(e)}"

    def suggest_search_terms(self, research_area: str) -> str:
        """Sugere termos de busca para uma área de pesquisa"""
        if not self.gemini_model:
            return "❌ Gemini não está disponível."

        try:
            prompt = f"""
            Como especialista em pesquisa científica, sugira termos de busca eficazes
            para encontrar dados relevantes no OSF.io na área de: {research_area}

            Forneça:
            1. 5-7 termos de busca específicos
            2. Combinações de palavras-chave
            3. Termos técnicos relevantes
            4. Variações em inglês (se aplicável)

            Foque em termos que provavelmente retornarão projetos com dados analisáveis.
            """

            response = self.gemini_model.generate_content(prompt)
            return response.text

        except Exception as e:
            return f"❌ Erro ao gerar sugestões: {str(e)}"

    def explain_dataset(self, dataset_info: dict) -> str:
        """Explica um dataset específico"""
        if not self.gemini_model:
            return "❌ Gemini não está disponível."

        try:
            prompt = f"""
            Como especialista em análise de dados, explique este dataset de forma didática:

            {json.dumps(dataset_info, indent=2, ensure_ascii=False)}

            Inclua:
            1. Interpretação das dimensões e estrutura
            2. Significado dos tipos de dados
            3. Qualidade dos dados (valores ausentes, etc.)
            4. Possíveis análises que podem ser feitas
            5. Limitações e considerações metodológicas

            Seja didático e use emojis para tornar mais interessante.
            """

            response = self.gemini_model.generate_content(prompt)
            return response.text

        except Exception as e:
            return f"❌ Erro na explicação: {str(e)}"

# Instância global do assistente OSF
gemini_osf_assistant = GeminiOSFAssistant()


def solve_quadratic_equation(a: float, b: float, c: float) -> Tuple[str, str]:
    """
    Resolve uma equação de segundo grau ax² + bx + c = 0.

    Args:
        a: Coeficiente de x²
        b: Coeficiente de x
        c: Termo independente

    Returns:
        Tuple com resultado detalhado e gráfico da função
    """
    # Usar o módulo separado para resolver a equação
    solution = solve_quadratic(a, b, c)
    result_text = format_solution(solution)
    plot_fig = plot_quadratic(a, b, c, solution)

    return result_text, plot_fig


def create_advanced_interactive_plot(a: float, b: float, c: float):
    """Cria gráfico interativo avançado usando Plotly."""
    try:
        solution = solve_quadratic(a, b, c)
        fig = create_interactive_quadratic_plot(a, b, c, solution)
        return fig
    except Exception as e:
        return f"Erro ao criar gráfico: {str(e)}"


def create_3d_analysis(a_min: float, a_max: float, b_min: float, b_max: float, c_fixed: float):
    """Cria análise 3D dos coeficientes."""
    try:
        fig = create_3d_surface_plot((a_min, a_max), (b_min, b_max), c_fixed)
        return fig
    except Exception as e:
        return f"Erro ao criar análise 3D: {str(e)}"


def create_discriminant_plot(a_min: float, a_max: float, b_min: float, b_max: float, c_min: float, c_max: float):
    """Cria visualização do discriminante."""
    try:
        fig = create_discriminant_analysis((a_min, a_max), (b_min, b_max), (c_min, c_max))
        return fig
    except Exception as e:
        return f"Erro ao criar análise do discriminante: {str(e)}"


def create_family_plot(base_a: float, base_b: float, base_c: float,
                      var1_a: float, var1_b: float, var1_c: float,
                      var2_a: float, var2_b: float, var2_c: float,
                      var3_a: float, var3_b: float, var3_c: float):
    """Cria comparação de família de equações."""
    try:
        base_coeffs = (base_a, base_b, base_c)
        variations = [(var1_a, var1_b, var1_c), (var2_a, var2_b, var2_c), (var3_a, var3_b, var3_c)]
        fig = create_family_comparison(base_coeffs, variations)
        return fig
    except Exception as e:
        return f"Erro ao criar comparação de família: {str(e)}"


def create_animation_plot(a_start: float, a_end: float, num_steps: int, b: float, c: float):
    """Cria animação da parábola."""
    try:
        a_values = np.linspace(a_start, a_end, num_steps)
        fig = create_animated_parabola(a_values.tolist(), b, c)
        return fig
    except Exception as e:
        return f"Erro ao criar animação: {str(e)}"


def create_completing_square_plot(a: float, b: float, c: float):
    """Cria visualização de completar quadrados."""
    try:
        fig = create_completing_square_visualization(a, b, c)
        return fig
    except Exception as e:
        return f"Erro ao criar visualização de completar quadrados: {str(e)}"


def create_methods_plot(a: float, b: float, c: float):
    """Cria visualização dos métodos de resolução."""
    try:
        fig = create_solution_method_visualization(a, b, c)
        return fig
    except Exception as e:
        return f"Erro ao criar visualização dos métodos: {str(e)}"


def generate_random_array(size: int, distribution: str) -> Tuple[str, str]:
    """
    Gera um array aleatório com a distribuição especificada.
    
    Args:
        size: Tamanho do array
        distribution: Tipo de distribuição ('normal', 'uniform', 'exponential')
    
    Returns:
        Tuple com estatísticas do array e gráfico
    """
    if distribution == "normal":
        arr = np.random.normal(0, 1, size)
    elif distribution == "uniform":
        arr = np.random.uniform(-1, 1, size)
    elif distribution == "exponential":
        arr = np.random.exponential(1, size)
    else:
        arr = np.random.random(size)
    
    # Calcular estatísticas
    stats = f"""
    📊 Estatísticas do Array:
    • Tamanho: {arr.size}
    • Média: {np.mean(arr):.4f}
    • Desvio Padrão: {np.std(arr):.4f}
    • Mínimo: {np.min(arr):.4f}
    • Máximo: {np.max(arr):.4f}
    • Mediana: {np.median(arr):.4f}
    """
    
    # Criar gráfico
    plt.figure(figsize=(10, 6))
    plt.subplot(1, 2, 1)
    plt.hist(arr, bins=30, alpha=0.7, color='skyblue', edgecolor='black')
    plt.title(f'Histograma - Distribuição {distribution.title()}')
    plt.xlabel('Valor')
    plt.ylabel('Frequência')
    
    plt.subplot(1, 2, 2)
    plt.plot(arr[:min(100, len(arr))], 'o-', markersize=3, linewidth=1)
    plt.title('Primeiros 100 valores')
    plt.xlabel('Índice')
    plt.ylabel('Valor')
    
    plt.tight_layout()
    
    return stats, plt


def matrix_operations(matrix_a: str, matrix_b: str, operation: str) -> str:
    """
    Realiza operações entre matrizes.
    
    Args:
        matrix_a: Matriz A como string
        matrix_b: Matriz B como string
        operation: Tipo de operação
    
    Returns:
        Resultado da operação
    """
    try:
        # Converter strings para arrays NumPy
        a = np.array(eval(matrix_a))
        b = np.array(eval(matrix_b))
        
        if operation == "Adição":
            result = a + b
            op_symbol = "+"
        elif operation == "Subtração":
            result = a - b
            op_symbol = "-"
        elif operation == "Multiplicação":
            result = a * b
            op_symbol = "*"
        elif operation == "Multiplicação de Matrizes":
            result = np.dot(a, b)
            op_symbol = "@"
        elif operation == "Divisão":
            result = a / b
            op_symbol = "/"
        else:
            return "Operação não suportada"
        
        return f"""
        🔢 Operação: {operation}
        
        Matriz A:
        {a}
        
        {op_symbol}
        
        Matriz B:
        {b}
        
        =
        
        Resultado:
        {result}
        
        📏 Dimensões do resultado: {result.shape}
        """
        
    except Exception as e:
        return f"❌ Erro: {str(e)}\n\nVerifique se as matrizes estão no formato correto: [[1,2],[3,4]]"


def fourier_analysis(signal_type: str, frequency: float, noise_level: float) -> Tuple[str, str]:
    """
    Demonstra análise de Fourier com NumPy.
    
    Args:
        signal_type: Tipo de sinal
        frequency: Frequência do sinal
        noise_level: Nível de ruído
    
    Returns:
        Informações do sinal e gráficos
    """
    # Gerar sinal
    t = np.linspace(0, 1, 1000)
    
    if signal_type == "Senoidal":
        signal = np.sin(2 * np.pi * frequency * t)
    elif signal_type == "Cossenoidal":
        signal = np.cos(2 * np.pi * frequency * t)
    elif signal_type == "Quadrada":
        signal = np.sign(np.sin(2 * np.pi * frequency * t))
    else:
        signal = np.sin(2 * np.pi * frequency * t)
    
    # Adicionar ruído
    noise = np.random.normal(0, noise_level, len(signal))
    noisy_signal = signal + noise
    
    # Análise de Fourier
    fft = np.fft.fft(noisy_signal)
    freqs = np.fft.fftfreq(len(t), t[1] - t[0])
    
    # Informações
    info = f"""
    🌊 Análise de Fourier:
    • Tipo de sinal: {signal_type}
    • Frequência: {frequency} Hz
    • Nível de ruído: {noise_level}
    • Pontos do sinal: {len(signal)}
    • Frequência dominante: {freqs[np.argmax(np.abs(fft[:len(fft)//2]))]} Hz
    """
    
    # Gráficos
    plt.figure(figsize=(12, 8))
    
    plt.subplot(2, 2, 1)
    plt.plot(t[:200], signal[:200], 'b-', label='Sinal original')
    plt.title('Sinal Original')
    plt.xlabel('Tempo (s)')
    plt.ylabel('Amplitude')
    plt.grid(True)
    
    plt.subplot(2, 2, 2)
    plt.plot(t[:200], noisy_signal[:200], 'r-', label='Sinal com ruído')
    plt.title('Sinal com Ruído')
    plt.xlabel('Tempo (s)')
    plt.ylabel('Amplitude')
    plt.grid(True)
    
    plt.subplot(2, 2, 3)
    plt.plot(freqs[:len(freqs)//2], np.abs(fft[:len(fft)//2]))
    plt.title('Espectro de Frequência')
    plt.xlabel('Frequência (Hz)')
    plt.ylabel('Magnitude')
    plt.grid(True)
    
    plt.subplot(2, 2, 4)
    plt.specgram(noisy_signal, Fs=1/(t[1]-t[0]))
    plt.title('Espectrograma')
    plt.xlabel('Tempo (s)')
    plt.ylabel('Frequência (Hz)')
    
    plt.tight_layout()
    
    return info, plt


def osf_search_projects(query: str, max_results: int) -> str:
    """
    Busca projetos no OSF.io

    Args:
        query: Termo de busca
        max_results: Número máximo de resultados

    Returns:
        Resultados formatados da busca
    """
    try:
        results = search_osf(query, max_results=max_results, use_selenium=True)

        if not results:
            return f"❌ Nenhum projeto encontrado para '{query}'"

        output = f"🔍 **Busca por '{query}'** - {len(results)} resultados encontrados:\n\n"

        for i, result in enumerate(results, 1):
            title = result.get('title', 'Sem título')
            url = result.get('url', 'N/A')
            project_type = result.get('type', 'Unknown')
            authors = result.get('authors', 'N/A')

            output += f"**{i}. {title}**\n"
            output += f"   • URL: {url}\n"
            output += f"   • Tipo: {project_type}\n"
            if authors != 'N/A':
                output += f"   • Autores: {authors}\n"
            output += "\n"

        return output

    except Exception as e:
        return f"❌ Erro durante a busca: {str(e)}"


def osf_download_files(project_url: str, download_dir: str = "osf_downloads") -> str:
    """
    Faz download dos arquivos de um projeto OSF

    Args:
        project_url: URL do projeto OSF
        download_dir: Diretório de download

    Returns:
        Resultado do download
    """
    try:
        if not project_url.strip():
            return "❌ Por favor, forneça uma URL válida do projeto OSF"

        # Garantir que a URL está no formato correto
        if not project_url.startswith('http'):
            project_url = f"https://osf.io/{project_url.strip('/')}/"

        downloaded_files = download_osf_files(project_url, download_dir)

        if not downloaded_files:
            return f"ℹ️ Projeto não tem arquivos disponíveis para download\n\nURL: {project_url}"

        output = f"✅ **Download concluído!**\n\n"
        output += f"📁 Projeto: {project_url}\n"
        output += f"📂 Diretório: {download_dir}\n"
        output += f"📥 Arquivos baixados: {len(downloaded_files)}\n\n"

        total_bytes = 0
        for i, file_info in enumerate(downloaded_files, 1):
            name = file_info['name']
            size_bytes = file_info['file_size_bytes']
            local_path = file_info['local_path']

            total_bytes += size_bytes
            size_mb = size_bytes / (1024 * 1024)

            output += f"**{i}. {name}**\n"
            output += f"   • Tamanho: {size_bytes:,} bytes ({size_mb:.2f} MB)\n"
            output += f"   • Local: {local_path}\n"

            # Verificar se arquivo existe
            if os.path.exists(local_path):
                output += f"   • Status: ✅ Salvo com sucesso\n"
            else:
                output += f"   • Status: ❌ Erro ao salvar\n"
            output += "\n"

        total_mb = total_bytes / (1024 * 1024)
        output += f"📊 **Total baixado:** {total_bytes:,} bytes ({total_mb:.2f} MB)"

        return output

    except Exception as e:
        return f"❌ Erro durante o download: {str(e)}"


def osf_analyze_data(download_dir: str = "osf_downloads") -> Tuple[str, str]:
    """
    Analisa os dados baixados e gera gráficos

    Args:
        download_dir: Diretório com os arquivos baixados

    Returns:
        Tuple com análise textual e gráfico
    """
    try:
        if not os.path.exists(download_dir):
            return f"❌ Diretório '{download_dir}' não encontrado", None

        # Procurar por arquivos de dados
        data_files = []
        for root, dirs, files in os.walk(download_dir):
            for file in files:
                if file.lower().endswith(('.csv', '.xlsx', '.xls', '.json')):
                    data_files.append(os.path.join(root, file))

        if not data_files:
            return f"ℹ️ Nenhum arquivo de dados encontrado em '{download_dir}'", None

        analysis_text = f"📊 **Análise de Dados**\n\n"
        analysis_text += f"📂 Diretório: {download_dir}\n"
        analysis_text += f"📄 Arquivos de dados encontrados: {len(data_files)}\n\n"

        # Analisar cada arquivo
        datasets = []
        for file_path in data_files:
            file_name = os.path.basename(file_path)
            analysis_text += f"**📄 {file_name}**\n"

            try:
                # Carregar dados baseado na extensão
                if file_path.lower().endswith('.csv'):
                    data = pd.read_csv(file_path)
                    file_type = "CSV"
                elif file_path.lower().endswith(('.xlsx', '.xls')):
                    data = pd.read_excel(file_path)
                    file_type = "Excel"
                elif file_path.lower().endswith('.json'):
                    with open(file_path, 'r') as f:
                        json_data = json.load(f)
                    if isinstance(json_data, list):
                        data = pd.DataFrame(json_data)
                        file_type = "JSON"
                    else:
                        continue

                # Informações básicas
                analysis_text += f"   • Tipo: {file_type}\n"
                analysis_text += f"   • Dimensões: {data.shape[0]:,} linhas × {data.shape[1]:,} colunas\n"
                analysis_text += f"   • Tamanho: {data.memory_usage(deep=True).sum() / 1024:.1f} KB\n"

                # Tipos de dados
                type_counts = data.dtypes.value_counts()
                analysis_text += f"   • Tipos de dados: {dict(type_counts)}\n"

                # Valores ausentes
                missing_count = data.isnull().sum().sum()
                analysis_text += f"   • Valores ausentes: {missing_count:,}\n"

                # Colunas numéricas
                numeric_cols = data.select_dtypes(include=[np.number]).columns
                analysis_text += f"   • Colunas numéricas: {len(numeric_cols)}\n"

                if len(numeric_cols) > 0:
                    analysis_text += f"   • Estatísticas básicas:\n"
                    stats = data[numeric_cols].describe()
                    for col in numeric_cols[:3]:  # Mostrar apenas as primeiras 3
                        mean_val = stats.loc['mean', col]
                        std_val = stats.loc['std', col]
                        analysis_text += f"     - {col}: média={mean_val:.2f}, std={std_val:.2f}\n"

                datasets.append({
                    'name': file_name,
                    'data': data,
                    'type': file_type
                })

                analysis_text += "\n"

            except Exception as e:
                analysis_text += f"   ❌ Erro ao analisar: {str(e)}\n\n"

        # Gerar gráfico se houver dados
        if datasets:
            plt.figure(figsize=(15, 10))

            # Determinar layout baseado no número de datasets
            n_datasets = len(datasets)
            if n_datasets == 1:
                rows, cols = 2, 2
            elif n_datasets <= 4:
                rows, cols = 2, 2
            else:
                rows, cols = 3, 2

            plot_idx = 1

            for i, dataset in enumerate(datasets[:4]):  # Máximo 4 datasets
                data = dataset['data']
                name = dataset['name']

                # Gráfico 1: Informações gerais
                if plot_idx <= rows * cols:
                    plt.subplot(rows, cols, plot_idx)
                    info_text = f"Dataset: {name[:20]}...\n"
                    info_text += f"Linhas: {data.shape[0]:,}\n"
                    info_text += f"Colunas: {data.shape[1]:,}\n"
                    info_text += f"Tipo: {dataset['type']}\n"
                    info_text += f"Memória: {data.memory_usage(deep=True).sum() / 1024:.1f} KB"

                    plt.text(0.1, 0.9, info_text, transform=plt.gca().transAxes,
                            fontsize=10, verticalalignment='top', fontfamily='monospace')
                    plt.title(f'Info: {name[:15]}...')
                    plt.axis('off')
                    plot_idx += 1

                # Gráfico 2: Distribuição numérica (se houver)
                if plot_idx <= rows * cols:
                    numeric_cols = data.select_dtypes(include=[np.number]).columns
                    if len(numeric_cols) > 0:
                        plt.subplot(rows, cols, plot_idx)
                        col = numeric_cols[0]
                        data[col].hist(bins=20, alpha=0.7)
                        plt.title(f'Distribuição: {col[:15]}...')
                        plt.xlabel(col)
                        plt.ylabel('Frequência')
                    else:
                        plt.subplot(rows, cols, plot_idx)
                        plt.text(0.5, 0.5, 'Sem dados\nnuméricos', ha='center', va='center')
                        plt.title('Distribuição')
                        plt.axis('off')
                    plot_idx += 1

            plt.tight_layout()
            return analysis_text, plt
        else:
            return analysis_text, None

    except Exception as e:
        return f"❌ Erro durante a análise: {str(e)}", None


def osf_pipeline_complete(query: str, max_results: int = 3) -> Tuple[str, str]:
    """
    Pipeline completo: Busca → Download → Análise

    Args:
        query: Termo de busca
        max_results: Número máximo de resultados

    Returns:
        Tuple com resultado textual e gráfico
    """
    try:
        # Etapa 1: Busca
        results = search_osf(query, max_results=max_results, use_selenium=True)

        if not results:
            return f"❌ Nenhum projeto encontrado para '{query}'", None

        output = f"🚀 **PIPELINE COMPLETO OSF.io**\n\n"
        output += f"🔍 **ETAPA 1: Busca**\n"
        output += f"Termo: '{query}'\n"
        output += f"Resultados: {len(results)} projetos encontrados\n\n"

        # Tentar download de projetos até encontrar dados
        download_dir = f"pipeline_osf_{query.replace(' ', '_')}"
        dados_encontrados = False

        for i, result in enumerate(results[:3]):  # Tentar até 3 projetos
            if result.get('type') == 'Project':
                output += f"📥 **ETAPA 2: Download (Tentativa {i+1})**\n"
                output += f"Projeto: {result.get('title', 'Sem título')[:50]}...\n"
                output += f"URL: {result.get('url', 'N/A')}\n"

                try:
                    downloaded_files = download_osf_files(result['url'], download_dir)

                    if downloaded_files:
                        output += f"✅ {len(downloaded_files)} arquivos baixados\n\n"

                        # Etapa 3: Análise
                        output += f"📊 **ETAPA 3: Análise**\n"
                        analysis_text, plot = osf_analyze_data(download_dir)

                        if "Nenhum arquivo de dados encontrado" not in analysis_text:
                            output += analysis_text
                            dados_encontrados = True
                            return output, plot
                        else:
                            output += "⚠️ Arquivos baixados não contêm dados analisáveis\n\n"
                    else:
                        output += "ℹ️ Projeto não tem arquivos disponíveis\n\n"

                except Exception as e:
                    output += f"❌ Erro no download: {str(e)}\n\n"

        if not dados_encontrados:
            output += "💡 **Fallback: Usando projeto conhecido com dados**\n"
            known_project_url = "https://osf.io/j4bv6/"
            output += f"URL: {known_project_url}\n"

            try:
                downloaded_files = download_osf_files(known_project_url, download_dir)
                if downloaded_files:
                    output += f"✅ {len(downloaded_files)} arquivos baixados\n\n"
                    analysis_text, plot = osf_analyze_data(download_dir)
                    output += analysis_text
                    return output, plot
            except Exception as e:
                output += f"❌ Erro no fallback: {str(e)}\n"

        return output + "\n❌ Não foi possível encontrar dados analisáveis", None

    except Exception as e:
        return f"❌ Erro no pipeline: {str(e)}", None


def osf_chat_with_gemini(message: str, chat_history: List, context_type: str = "general") -> Tuple[List, str]:
    """
    Chat interativo com Gemini sobre dados OSF

    Args:
        message: Mensagem do usuário
        chat_history: Histórico do chat
        context_type: Tipo de contexto (search, analysis, general)

    Returns:
        Tuple com histórico atualizado e campo de input limpo
    """
    if not message.strip():
        return chat_history, ""

    if not gemini_osf_assistant.gemini_model:
        response = "❌ Gemini não está disponível. Verifique se a API key está configurada."
        chat_history.append([message, response])
        return chat_history, ""

    try:
        # Iniciar sessão se necessário
        if not gemini_osf_assistant.chat_session:
            gemini_osf_assistant.start_chat_session()

        # Responder baseado no contexto
        if context_type == "search" and gemini_osf_assistant.current_search_results:
            response = gemini_osf_assistant.discuss_search_results(message, gemini_osf_assistant.current_search_results)
        elif context_type == "analysis" and gemini_osf_assistant.current_analysis:
            response = gemini_osf_assistant.discuss_data_analysis(message, gemini_osf_assistant.current_analysis, [])
        else:
            # Chat geral sobre OSF/pesquisa
            enhanced_message = f"""
            Pergunta sobre OSF.io ou análise de dados de pesquisa: {message}

            Por favor, responda como um especialista em ciência de dados e pesquisa científica.
            """

            response = gemini_osf_assistant.chat_session.send_message(enhanced_message)
            response = response.text

        # Adicionar ao histórico
        chat_history.append([message, response])
        return chat_history, ""

    except Exception as e:
        error_response = f"❌ Erro no chat: {str(e)}"
        chat_history.append([message, error_response])
        return chat_history, ""


def osf_search_with_gemini_discussion(query: str, max_results: int) -> Tuple[str, List]:
    """
    Busca OSF com discussão Gemini integrada

    Args:
        query: Termo de busca
        max_results: Número máximo de resultados

    Returns:
        Tuple com resultados da busca e chat history inicial
    """
    # Fazer busca normal
    search_result = osf_search_projects(query, max_results)

    # Extrair resultados para o Gemini
    if "❌" not in search_result and gemini_osf_assistant.gemini_model:
        try:
            # Simular extração de resultados (em implementação real, seria melhor passar os dados diretamente)
            results = search_osf(query, max_results=max_results, use_selenium=True)
            gemini_osf_assistant.current_search_results = results

            # Gerar análise inicial com Gemini
            initial_analysis = gemini_osf_assistant.discuss_search_results(
                f"Analise estes resultados de busca para '{query}' e forneça insights sobre a relevância e qualidade dos projetos encontrados.",
                results
            )

            # Criar histórico inicial
            initial_history = [
                [f"Análise dos resultados para '{query}'", initial_analysis]
            ]

            return search_result, initial_history

        except Exception as e:
            return search_result, [[f"Erro na análise inicial", f"❌ {str(e)}"]]

    return search_result, []


def osf_analyze_with_gemini_discussion(download_dir: str) -> Tuple[str, str, List]:
    """
    Análise OSF com discussão Gemini integrada

    Args:
        download_dir: Diretório com dados

    Returns:
        Tuple com análise textual, gráfico e chat history inicial
    """
    # Fazer análise normal
    analysis_text, plot = osf_analyze_data(download_dir)

    # Gerar discussão inicial com Gemini
    if "❌" not in analysis_text and gemini_osf_assistant.gemini_model:
        try:
            gemini_osf_assistant.current_analysis = analysis_text

            # Gerar análise inicial com Gemini
            initial_analysis = gemini_osf_assistant.discuss_data_analysis(
                "Analise estes dados e forneça insights sobre a qualidade, estrutura e possíveis análises que podem ser realizadas.",
                analysis_text,
                []
            )

            # Criar histórico inicial
            initial_history = [
                ["Análise dos dados baixados", initial_analysis]
            ]

            return analysis_text, plot, initial_history

        except Exception as e:
            return analysis_text, plot, [["Erro na análise inicial", f"❌ {str(e)}"]]

    return analysis_text, plot, []


def osf_suggest_search_terms_gemini(research_area: str) -> str:
    """
    Sugere termos de busca usando Gemini

    Args:
        research_area: Área de pesquisa

    Returns:
        Sugestões de termos de busca
    """
    if not research_area.strip():
        return "Por favor, especifique uma área de pesquisa."

    return gemini_osf_assistant.suggest_search_terms(research_area)


def create_interface():
    """Cria a interface Gradio."""
    
    with gr.Blocks(title="NumPy + Gradio Demo", theme=gr.themes.Soft()) as demo:
        gr.Markdown("""
        # 🔢 NumPy + Gradio Demo

        Esta aplicação demonstra várias funcionalidades do NumPy através de uma interface interativa.
        Explore as diferentes abas para ver exemplos de:
        - Geração de arrays aleatórios
        - Operações com matrizes
        - Resolução de equações quadráticas
        - Análise de Fourier
        """)
        
        with gr.Tabs():
            # Aba 1: Arrays Aleatórios
            with gr.TabItem("🎲 Arrays Aleatórios"):
                gr.Markdown("### Geração e Análise de Arrays Aleatórios")
                
                with gr.Row():
                    with gr.Column():
                        size_input = gr.Slider(
                            minimum=10, maximum=10000, value=1000, step=10,
                            label="Tamanho do Array"
                        )
                        dist_input = gr.Dropdown(
                            choices=["normal", "uniform", "exponential"],
                            value="normal",
                            label="Distribuição"
                        )
                        generate_btn = gr.Button("Gerar Array", variant="primary")
                    
                    with gr.Column():
                        stats_output = gr.Textbox(
                            label="Estatísticas",
                            lines=8,
                            max_lines=10
                        )
                
                plot_output = gr.Plot(label="Visualização")
                
                generate_btn.click(
                    fn=generate_random_array,
                    inputs=[size_input, dist_input],
                    outputs=[stats_output, plot_output]
                )
            
            # Aba 2: Operações com Matrizes
            with gr.TabItem("🔢 Operações com Matrizes"):
                gr.Markdown("### Operações Matemáticas entre Matrizes")
                gr.Markdown("**Formato das matrizes:** `[[1,2],[3,4]]` para matriz 2x2")
                
                with gr.Row():
                    with gr.Column():
                        matrix_a_input = gr.Textbox(
                            label="Matriz A",
                            value="[[1,2],[3,4]]",
                            placeholder="[[1,2],[3,4]]"
                        )
                        matrix_b_input = gr.Textbox(
                            label="Matriz B",
                            value="[[5,6],[7,8]]",
                            placeholder="[[5,6],[7,8]]"
                        )
                        operation_input = gr.Dropdown(
                            choices=["Adição", "Subtração", "Multiplicação", "Multiplicação de Matrizes", "Divisão"],
                            value="Adição",
                            label="Operação"
                        )
                        calc_btn = gr.Button("Calcular", variant="primary")
                    
                    with gr.Column():
                        result_output = gr.Textbox(
                            label="Resultado",
                            lines=15,
                            max_lines=20
                        )
                
                calc_btn.click(
                    fn=matrix_operations,
                    inputs=[matrix_a_input, matrix_b_input, operation_input],
                    outputs=[result_output]
                )
            
            # Aba 3: Equações Quadráticas
            with gr.TabItem("📐 Equações Quadráticas"):
                gr.Markdown("### Resolução de Equações de Segundo Grau")
                gr.Markdown("**Formato:** ax² + bx + c = 0")

                with gr.Row():
                    with gr.Column():
                        gr.Markdown("#### Coeficientes da Equação")
                        a_input = gr.Number(
                            label="Coeficiente a (x²)",
                            value=1,
                            info="Se a = 0, será uma equação linear"
                        )
                        b_input = gr.Number(
                            label="Coeficiente b (x)",
                            value=-5,
                            info="Coeficiente do termo linear"
                        )
                        c_input = gr.Number(
                            label="Coeficiente c (termo independente)",
                            value=6,
                            info="Termo constante da equação"
                        )
                        solve_btn = gr.Button("Resolver Equação", variant="primary")

                        # Exemplos pré-definidos
                        gr.Markdown("#### 📚 Exemplos Rápidos")
                        with gr.Row():
                            example1_btn = gr.Button("x² - 5x + 6 = 0", size="sm")
                            example2_btn = gr.Button("x² - 4x + 4 = 0", size="sm")
                        with gr.Row():
                            example3_btn = gr.Button("x² + x + 1 = 0", size="sm")
                            example4_btn = gr.Button("2x - 4 = 0", size="sm")

                    with gr.Column():
                        equation_result = gr.Textbox(
                            label="Solução da Equação",
                            lines=12,
                            max_lines=15
                        )

                equation_plot = gr.Plot(label="Gráfico da Função")

                # Eventos dos botões
                solve_btn.click(
                    fn=solve_quadratic_equation,
                    inputs=[a_input, b_input, c_input],
                    outputs=[equation_result, equation_plot]
                )

                # Exemplos pré-definidos
                example1_btn.click(
                    lambda: (1, -5, 6),
                    outputs=[a_input, b_input, c_input]
                )
                example2_btn.click(
                    lambda: (1, -4, 4),
                    outputs=[a_input, b_input, c_input]
                )
                example3_btn.click(
                    lambda: (1, 1, 1),
                    outputs=[a_input, b_input, c_input]
                )
                example4_btn.click(
                    lambda: (0, 2, -4),
                    outputs=[a_input, b_input, c_input]
                )

            # Aba 4: Gráficos Avançados
            with gr.TabItem("📊 Gráficos Avançados"):
                gr.Markdown("### Visualizações Avançadas e Interativas")

                with gr.Tabs():
                    # Sub-aba 1: Gráfico Interativo
                    with gr.TabItem("🎯 Gráfico Interativo"):
                        with gr.Row():
                            with gr.Column():
                                gr.Markdown("#### Coeficientes da Equação")
                                adv_a = gr.Number(label="a", value=1)
                                adv_b = gr.Number(label="b", value=-5)
                                adv_c = gr.Number(label="c", value=6)
                                interactive_btn = gr.Button("Criar Gráfico Interativo", variant="primary")

                            with gr.Column():
                                interactive_plot = gr.Plot(label="Gráfico Interativo Plotly")

                        interactive_btn.click(
                            fn=create_advanced_interactive_plot,
                            inputs=[adv_a, adv_b, adv_c],
                            outputs=[interactive_plot]
                        )

                    # Sub-aba 2: Análise 3D
                    with gr.TabItem("🌐 Análise 3D"):
                        with gr.Row():
                            with gr.Column():
                                gr.Markdown("#### Ranges dos Coeficientes")
                                a_min = gr.Number(label="a mínimo", value=-2)
                                a_max = gr.Number(label="a máximo", value=2)
                                b_min = gr.Number(label="b mínimo", value=-5)
                                b_max = gr.Number(label="b máximo", value=5)
                                c_3d = gr.Number(label="c fixo", value=0)
                                analysis_3d_btn = gr.Button("Criar Análise 3D", variant="primary")

                            with gr.Column():
                                plot_3d = gr.Plot(label="Superfície 3D")

                        analysis_3d_btn.click(
                            fn=create_3d_analysis,
                            inputs=[a_min, a_max, b_min, b_max, c_3d],
                            outputs=[plot_3d]
                        )

                    # Sub-aba 3: Discriminante
                    with gr.TabItem("🔍 Análise do Discriminante"):
                        with gr.Row():
                            with gr.Column():
                                gr.Markdown("#### Ranges para Análise")
                                disc_a_min = gr.Number(label="a mínimo", value=-3)
                                disc_a_max = gr.Number(label="a máximo", value=3)
                                disc_b_min = gr.Number(label="b mínimo", value=-5)
                                disc_b_max = gr.Number(label="b máximo", value=5)
                                disc_c_min = gr.Number(label="c mínimo", value=-3)
                                disc_c_max = gr.Number(label="c máximo", value=3)
                                discriminant_btn = gr.Button("Analisar Discriminante", variant="primary")

                            with gr.Column():
                                discriminant_plot = gr.Plot(label="Mapa do Discriminante")

                        discriminant_btn.click(
                            fn=create_discriminant_plot,
                            inputs=[disc_a_min, disc_a_max, disc_b_min, disc_b_max, disc_c_min, disc_c_max],
                            outputs=[discriminant_plot]
                        )

                    # Sub-aba 4: Comparação de Família
                    with gr.TabItem("👥 Família de Equações"):
                        with gr.Row():
                            with gr.Column():
                                gr.Markdown("#### Equação Base")
                                fam_base_a = gr.Number(label="a base", value=1)
                                fam_base_b = gr.Number(label="b base", value=-5)
                                fam_base_c = gr.Number(label="c base", value=6)

                                gr.Markdown("#### Variações")
                                with gr.Row():
                                    with gr.Column():
                                        gr.Markdown("**Variação 1**")
                                        fam_var1_a = gr.Number(label="a1", value=1)
                                        fam_var1_b = gr.Number(label="b1", value=-3)
                                        fam_var1_c = gr.Number(label="c1", value=2)

                                    with gr.Column():
                                        gr.Markdown("**Variação 2**")
                                        fam_var2_a = gr.Number(label="a2", value=2)
                                        fam_var2_b = gr.Number(label="b2", value=-4)
                                        fam_var2_c = gr.Number(label="c2", value=1)

                                    with gr.Column():
                                        gr.Markdown("**Variação 3**")
                                        fam_var3_a = gr.Number(label="a3", value=-1)
                                        fam_var3_b = gr.Number(label="b3", value=2)
                                        fam_var3_c = gr.Number(label="c3", value=3)

                                family_btn = gr.Button("Comparar Família", variant="primary")

                            with gr.Column():
                                family_plot = gr.Plot(label="Comparação de Família")

                        family_btn.click(
                            fn=create_family_plot,
                            inputs=[fam_base_a, fam_base_b, fam_base_c,
                                   fam_var1_a, fam_var1_b, fam_var1_c,
                                   fam_var2_a, fam_var2_b, fam_var2_c,
                                   fam_var3_a, fam_var3_b, fam_var3_c],
                            outputs=[family_plot]
                        )

                    # Sub-aba 5: Animação
                    with gr.TabItem("🎬 Animação"):
                        with gr.Row():
                            with gr.Column():
                                gr.Markdown("#### Parâmetros da Animação")
                                anim_a_start = gr.Number(label="a inicial", value=-2)
                                anim_a_end = gr.Number(label="a final", value=2)
                                anim_steps = gr.Slider(label="Número de passos", minimum=5, maximum=50, value=20, step=1)
                                anim_b = gr.Number(label="b fixo", value=0)
                                anim_c = gr.Number(label="c fixo", value=0)
                                animation_btn = gr.Button("Criar Animação", variant="primary")

                            with gr.Column():
                                animation_plot = gr.Plot(label="Animação da Parábola")

                        animation_btn.click(
                            fn=create_animation_plot,
                            inputs=[anim_a_start, anim_a_end, anim_steps, anim_b, anim_c],
                            outputs=[animation_plot]
                        )

                    # Sub-aba 6: Completar Quadrados
                    with gr.TabItem("🧮 Completar Quadrados"):
                        with gr.Row():
                            with gr.Column():
                                gr.Markdown("#### Equação para Análise")
                                comp_a = gr.Number(label="a", value=1)
                                comp_b = gr.Number(label="b", value=-6)
                                comp_c = gr.Number(label="c", value=8)
                                completing_btn = gr.Button("Visualizar Processo", variant="primary")

                            with gr.Column():
                                completing_plot = gr.Plot(label="Processo de Completar Quadrados")

                        completing_btn.click(
                            fn=create_completing_square_plot,
                            inputs=[comp_a, comp_b, comp_c],
                            outputs=[completing_plot]
                        )

                    # Sub-aba 7: Métodos de Resolução
                    with gr.TabItem("🔧 Métodos de Resolução"):
                        with gr.Row():
                            with gr.Column():
                                gr.Markdown("#### Equação para Análise")
                                meth_a = gr.Number(label="a", value=1)
                                meth_b = gr.Number(label="b", value=-5)
                                meth_c = gr.Number(label="c", value=6)
                                methods_btn = gr.Button("Analisar Métodos", variant="primary")

                            with gr.Column():
                                methods_plot = gr.Plot(label="Métodos de Resolução")

                        methods_btn.click(
                            fn=create_methods_plot,
                            inputs=[meth_a, meth_b, meth_c],
                            outputs=[methods_plot]
                        )

            # Aba 5: OSF.io Research Data
            with gr.TabItem("🔬 OSF.io Research Data"):
                gr.Markdown("### Busca, Download e Análise de Dados de Pesquisa")
                gr.Markdown("**OSF.io** é uma plataforma de ciência aberta com milhares de projetos de pesquisa e datasets.")

                with gr.Tabs():
                    # Sub-aba 1: Busca de Projetos com Gemini
                    with gr.TabItem("🔍 Buscar Projetos + 🤖 Gemini"):
                        gr.Markdown("#### Buscar projetos no OSF.io com discussão interativa")

                        with gr.Row():
                            with gr.Column():
                                search_query = gr.Textbox(
                                    label="Termo de busca",
                                    placeholder="Ex: machine learning, psychology, data science",
                                    value="data"
                                )
                                search_max_results = gr.Slider(
                                    minimum=1, maximum=10, value=5, step=1,
                                    label="Número máximo de resultados"
                                )
                                search_btn = gr.Button("🔍 Buscar Projetos", variant="primary")

                                gr.Markdown("#### 💡 Sugestões de busca:")
                                with gr.Row():
                                    suggest1_btn = gr.Button("machine learning", size="sm")
                                    suggest2_btn = gr.Button("psychology", size="sm")
                                with gr.Row():
                                    suggest3_btn = gr.Button("data science", size="sm")
                                    suggest4_btn = gr.Button("neuroscience", size="sm")

                                # Sugestões inteligentes com Gemini
                                gr.Markdown("#### 🤖 Sugestões Inteligentes:")
                                research_area_input = gr.Textbox(
                                    label="Área de pesquisa",
                                    placeholder="Ex: psicologia cognitiva, aprendizado de máquina",
                                    value=""
                                )
                                suggest_terms_btn = gr.Button("🤖 Sugerir Termos com Gemini", size="sm")

                                suggested_terms = gr.Textbox(
                                    label="Termos Sugeridos pelo Gemini",
                                    lines=4,
                                    max_lines=6
                                )

                            with gr.Column():
                                search_results = gr.Textbox(
                                    label="Resultados da Busca",
                                    lines=8,
                                    max_lines=10
                                )

                                # Chat com Gemini sobre os resultados
                                gr.Markdown("#### 🤖 Discussão com Gemini sobre os Resultados")
                                search_chatbot = gr.Chatbot(
                                    label="Chat sobre Resultados da Busca",
                                    height=300,
                                    type="tuples"
                                )
                                search_chat_input = gr.Textbox(
                                    label="Pergunte ao Gemini sobre os resultados",
                                    placeholder="Ex: Qual projeto parece mais relevante? Como avaliar a qualidade dos dados?",
                                    lines=2
                                )
                                search_chat_btn = gr.Button("💬 Enviar para Gemini", size="sm")

                        # Eventos de busca
                        search_btn.click(
                            fn=osf_search_with_gemini_discussion,
                            inputs=[search_query, search_max_results],
                            outputs=[search_results, search_chatbot]
                        )

                        # Chat sobre resultados
                        search_chat_btn.click(
                            fn=lambda msg, history: osf_chat_with_gemini(msg, history, "search"),
                            inputs=[search_chat_input, search_chatbot],
                            outputs=[search_chatbot, search_chat_input]
                        )

                        # Sugestões inteligentes
                        suggest_terms_btn.click(
                            fn=osf_suggest_search_terms_gemini,
                            inputs=[research_area_input],
                            outputs=[suggested_terms]
                        )

                        # Botões de sugestão
                        suggest1_btn.click(lambda: "machine learning", outputs=[search_query])
                        suggest2_btn.click(lambda: "psychology", outputs=[search_query])
                        suggest3_btn.click(lambda: "data science", outputs=[search_query])
                        suggest4_btn.click(lambda: "neuroscience", outputs=[search_query])

                    # Sub-aba 2: Download de Arquivos
                    with gr.TabItem("📥 Download de Arquivos"):
                        gr.Markdown("#### Fazer download dos arquivos de um projeto")
                        gr.Markdown("**Formato da URL:** `https://osf.io/xxxxx/` ou apenas `xxxxx`")

                        with gr.Row():
                            with gr.Column():
                                project_url = gr.Textbox(
                                    label="URL do Projeto OSF",
                                    placeholder="https://osf.io/j4bv6/ ou j4bv6",
                                    value="https://osf.io/j4bv6/"
                                )
                                download_dir_input = gr.Textbox(
                                    label="Diretório de Download",
                                    value="osf_downloads",
                                    placeholder="osf_downloads"
                                )
                                download_btn = gr.Button("📥 Fazer Download", variant="primary")

                                gr.Markdown("#### 📂 Projetos de exemplo:")
                                with gr.Row():
                                    example1_btn = gr.Button("Machine Learning ICH", size="sm")
                                    example2_btn = gr.Button("Moral Algorithms", size="sm")

                            with gr.Column():
                                download_results = gr.Textbox(
                                    label="Resultado do Download",
                                    lines=15,
                                    max_lines=20
                                )

                        # Eventos de download
                        download_btn.click(
                            fn=osf_download_files,
                            inputs=[project_url, download_dir_input],
                            outputs=[download_results]
                        )

                        # Exemplos
                        example1_btn.click(lambda: "https://osf.io/j4bv6/", outputs=[project_url])
                        example2_btn.click(lambda: "https://osf.io/2zfu4/", outputs=[project_url])

                    # Sub-aba 3: Análise de Dados com Gemini
                    with gr.TabItem("📊 Análise de Dados + 🤖 Gemini"):
                        gr.Markdown("#### Analisar dados baixados com discussão interativa")

                        with gr.Row():
                            with gr.Column():
                                analysis_dir = gr.Textbox(
                                    label="Diretório com dados",
                                    value="osf_downloads",
                                    placeholder="osf_downloads"
                                )
                                analyze_btn = gr.Button("📊 Analisar Dados", variant="primary")

                                analysis_results = gr.Textbox(
                                    label="Resultado da Análise",
                                    lines=8,
                                    max_lines=10
                                )

                            with gr.Column():
                                # Chat com Gemini sobre a análise
                                gr.Markdown("#### 🤖 Discussão com Gemini sobre os Dados")
                                analysis_chatbot = gr.Chatbot(
                                    label="Chat sobre Análise de Dados",
                                    height=300,
                                    type="tuples"
                                )
                                analysis_chat_input = gr.Textbox(
                                    label="Pergunte ao Gemini sobre os dados",
                                    placeholder="Ex: O que significam essas estatísticas? Que análises posso fazer? Há outliers?",
                                    lines=2
                                )
                                analysis_chat_btn = gr.Button("💬 Enviar para Gemini", size="sm")

                        analysis_plot = gr.Plot(label="Gráficos dos Dados")

                        # Evento de análise
                        analyze_btn.click(
                            fn=osf_analyze_with_gemini_discussion,
                            inputs=[analysis_dir],
                            outputs=[analysis_results, analysis_plot, analysis_chatbot]
                        )

                        # Chat sobre análise
                        analysis_chat_btn.click(
                            fn=lambda msg, history: osf_chat_with_gemini(msg, history, "analysis"),
                            inputs=[analysis_chat_input, analysis_chatbot],
                            outputs=[analysis_chatbot, analysis_chat_input]
                        )

                    # Sub-aba 4: Pipeline Completo com Gemini
                    with gr.TabItem("🚀 Pipeline Completo + 🤖 Gemini"):
                        gr.Markdown("#### Pipeline Automático com Discussão IA: Busca → Download → Análise → Chat")
                        gr.Markdown("**Este pipeline faz tudo automaticamente e permite discussão com Gemini!**")

                        with gr.Row():
                            with gr.Column():
                                pipeline_query = gr.Textbox(
                                    label="Termo de busca",
                                    placeholder="Ex: data, psychology, machine learning",
                                    value="data"
                                )
                                pipeline_max = gr.Slider(
                                    minimum=1, maximum=5, value=3, step=1,
                                    label="Máximo de projetos a tentar"
                                )
                                pipeline_btn = gr.Button("🚀 Executar Pipeline Completo", variant="primary")

                                gr.Markdown("#### ⚡ Pipelines rápidos:")
                                with gr.Row():
                                    quick1_btn = gr.Button("Data Science", size="sm")
                                    quick2_btn = gr.Button("Psychology", size="sm")
                                with gr.Row():
                                    quick3_btn = gr.Button("Neuroscience", size="sm")
                                    quick4_btn = gr.Button("Education", size="sm")

                                pipeline_results = gr.Textbox(
                                    label="Resultado do Pipeline",
                                    lines=8,
                                    max_lines=10
                                )

                            with gr.Column():
                                # Chat com Gemini sobre todo o pipeline
                                gr.Markdown("#### 🤖 Discussão Completa com Gemini")
                                pipeline_chatbot = gr.Chatbot(
                                    label="Chat sobre Pipeline Completo",
                                    height=350,
                                    type="tuples"
                                )
                                pipeline_chat_input = gr.Textbox(
                                    label="Discuta os resultados com Gemini",
                                    placeholder="Ex: Interprete os resultados, sugira próximos passos, explique as análises",
                                    lines=2
                                )
                                pipeline_chat_btn = gr.Button("💬 Enviar para Gemini", size="sm")

                        pipeline_plot = gr.Plot(label="Análise Automática dos Dados")

                        # Evento do pipeline
                        pipeline_btn.click(
                            fn=osf_pipeline_complete,
                            inputs=[pipeline_query, pipeline_max],
                            outputs=[pipeline_results, pipeline_plot]
                        )

                        # Chat sobre pipeline
                        pipeline_chat_btn.click(
                            fn=lambda msg, history: osf_chat_with_gemini(msg, history, "general"),
                            inputs=[pipeline_chat_input, pipeline_chatbot],
                            outputs=[pipeline_chatbot, pipeline_chat_input]
                        )

                        # Pipelines rápidos
                        quick1_btn.click(lambda: "data science", outputs=[pipeline_query])
                        quick2_btn.click(lambda: "psychology", outputs=[pipeline_query])
                        quick3_btn.click(lambda: "neuroscience", outputs=[pipeline_query])
                        quick4_btn.click(lambda: "education", outputs=[pipeline_query])

            # Aba 6: Análise de Fourier
            with gr.TabItem("🌊 Análise de Fourier"):
                gr.Markdown("### Transformada de Fourier e Análise de Sinais")
                
                with gr.Row():
                    with gr.Column():
                        signal_type_input = gr.Dropdown(
                            choices=["Senoidal", "Cossenoidal", "Quadrada"],
                            value="Senoidal",
                            label="Tipo de Sinal"
                        )
                        frequency_input = gr.Slider(
                            minimum=1, maximum=50, value=5, step=1,
                            label="Frequência (Hz)"
                        )
                        noise_input = gr.Slider(
                            minimum=0, maximum=1, value=0.1, step=0.01,
                            label="Nível de Ruído"
                        )
                        fourier_btn = gr.Button("Analisar", variant="primary")
                    
                    with gr.Column():
                        fourier_info = gr.Textbox(
                            label="Informações do Sinal",
                            lines=8,
                            max_lines=10
                        )
                
                fourier_plot = gr.Plot(label="Análise de Fourier")
                
                fourier_btn.click(
                    fn=fourier_analysis,
                    inputs=[signal_type_input, frequency_input, noise_input],
                    outputs=[fourier_info, fourier_plot]
                )
        
        gr.Markdown("""
        ---
        💡 **Dicas:**
        - Experimente diferentes parâmetros para ver como afetam os resultados
        - Use matrizes pequenas para operações mais rápidas
        - Para equações quadráticas, teste os exemplos pré-definidos
        - A análise de Fourier é útil para processamento de sinais
        - **Novo!** Explore os gráficos avançados para análises visuais profundas
        - Use gráficos interativos para explorar propriedades das equações
        - Animações ajudam a entender como os coeficientes afetam a parábola
        - Análise 3D mostra relações entre múltiplos parâmetros
        - **🔬 OSF.io Research Data:** Busque, baixe e analise dados reais de pesquisa!
        - **🤖 Gemini AI integrado:** Discuta resultados e obtenha insights inteligentes
        - Use o Pipeline Completo para análise automática de dados científicos
        - Experimente diferentes termos de busca para encontrar datasets interessantes
        - Nem todos os projetos OSF têm arquivos - isso é normal!
        - **Chat inteligente:** Pergunte ao Gemini sobre metodologias e interpretações
        - **Sugestões automáticas:** Use Gemini para gerar termos de busca relevantes
        """)
    
    return demo


def main():
    """Função principal."""
    print("🚀 Iniciando aplicação NumPy + Gradio...")
    
    # Criar e lançar a interface
    demo = create_interface()
    demo.launch(
        server_name="0.0.0.0",
        server_port=7860,
        share=False,
        show_error=True
    )


if __name__ == "__main__":
    main()
