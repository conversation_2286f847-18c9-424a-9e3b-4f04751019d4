#!/usr/bin/env python3
"""
Módulo de gráficos avançados para análise de equações quadráticas.

Este módulo fornece funcionalidades avançadas de visualização usando Plotly,
incluindo gráficos interativos, 3D, animações e análises comparativas.
"""

import numpy as np
import matplotlib.pyplot as plt
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import seaborn as sns
from typing import Dict, List, Tuple, Optional, Union
import pandas as pd
from src.quadratic_solver import solve_quadratic


def create_interactive_quadratic_plot(a: float, b: float, c: float, solution: Dict = None) -> go.Figure:
    """
    Cria um gráfico interativo da função quadrática usando Plotly.
    
    Args:
        a, b, c: Coeficientes da equação
        solution: Solução da equação (opcional)
    
    Returns:
        Figura Plotly interativa
    """
    if solution is None:
        solution = solve_quadratic(a, b, c)
    
    # Gerar pontos para o gráfico
    x_vals = np.linspace(-10, 10, 1000)
    y_vals = a * x_vals**2 + b * x_vals + c
    
    # Criar figura
    fig = go.Figure()
    
    # Adicionar curva principal
    fig.add_trace(go.Scatter(
        x=x_vals,
        y=y_vals,
        mode='lines',
        name=f'{a}x² + {b}x + {c}',
        line=dict(color='blue', width=3),
        hovertemplate='x: %{x:.3f}<br>f(x): %{y:.3f}<extra></extra>'
    ))
    
    # Adicionar linha do eixo x
    fig.add_hline(y=0, line_dash="dash", line_color="gray", opacity=0.5)
    
    # Adicionar vértice se for quadrática
    if solution['equation_type'] == 'quadratic':
        vertex_x, vertex_y = solution['vertex']
        fig.add_trace(go.Scatter(
            x=[vertex_x],
            y=[vertex_y],
            mode='markers',
            name='Vértice',
            marker=dict(color='green', size=12, symbol='diamond'),
            hovertemplate=f'Vértice<br>x: {vertex_x:.3f}<br>y: {vertex_y:.3f}<extra></extra>'
        ))
        
        # Adicionar raízes se existirem
        if solution['has_real_roots']:
            roots = solution['roots']
            fig.add_trace(go.Scatter(
                x=roots,
                y=[0] * len(roots),
                mode='markers',
                name='Raízes',
                marker=dict(color='red', size=10, symbol='circle'),
                hovertemplate='Raiz: %{x:.3f}<extra></extra>'
            ))
    
    # Configurar layout
    fig.update_layout(
        title=f'Gráfico Interativo: {a}x² + {b}x + {c} = 0',
        xaxis_title='x',
        yaxis_title='f(x)',
        hovermode='closest',
        showlegend=True,
        template='plotly_white',
        width=800,
        height=600
    )
    
    return fig


def create_3d_surface_plot(a_range: Tuple[float, float] = (-2, 2), 
                          b_range: Tuple[float, float] = (-5, 5),
                          c_fixed: float = 0) -> go.Figure:
    """
    Cria um gráfico 3D mostrando como a função muda com diferentes coeficientes.
    
    Args:
        a_range: Range para o coeficiente a
        b_range: Range para o coeficiente b  
        c_fixed: Valor fixo para c
    
    Returns:
        Figura Plotly 3D
    """
    # Criar grade de valores
    a_vals = np.linspace(a_range[0], a_range[1], 20)
    b_vals = np.linspace(b_range[0], b_range[1], 20)
    x_vals = np.linspace(-5, 5, 50)
    
    # Criar meshgrid
    A, B = np.meshgrid(a_vals, b_vals)
    
    # Calcular superfície para x = 0 (intercepto y)
    Z_intercept = c_fixed * np.ones_like(A)
    
    # Calcular superfície para vértice
    vertex_x = -B / (2 * A)
    vertex_y = A * vertex_x**2 + B * vertex_x + c_fixed
    
    # Criar figura com subplots 3D
    fig = make_subplots(
        rows=1, cols=2,
        specs=[[{'type': 'surface'}, {'type': 'surface'}]],
        subplot_titles=['Intercepto Y (x=0)', 'Vértice da Parábola']
    )
    
    # Adicionar superfície do intercepto
    fig.add_trace(
        go.Surface(
            x=A, y=B, z=Z_intercept,
            colorscale='Viridis',
            name='Intercepto Y'
        ),
        row=1, col=1
    )
    
    # Adicionar superfície do vértice
    fig.add_trace(
        go.Surface(
            x=A, y=B, z=vertex_y,
            colorscale='Plasma',
            name='Vértice'
        ),
        row=1, col=2
    )
    
    # Configurar layout
    fig.update_layout(
        title=f'Análise 3D: Variação dos Coeficientes (c = {c_fixed})',
        scene=dict(
            xaxis_title='Coeficiente a',
            yaxis_title='Coeficiente b',
            zaxis_title='Valor'
        ),
        scene2=dict(
            xaxis_title='Coeficiente a',
            yaxis_title='Coeficiente b',
            zaxis_title='Valor do Vértice'
        ),
        width=1200,
        height=600
    )
    
    return fig


def create_discriminant_analysis(a_range: Tuple[float, float] = (-3, 3),
                               b_range: Tuple[float, float] = (-5, 5),
                               c_range: Tuple[float, float] = (-3, 3)) -> go.Figure:
    """
    Cria visualização do discriminante para diferentes coeficientes.
    
    Args:
        a_range, b_range, c_range: Ranges para os coeficientes
    
    Returns:
        Figura Plotly com análise do discriminante
    """
    # Criar grade de valores
    a_vals = np.linspace(a_range[0], a_range[1], 50)
    b_vals = np.linspace(b_range[0], b_range[1], 50)
    c_vals = np.linspace(c_range[0], c_range[1], 50)
    
    # Fixar um coeficiente e variar os outros dois
    c_fixed = 0  # Fixar c = 0 para visualização 2D
    
    A, B = np.meshgrid(a_vals, b_vals)
    discriminant = B**2 - 4 * A * c_fixed
    
    # Criar figura
    fig = go.Figure()
    
    # Adicionar contorno do discriminante
    fig.add_trace(go.Contour(
        x=a_vals,
        y=b_vals,
        z=discriminant,
        colorscale='RdYlBu',
        contours=dict(
            start=-10,
            end=10,
            size=1,
            showlabels=True
        ),
        hovertemplate='a: %{x:.2f}<br>b: %{y:.2f}<br>Δ: %{z:.2f}<extra></extra>'
    ))
    
    # Adicionar linha onde discriminante = 0
    fig.add_trace(go.Contour(
        x=a_vals,
        y=b_vals,
        z=discriminant,
        contours=dict(
            start=0,
            end=0,
            size=1,
            coloring='lines'
        ),
        line=dict(color='black', width=3),
        showscale=False,
        name='Δ = 0'
    ))
    
    # Configurar layout
    fig.update_layout(
        title=f'Análise do Discriminante (c = {c_fixed})',
        xaxis_title='Coeficiente a',
        yaxis_title='Coeficiente b',
        width=800,
        height=600,
        annotations=[
            dict(
                x=0.02, y=0.98,
                xref='paper', yref='paper',
                text='Δ > 0: Duas raízes reais<br>Δ = 0: Uma raiz real<br>Δ < 0: Raízes complexas',
                showarrow=False,
                bgcolor='white',
                bordercolor='black',
                borderwidth=1
            )
        ]
    )
    
    return fig


def create_family_comparison(base_coeffs: Tuple[float, float, float],
                           variations: List[Tuple[float, float, float]]) -> go.Figure:
    """
    Compara uma família de equações quadráticas.
    
    Args:
        base_coeffs: Coeficientes da equação base (a, b, c)
        variations: Lista de variações dos coeficientes
    
    Returns:
        Figura Plotly com comparação
    """
    fig = go.Figure()
    
    x_vals = np.linspace(-10, 10, 500)
    
    # Adicionar equação base
    a_base, b_base, c_base = base_coeffs
    y_base = a_base * x_vals**2 + b_base * x_vals + c_base
    
    fig.add_trace(go.Scatter(
        x=x_vals,
        y=y_base,
        mode='lines',
        name=f'Base: {a_base}x² + {b_base}x + {c_base}',
        line=dict(color='black', width=4)
    ))
    
    # Adicionar variações
    colors = px.colors.qualitative.Set1
    for i, (a, b, c) in enumerate(variations):
        y_vals = a * x_vals**2 + b * x_vals + c
        
        fig.add_trace(go.Scatter(
            x=x_vals,
            y=y_vals,
            mode='lines',
            name=f'{a}x² + {b}x + {c}',
            line=dict(color=colors[i % len(colors)], width=2)
        ))
    
    # Configurar layout
    fig.update_layout(
        title='Comparação de Família de Equações Quadráticas',
        xaxis_title='x',
        yaxis_title='f(x)',
        hovermode='x unified',
        template='plotly_white',
        width=1000,
        height=600
    )
    
    return fig


def create_animated_parabola(a_values: List[float], b: float = 0, c: float = 0) -> go.Figure:
    """
    Cria animação mostrando como a parábola muda com diferentes valores de 'a'.

    Args:
        a_values: Lista de valores para o coeficiente 'a'
        b, c: Coeficientes fixos

    Returns:
        Figura Plotly animada
    """
    x_vals = np.linspace(-5, 5, 100)

    # Criar frames para animação
    frames = []
    for a in a_values:
        y_vals = a * x_vals**2 + b * x_vals + c

        frame = go.Frame(
            data=[
                go.Scatter(
                    x=x_vals,
                    y=y_vals,
                    mode='lines',
                    name=f'a = {a:.2f}',
                    line=dict(color='blue', width=3)
                )
            ],
            name=str(a)
        )
        frames.append(frame)

    # Criar figura inicial
    fig = go.Figure(
        data=[
            go.Scatter(
                x=x_vals,
                y=a_values[0] * x_vals**2 + b * x_vals + c,
                mode='lines',
                name=f'a = {a_values[0]:.2f}',
                line=dict(color='blue', width=3)
            )
        ],
        frames=frames
    )

    # Adicionar controles de animação
    fig.update_layout(
        title='Animação: Variação do Coeficiente "a"',
        xaxis_title='x',
        yaxis_title='f(x)',
        updatemenus=[
            dict(
                type='buttons',
                showactive=False,
                buttons=[
                    dict(
                        label='Play',
                        method='animate',
                        args=[None, dict(frame=dict(duration=500, redraw=True), fromcurrent=True)]
                    ),
                    dict(
                        label='Pause',
                        method='animate',
                        args=[[None], dict(frame=dict(duration=0, redraw=False), mode='immediate')]
                    )
                ]
            )
        ],
        sliders=[
            dict(
                steps=[
                    dict(
                        args=[[str(a)], dict(frame=dict(duration=0, redraw=True), mode='immediate')],
                        label=f'a = {a:.2f}',
                        method='animate'
                    ) for a in a_values
                ],
                active=0,
                currentvalue=dict(prefix='Coeficiente a: '),
                len=0.9,
                x=0.1,
                xanchor='left',
                y=0,
                yanchor='top'
            )
        ],
        width=800,
        height=600
    )

    return fig


def create_completing_square_visualization(a: float, b: float, c: float) -> go.Figure:
    """
    Visualiza o processo de completar quadrados.

    Args:
        a, b, c: Coeficientes da equação

    Returns:
        Figura Plotly mostrando o processo
    """
    if a == 0:
        return go.Figure().add_annotation(text="Não é uma equação quadrática",
                                        xref="paper", yref="paper", x=0.5, y=0.5)

    # Calcular forma canônica: a(x - h)² + k
    h = -b / (2 * a)
    k = c - (b**2) / (4 * a)

    x_vals = np.linspace(h - 5, h + 5, 500)

    # Função original
    y_original = a * x_vals**2 + b * x_vals + c

    # Função na forma canônica (mesma função, mas destacando a transformação)
    y_canonical = a * (x_vals - h)**2 + k

    # Criar subplots
    fig = make_subplots(
        rows=2, cols=2,
        subplot_titles=[
            f'Original: {a}x² + {b}x + {c}',
            f'Canônica: {a}(x - {h:.2f})² + {k:.2f}',
            'Comparação',
            'Transformações'
        ]
    )

    # Gráfico original
    fig.add_trace(
        go.Scatter(x=x_vals, y=y_original, mode='lines', name='Original', line=dict(color='blue')),
        row=1, col=1
    )

    # Gráfico canônico
    fig.add_trace(
        go.Scatter(x=x_vals, y=y_canonical, mode='lines', name='Canônica', line=dict(color='red')),
        row=1, col=2
    )

    # Comparação
    fig.add_trace(
        go.Scatter(x=x_vals, y=y_original, mode='lines', name='Original', line=dict(color='blue')),
        row=2, col=1
    )
    fig.add_trace(
        go.Scatter(x=x_vals, y=y_canonical, mode='lines', name='Canônica', line=dict(color='red', dash='dash')),
        row=2, col=1
    )

    # Marcar vértice
    fig.add_trace(
        go.Scatter(x=[h], y=[k], mode='markers', name='Vértice',
                  marker=dict(color='green', size=10, symbol='diamond')),
        row=2, col=1
    )

    # Informações das transformações
    transformations_text = f"""
    Transformações:
    • Translação horizontal: {h:.2f}
    • Translação vertical: {k:.2f}
    • Fator de escala: {a:.2f}

    Vértice: ({h:.2f}, {k:.2f})

    Processo:
    1. {a}x² + {b}x + {c}
    2. {a}(x² + {b/a:.2f}x) + {c}
    3. {a}(x² + {b/a:.2f}x + {(b/(2*a))**2:.2f}) + {c} - {a*(b/(2*a))**2:.2f}
    4. {a}(x + {b/(2*a):.2f})² + {k:.2f}
    """

    fig.add_annotation(
        text=transformations_text,
        xref="x domain", yref="y domain",
        x=0.1, y=0.9,
        showarrow=False,
        font=dict(family="monospace", size=10),
        bgcolor="white",
        bordercolor="black",
        borderwidth=1,
        row=2, col=2
    )

    fig.update_layout(
        title='Visualização: Completando Quadrados',
        showlegend=True,
        width=1200,
        height=800
    )

    return fig


def create_properties_comparison_chart(equations: List[Tuple[float, float, float]]) -> go.Figure:
    """
    Cria gráfico de barras comparando propriedades de múltiplas equações.

    Args:
        equations: Lista de coeficientes (a, b, c)

    Returns:
        Figura Plotly com comparação de propriedades
    """
    properties_data = []

    for i, (a, b, c) in enumerate(equations):
        solution = solve_quadratic(a, b, c)

        # Calcular propriedades
        vertex_x, vertex_y = solution.get('vertex', (0, 0))
        discriminant = solution.get('discriminant', 0)

        properties_data.append({
            'Equação': f'Eq {i+1}: {a}x² + {b}x + {c}',
            'Vértice X': vertex_x,
            'Vértice Y': vertex_y,
            'Discriminante': discriminant,
            'Intercepto Y': c,
            'Coef. a': a
        })

    df = pd.DataFrame(properties_data)

    # Criar subplots para diferentes propriedades
    fig = make_subplots(
        rows=2, cols=3,
        subplot_titles=['Vértice X', 'Vértice Y', 'Discriminante', 'Intercepto Y', 'Coeficiente a', 'Resumo']
    )

    # Gráficos de barras para cada propriedade
    properties = ['Vértice X', 'Vértice Y', 'Discriminante', 'Intercepto Y', 'Coef. a']
    positions = [(1,1), (1,2), (1,3), (2,1), (2,2)]

    for prop, (row, col) in zip(properties, positions):
        fig.add_trace(
            go.Bar(x=df['Equação'], y=df[prop], name=prop, showlegend=False),
            row=row, col=col
        )

    # Tabela resumo
    fig.add_trace(
        go.Table(
            header=dict(values=list(df.columns), fill_color='lightblue'),
            cells=dict(values=[df[col] for col in df.columns], fill_color='white')
        ),
        row=2, col=3
    )

    fig.update_layout(
        title='Comparação de Propriedades das Equações',
        showlegend=False,
        width=1400,
        height=800
    )

    return fig


def create_solution_method_visualization(a: float, b: float, c: float) -> go.Figure:
    """
    Visualiza diferentes métodos de resolução da equação quadrática.

    Args:
        a, b, c: Coeficientes da equação

    Returns:
        Figura Plotly mostrando métodos de resolução
    """
    if a == 0:
        return go.Figure().add_annotation(text="Não é uma equação quadrática",
                                        xref="paper", yref="paper", x=0.5, y=0.5)

    solution = solve_quadratic(a, b, c)
    discriminant = solution['discriminant']

    # Criar figura com múltiplos subplots
    fig = make_subplots(
        rows=2, cols=2,
        subplot_titles=[
            'Método Gráfico',
            'Fórmula de Bhaskara',
            'Completar Quadrados',
            'Análise do Discriminante'
        ]
    )

    # 1. Método Gráfico
    x_vals = np.linspace(-10, 10, 500)
    y_vals = a * x_vals**2 + b * x_vals + c

    fig.add_trace(
        go.Scatter(x=x_vals, y=y_vals, mode='lines', name='f(x)', line=dict(color='blue')),
        row=1, col=1
    )
    fig.add_hline(y=0, line_dash="dash", line_color="gray", row=1, col=1)

    if solution['has_real_roots']:
        roots = solution['roots']
        fig.add_trace(
            go.Scatter(x=roots, y=[0]*len(roots), mode='markers',
                      name='Raízes', marker=dict(color='red', size=8)),
            row=1, col=1
        )

    # 2. Fórmula de Bhaskara (texto explicativo)
    bhaskara_text = f"""
    Fórmula de Bhaskara:
    x = (-b ± √(b² - 4ac)) / (2a)

    Substituindo:
    a = {a}
    b = {b}
    c = {c}

    Discriminante:
    Δ = b² - 4ac = {b}² - 4({a})({c}) = {discriminant:.3f}

    """

    if discriminant > 0:
        x1 = (-b + np.sqrt(discriminant)) / (2*a)
        x2 = (-b - np.sqrt(discriminant)) / (2*a)
        bhaskara_text += f"""
    √Δ = {np.sqrt(discriminant):.3f}

    x₁ = ({-b} + {np.sqrt(discriminant):.3f}) / {2*a} = {x1:.3f}
    x₂ = ({-b} - {np.sqrt(discriminant):.3f}) / {2*a} = {x2:.3f}
        """
    elif discriminant == 0:
        x = -b / (2*a)
        bhaskara_text += f"""
    √Δ = 0

    x = {-b} / {2*a} = {x:.3f} (raiz dupla)
        """
    else:
        real_part = -b / (2*a)
        imag_part = np.sqrt(-discriminant) / (2*a)
        bhaskara_text += f"""
    √Δ = {np.sqrt(-discriminant):.3f}i

    x₁ = {real_part:.3f} + {imag_part:.3f}i
    x₂ = {real_part:.3f} - {imag_part:.3f}i
        """

    fig.add_annotation(
        text=bhaskara_text,
        xref="x domain", yref="y domain",
        x=0.1, y=0.9,
        showarrow=False,
        font=dict(family="monospace", size=9),
        bgcolor="lightyellow",
        bordercolor="orange",
        borderwidth=1,
        row=1, col=2
    )

    # 3. Completar Quadrados
    h = -b / (2*a)
    k = c - (b**2) / (4*a)

    completing_text = f"""
    Completando Quadrados:

    {a}x² + {b}x + {c} = 0

    Dividir por a:
    x² + {b/a:.3f}x + {c/a:.3f} = 0

    Completar o quadrado:
    x² + {b/a:.3f}x + {(b/(2*a))**2:.3f} = {(b/(2*a))**2:.3f} - {c/a:.3f}

    (x + {b/(2*a):.3f})² = {(b/(2*a))**2 - c/a:.3f}

    Forma canônica:
    {a}(x - {h:.3f})² + {k:.3f} = 0

    Vértice: ({h:.3f}, {k:.3f})
    """

    fig.add_annotation(
        text=completing_text,
        xref="x domain", yref="y domain",
        x=0.1, y=0.9,
        showarrow=False,
        font=dict(family="monospace", size=9),
        bgcolor="lightgreen",
        bordercolor="green",
        borderwidth=1,
        row=2, col=1
    )

    # 4. Análise do Discriminante
    discriminant_analysis = f"""
    Análise do Discriminante:

    Δ = b² - 4ac = {discriminant:.3f}

    """

    if discriminant > 0:
        discriminant_analysis += """
    Δ > 0: Duas raízes reais distintas
    • A parábola intercepta o eixo x em dois pontos
    • A equação tem duas soluções reais
        """
    elif discriminant == 0:
        discriminant_analysis += """
    Δ = 0: Uma raiz real (raiz dupla)
    • A parábola toca o eixo x em um ponto (vértice)
    • A equação tem uma solução real repetida
        """
    else:
        discriminant_analysis += """
    Δ < 0: Duas raízes complexas conjugadas
    • A parábola não intercepta o eixo x
    • A equação tem soluções complexas
        """

    fig.add_annotation(
        text=discriminant_analysis,
        xref="x domain", yref="y domain",
        x=0.1, y=0.9,
        showarrow=False,
        font=dict(family="monospace", size=10),
        bgcolor="lightcoral",
        bordercolor="red",
        borderwidth=1,
        row=2, col=2
    )

    fig.update_layout(
        title=f'Métodos de Resolução: {a}x² + {b}x + {c} = 0',
        showlegend=True,
        width=1400,
        height=1000
    )

    return fig


def create_roots_histogram(equations: List[Tuple[float, float, float]]) -> go.Figure:
    """
    Cria histograma das raízes de múltiplas equações.
    
    Args:
        equations: Lista de coeficientes (a, b, c)
    
    Returns:
        Figura Plotly com histograma
    """
    real_roots = []
    complex_roots = []
    
    for a, b, c in equations:
        solution = solve_quadratic(a, b, c)
        if solution['has_real_roots']:
            real_roots.extend(solution['roots'])
        else:
            # Para raízes complexas, usar apenas a parte real
            for root in solution['roots']:
                if isinstance(root, complex):
                    complex_roots.append(root.real)
    
    # Criar subplots
    fig = make_subplots(
        rows=2, cols=1,
        subplot_titles=['Distribuição de Raízes Reais', 'Parte Real de Raízes Complexas']
    )
    
    # Histograma de raízes reais
    if real_roots:
        fig.add_trace(
            go.Histogram(
                x=real_roots,
                nbinsx=20,
                name='Raízes Reais',
                marker_color='blue',
                opacity=0.7
            ),
            row=1, col=1
        )
    
    # Histograma de partes reais de raízes complexas
    if complex_roots:
        fig.add_trace(
            go.Histogram(
                x=complex_roots,
                nbinsx=20,
                name='Parte Real (Complexas)',
                marker_color='red',
                opacity=0.7
            ),
            row=2, col=1
        )
    
    # Configurar layout
    fig.update_layout(
        title=f'Análise Estatística de {len(equations)} Equações',
        showlegend=True,
        width=800,
        height=800
    )
    
    return fig
