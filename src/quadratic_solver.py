"""
Módulo para resolução de equações quadráticas.

Este módulo contém funções para resolver equações de segundo grau
e analisar suas propriedades matemáticas.
"""

import numpy as np
import matplotlib.pyplot as plt
from typing import Tuple, Dict, List, Optional
import cmath


def solve_quadratic(a: float, b: float, c: float) -> Dict:
    """
    Resolve uma equação quadrática ax² + bx + c = 0.
    
    Args:
        a: Coeficiente de x²
        b: Coeficiente de x
        c: Termo independente
    
    Returns:
        Dicionário com informações completas sobre a solução
    """
    result = {
        'coefficients': {'a': a, 'b': b, 'c': c},
        'equation_type': '',
        'discriminant': None,
        'roots': [],
        'roots_type': '',
        'vertex': None,
        'axis_of_symmetry': None,
        'y_intercept': c,
        'concavity': '',
        'factored_form': '',
        'has_real_roots': False
    }
    
    if a == 0:
        if b == 0:
            if c == 0:
                result['equation_type'] = 'indeterminate'
                result['roots_type'] = 'infinitas soluções'
            else:
                result['equation_type'] = 'impossible'
                result['roots_type'] = 'sem solução'
        else:
            # Equação linear: bx + c = 0
            result['equation_type'] = 'linear'
            root = -c / b
            result['roots'] = [root]
            result['roots_type'] = 'uma raiz real'
            result['has_real_roots'] = True
    else:
        # Equação quadrática
        result['equation_type'] = 'quadratic'
        discriminant = b**2 - 4*a*c
        result['discriminant'] = discriminant
        
        # Vértice da parábola
        vertex_x = -b / (2*a)
        vertex_y = a * vertex_x**2 + b * vertex_x + c
        result['vertex'] = (vertex_x, vertex_y)
        result['axis_of_symmetry'] = vertex_x
        
        # Concavidade
        result['concavity'] = 'para cima' if a > 0 else 'para baixo'
        
        if discriminant > 0:
            # Duas raízes reais distintas
            x1 = (-b + np.sqrt(discriminant)) / (2*a)
            x2 = (-b - np.sqrt(discriminant)) / (2*a)
            result['roots'] = [x1, x2]
            result['roots_type'] = 'duas raízes reais distintas'
            result['has_real_roots'] = True
            result['factored_form'] = f"{a}(x - {x1:.3f})(x - {x2:.3f})"
            
        elif discriminant == 0:
            # Uma raiz real (raiz dupla)
            x = -b / (2*a)
            result['roots'] = [x]
            result['roots_type'] = 'uma raiz real (raiz dupla)'
            result['has_real_roots'] = True
            result['factored_form'] = f"{a}(x - {x:.3f})²"
            
        else:
            # Duas raízes complexas
            real_part = -b / (2*a)
            imag_part = np.sqrt(-discriminant) / (2*a)
            x1 = complex(real_part, imag_part)
            x2 = complex(real_part, -imag_part)
            result['roots'] = [x1, x2]
            result['roots_type'] = 'duas raízes complexas conjugadas'
            result['has_real_roots'] = False
    
    return result


def format_solution(solution: Dict) -> str:
    """
    Formata a solução de uma equação quadrática para exibição.
    
    Args:
        solution: Dicionário com a solução da equação
    
    Returns:
        String formatada com a solução
    """
    a, b, c = solution['coefficients']['a'], solution['coefficients']['b'], solution['coefficients']['c']
    
    if solution['equation_type'] == 'indeterminate':
        return "❌ Equação indeterminada: 0 = 0 (infinitas soluções)"
    elif solution['equation_type'] == 'impossible':
        return f"❌ Equação impossível: {c} = 0 (sem solução)"
    elif solution['equation_type'] == 'linear':
        x = solution['roots'][0]
        return f"""
📐 Equação Linear: {b}x + {c} = 0

✅ Solução única:
x = {x:.6f}
"""
    else:
        # Equação quadrática
        text = f"""
📐 Equação Quadrática: {a}x² + {b}x + {c} = 0

🔍 Discriminante (Δ): {solution['discriminant']:.6f}
📍 Vértice: ({solution['vertex'][0]:.3f}, {solution['vertex'][1]:.3f})
📏 Eixo de simetria: x = {solution['axis_of_symmetry']:.3f}
📈 Concavidade: {solution['concavity']}
📌 Intercepta eixo y em: (0, {solution['y_intercept']})

"""
        
        if solution['roots_type'] == 'duas raízes reais distintas':
            x1, x2 = solution['roots']
            text += f"""✅ Duas raízes reais distintas:
x₁ = {x1:.6f}
x₂ = {x2:.6f}

📊 Forma fatorada: {solution['factored_form']}
"""
        elif solution['roots_type'] == 'uma raiz real (raiz dupla)':
            x = solution['roots'][0]
            text += f"""✅ Uma raiz real (raiz dupla):
x = {x:.6f}

📊 Forma fatorada: {solution['factored_form']}
"""
        else:
            x1, x2 = solution['roots']
            text += f"""✅ Duas raízes complexas conjugadas:
x₁ = {x1.real:.6f} + {x1.imag:.6f}i
x₂ = {x2.real:.6f} + {x2.imag:.6f}i
"""
        
        return text


def plot_quadratic(a: float, b: float, c: float, solution: Dict = None) -> plt.Figure:
    """
    Cria um gráfico detalhado da função quadrática.
    
    Args:
        a, b, c: Coeficientes da equação
        solution: Solução da equação (opcional)
    
    Returns:
        Figura do matplotlib
    """
    if solution is None:
        solution = solve_quadratic(a, b, c)
    
    fig = plt.figure(figsize=(12, 8))
    
    if solution['equation_type'] == 'linear':
        # Gráfico para equação linear
        x_vals = np.linspace(-10, 10, 100)
        y_vals = b * x_vals + c
        
        plt.plot(x_vals, y_vals, 'b-', linewidth=2, label=f'{b}x + {c}')
        plt.axhline(y=0, color='k', linestyle='-', alpha=0.3)
        plt.axvline(x=0, color='k', linestyle='-', alpha=0.3)
        plt.grid(True, alpha=0.3)
        plt.xlabel('x')
        plt.ylabel('f(x)')
        plt.title('Gráfico da Função Linear')
        
        if b != 0:
            root_x = solution['roots'][0]
            plt.plot(root_x, 0, 'ro', markersize=8, label=f'Raiz: x = {root_x:.3f}')
        
        plt.legend()
        
    elif solution['equation_type'] == 'quadratic':
        # Gráfico para equação quadrática
        x_vals = np.linspace(-10, 10, 1000)
        y_vals = a * x_vals**2 + b * x_vals + c
        
        # Gráfico principal
        plt.subplot(2, 2, 1)
        plt.plot(x_vals, y_vals, 'b-', linewidth=2, label=f'{a}x² + {b}x + {c}')
        plt.axhline(y=0, color='k', linestyle='-', alpha=0.3)
        plt.axvline(x=0, color='k', linestyle='-', alpha=0.3)
        plt.grid(True, alpha=0.3)
        plt.xlabel('x')
        plt.ylabel('f(x)')
        plt.title('Gráfico da Função Quadrática')
        
        # Marcar vértice
        vertex_x, vertex_y = solution['vertex']
        plt.plot(vertex_x, vertex_y, 'go', markersize=8, label=f'Vértice ({vertex_x:.2f}, {vertex_y:.2f})')
        
        # Marcar raízes se existirem
        if solution['has_real_roots']:
            roots = solution['roots']
            if len(roots) == 2:
                plt.plot(roots, [0, 0], 'ro', markersize=8, label='Raízes')
            else:
                plt.plot(roots[0], 0, 'ro', markersize=8, label='Raiz dupla')
        
        plt.legend()
        
        # Zoom na região das raízes
        plt.subplot(2, 2, 2)
        if solution['has_real_roots']:
            if len(solution['roots']) == 2:
                center = sum(solution['roots']) / 2
                range_x = abs(solution['roots'][0] - solution['roots'][1]) * 2 + 2
            else:
                center = solution['roots'][0]
                range_x = 4
        else:
            center = vertex_x
            range_x = 4
        
        x_zoom = np.linspace(center - range_x/2, center + range_x/2, 500)
        y_zoom = a * x_zoom**2 + b * x_zoom + c
        plt.plot(x_zoom, y_zoom, 'b-', linewidth=2)
        plt.axhline(y=0, color='k', linestyle='-', alpha=0.3)
        plt.axvline(x=0, color='k', linestyle='-', alpha=0.3)
        plt.grid(True, alpha=0.3)
        plt.xlabel('x')
        plt.ylabel('f(x)')
        plt.title('Zoom na Região das Raízes')
        
        # Informações da função
        plt.subplot(2, 2, 3)
        info_text = f"""Análise da Função:
• Coeficiente a = {a}
• Concavidade: {solution['concavity']}
• Vértice: ({vertex_x:.3f}, {vertex_y:.3f})
• Discriminante: {solution['discriminant']:.3f}
• Intercepta eixo x: {'Sim' if solution['has_real_roots'] else 'Não'}
• Intercepta eixo y em: (0, {c})"""
        
        plt.text(0.1, 0.9, info_text, fontsize=10, transform=plt.gca().transAxes, 
                verticalalignment='top', fontfamily='monospace')
        plt.axis('off')
        
        # Derivada
        plt.subplot(2, 2, 4)
        derivative = 2*a*x_vals + b
        plt.plot(x_vals, derivative, 'r-', linewidth=2, label="f'(x) = 2ax + b")
        plt.axhline(y=0, color='k', linestyle='-', alpha=0.3)
        plt.axvline(x=vertex_x, color='g', linestyle='--', alpha=0.7, 
                   label=f'x do vértice = {vertex_x:.2f}')
        plt.grid(True, alpha=0.3)
        plt.xlabel('x')
        plt.ylabel("f'(x)")
        plt.title('Derivada da Função')
        plt.legend()
        
        plt.tight_layout()
    
    return fig


# Exemplos de uso
EXAMPLES = {
    'duas_raizes_distintas': (1, -5, 6),      # x² - 5x + 6 = 0
    'raiz_dupla': (1, -4, 4),                 # x² - 4x + 4 = 0
    'raizes_complexas': (1, 1, 1),            # x² + x + 1 = 0
    'equacao_linear': (0, 2, -4),             # 2x - 4 = 0
    'parabola_para_baixo': (-1, 2, 3),        # -x² + 2x + 3 = 0
}


def get_example(name: str) -> Tuple[float, float, float]:
    """
    Retorna os coeficientes de um exemplo pré-definido.
    
    Args:
        name: Nome do exemplo
    
    Returns:
        Tupla com os coeficientes (a, b, c)
    """
    return EXAMPLES.get(name, (1, 0, 0))


if __name__ == "__main__":
    # Teste da função
    print("🧮 Testando o resolvedor de equações quadráticas")
    print("=" * 50)
    
    for name, (a, b, c) in EXAMPLES.items():
        print(f"\n📐 Exemplo: {name}")
        print(f"Equação: {a}x² + {b}x + {c} = 0")
        
        solution = solve_quadratic(a, b, c)
        formatted = format_solution(solution)
        print(formatted)
