#!/usr/bin/env python3
"""
Teste rápido do servidor Gemini + MCP.
"""

import requests
import json
import time

def test_gemini_server():
    """Testa o servidor Gemini + MCP."""
    
    print("🧪 Testando Servidor Gemini + MCP")
    print("=" * 40)
    
    # URL do servidor
    base_url = "http://localhost:7861"
    
    # Testar se o servidor está rodando
    try:
        response = requests.get(base_url, timeout=5)
        print("✅ Servidor está rodando")
    except:
        print("❌ Servidor não está rodando. Execute: python gemini_mcp_server.py")
        return
    
    # Testar schema MCP
    try:
        schema_response = requests.get(f"{base_url}/gradio_api/mcp/schema", timeout=5)
        if schema_response.status_code == 200:
            schema = schema_response.json()
            print(f"✅ Schema MCP disponível ({len(schema)} ferramentas)")
            for tool in schema:
                print(f"   • {tool.get('name', 'unnamed')}")
        else:
            print("⚠️ Schema MCP não disponível")
    except Exception as e:
        print(f"❌ Erro ao acessar schema: {e}")
    
    # Testar função via API Gradio
    print("\n🔍 Testando resolução de equação...")
    
    test_cases = [
        "Resolva a equação x² - 5x + 6 = 0",
        "x² - 4x + 4 = 0",
        "x² + x + 1 = 0"
    ]
    
    for i, equation in enumerate(test_cases, 1):
        print(f"\nTeste {i}: {equation}")
        
        try:
            # Fazer requisição para a API do Gradio
            api_response = requests.post(
                f"{base_url}/gradio_api/predict",
                json={
                    "fn_index": 0,  # Índice da função solve_with_gemini_mcp
                    "data": [equation]
                },
                timeout=30
            )
            
            if api_response.status_code == 200:
                result = api_response.json()
                if "data" in result and len(result["data"]) >= 2:
                    explanation = result["data"][0]
                    json_result = result["data"][1]
                    
                    print("✅ Resposta recebida:")
                    print(f"   Explicação: {explanation[:100]}...")
                    print(f"   JSON: {json_result[:100]}...")
                else:
                    print("⚠️ Formato de resposta inesperado")
                    print(f"   Resposta: {result}")
            else:
                print(f"❌ Erro HTTP {api_response.status_code}")
                
        except Exception as e:
            print(f"❌ Erro na requisição: {e}")
        
        time.sleep(1)  # Pausa entre testes
    
    print("\n" + "=" * 40)
    print("🎯 Teste concluído!")
    print("💡 Acesse http://localhost:7861 para usar a interface web")


if __name__ == "__main__":
    test_gemini_server()
