#!/usr/bin/env python3
"""
Teste da funcionalidade de download do OSF Scraper
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from osf_scraper import download_osf_files, OSFScraper

def test_download_basic():
    """Teste básico de download"""
    print("=== Teste Básico de Download ===")
    
    # URL de um projeto OSF com arquivos
    project_url = "https://osf.io/j4bv6/"  # Machine learning prediction project
    download_dir = "test_downloads"
    
    print(f"Projeto: {project_url}")
    print(f"Diretório de download: {download_dir}")
    
    try:
        # Fazer download
        downloaded_files = download_osf_files(project_url, download_dir)
        
        if downloaded_files:
            print(f"\n✓ {len(downloaded_files)} arquivos baixados com sucesso:")
            
            for i, file_info in enumerate(downloaded_files, 1):
                print(f"\n{i}. {file_info['name']}")
                print(f"   URL original: {file_info['original_url']}")
                print(f"   Arquivo local: {file_info['local_path']}")
                print(f"   Tamanho: {file_info.get('size', 'Unknown')}")
                print(f"   Bytes baixados: {file_info['file_size_bytes']}")
                print(f"   Data: {file_info.get('date', 'Unknown')}")
                
                # Verificar se o arquivo existe
                if os.path.exists(file_info['local_path']):
                    print(f"   ✓ Arquivo salvo com sucesso")
                else:
                    print(f"   ✗ Arquivo não encontrado no disco")
        else:
            print("✗ Nenhum arquivo foi baixado")
            
    except Exception as e:
        print(f"✗ Erro durante o download: {e}")

def test_download_with_class():
    """Teste usando a classe OSFScraper diretamente"""
    print("\n=== Teste com Classe OSFScraper ===")
    
    project_url = "https://osf.io/j4bv6/"
    download_dir = "test_downloads_class"
    
    try:
        # Criar instância do scraper
        scraper = OSFScraper(timeout=15, use_selenium=True)
        
        # Fazer download
        downloaded_files = scraper.download_project_files(project_url, download_dir)
        
        print(f"Resultado: {len(downloaded_files)} arquivos baixados")
        
        for file_info in downloaded_files:
            print(f"- {file_info['name']} ({file_info['file_size_bytes']} bytes)")
            
    except Exception as e:
        print(f"Erro: {e}")

def test_list_files_only():
    """Teste para apenas listar arquivos sem baixar"""
    print("\n=== Teste: Apenas Listar Arquivos ===")
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.chrome.service import Service
        from webdriver_manager.chrome import ChromeDriverManager
        from bs4 import BeautifulSoup
        import time
        
        # Configurar Chrome
        chrome_options = Options()
        chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)
        
        try:
            # Acessar página de arquivos
            files_url = "https://osf.io/j4bv6/files/osfstorage"
            driver.get(files_url)
            time.sleep(8)
            
            # Analisar HTML
            soup = BeautifulSoup(driver.page_source, 'html.parser')
            
            # Procurar por arquivos
            all_links = soup.find_all('a', href=True)
            file_links = []
            
            for link in all_links:
                href = link.get('href', '')
                text = link.get_text(strip=True)
                
                if '/files/osfstorage/' in href and len(text) > 3:
                    file_links.append({
                        'name': text,
                        'url': href if href.startswith('http') else f"https://osf.io{href}",
                        'download_url': href + '?action=download' if href.startswith('http') else f"https://osf.io{href}?action=download"
                    })
            
            print(f"Arquivos encontrados: {len(file_links)}")
            for i, file_info in enumerate(file_links, 1):
                print(f"{i}. {file_info['name']}")
                print(f"   URL: {file_info['url']}")
                print(f"   Download: {file_info['download_url']}")
                print()
                
        finally:
            driver.quit()
            
    except Exception as e:
        print(f"Erro: {e}")

def main():
    """Função principal de teste"""
    print("OSF Scraper - Teste de Download")
    print("=" * 50)
    
    # Executar testes
    test_download_basic()
    test_download_with_class()
    test_list_files_only()
    
    print("\n" + "=" * 50)
    print("Testes concluídos!")
    
    # Mostrar arquivos baixados
    download_dirs = ["test_downloads", "test_downloads_class"]
    
    for download_dir in download_dirs:
        if os.path.exists(download_dir):
            files = os.listdir(download_dir)
            if files:
                print(f"\nArquivos em {download_dir}:")
                for file in files:
                    file_path = os.path.join(download_dir, file)
                    size = os.path.getsize(file_path)
                    print(f"  - {file} ({size} bytes)")

if __name__ == "__main__":
    main()
