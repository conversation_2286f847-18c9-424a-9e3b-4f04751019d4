#!/usr/bin/env python3
"""
Debug script para investigar o OSF.io
"""

import requests
from bs4 import BeautifulSoup
import time
import sys
import os

# Adicionar src ao path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def debug_requests_approach():
    """Debug da abordagem com requests"""
    print("=== Debug: Abordagem com Requests ===")
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    })
    
    # Testar diferentes URLs
    urls = [
        "https://osf.io/search/?q=python",
        "https://osf.io/search/?q=machine+learning",
        "https://osf.io/",
        "https://osf.io/search/"
    ]
    
    for url in urls:
        print(f"\nTestando URL: {url}")
        try:
            response = session.get(url, timeout=10)
            print(f"Status: {response.status_code}")
            print(f"Content-Type: {response.headers.get('content-type', 'N/A')}")
            print(f"Tamanho do conteúdo: {len(response.content)} bytes")
            
            # Verificar se tem JavaScript
            if 'javascript' in response.text.lower():
                print("✓ Contém JavaScript")
            else:
                print("✗ Não contém JavaScript")
            
            # Procurar por elementos de busca
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Procurar inputs de busca
            search_inputs = soup.find_all('input')
            print(f"Inputs encontrados: {len(search_inputs)}")
            
            for inp in search_inputs[:3]:  # Mostrar apenas os primeiros 3
                print(f"  - {inp.get('type', 'text')}: {inp.get('placeholder', 'N/A')}")
            
            # Procurar por resultados
            possible_results = soup.find_all(['div', 'article', 'section'], class_=True)
            print(f"Elementos com class encontrados: {len(possible_results)}")
            
            # Procurar por links
            links = soup.find_all('a', href=True)
            osf_links = [link for link in links if '/project/' in link.get('href', '') or link.get('href', '').startswith('/')]
            print(f"Links OSF encontrados: {len(osf_links)}")
            
        except Exception as e:
            print(f"Erro: {e}")
        
        print("-" * 50)


def debug_selenium_approach():
    """Debug da abordagem com Selenium"""
    print("\n=== Debug: Abordagem com Selenium ===")
    
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.chrome.service import Service
        from webdriver_manager.chrome import ChromeDriverManager
        from selenium.webdriver.common.by import By
        from selenium.webdriver.support.ui import WebDriverWait
        from selenium.webdriver.support import expected_conditions as EC
        
        # Configurar Chrome
        chrome_options = Options()
        chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)
        
        try:
            print("Acessando OSF.io...")
            driver.get("https://osf.io/")
            
            print(f"Título da página: {driver.title}")
            print(f"URL atual: {driver.current_url}")
            
            # Aguardar página carregar
            time.sleep(3)
            
            # Procurar por elementos de busca
            search_selectors = [
                "input[placeholder*='Search']",
                "input[type='search']",
                "input[name='q']",
                "#search-input",
                ".search-input",
                "input[placeholder*='search']",
                "input[class*='search']"
            ]
            
            print("\nProcurando elementos de busca...")
            for selector in search_selectors:
                try:
                    elements = driver.find_elements(By.CSS_SELECTOR, selector)
                    if elements:
                        print(f"✓ Encontrado com seletor: {selector} ({len(elements)} elementos)")
                        for i, elem in enumerate(elements[:2]):
                            print(f"  - Elemento {i+1}: placeholder='{elem.get_attribute('placeholder')}', name='{elem.get_attribute('name')}'")
                    else:
                        print(f"✗ Não encontrado: {selector}")
                except Exception as e:
                    print(f"✗ Erro com seletor {selector}: {e}")
            
            # Tentar buscar por "python"
            print("\nTentando realizar busca...")
            
            # Primeiro, tentar encontrar qualquer input
            all_inputs = driver.find_elements(By.TAG_NAME, "input")
            print(f"Total de inputs na página: {len(all_inputs)}")
            
            for i, inp in enumerate(all_inputs[:5]):
                inp_type = inp.get_attribute('type')
                inp_placeholder = inp.get_attribute('placeholder')
                inp_name = inp.get_attribute('name')
                print(f"  Input {i+1}: type='{inp_type}', placeholder='{inp_placeholder}', name='{inp_name}'")
            
            # Tentar busca direta via URL
            print("\nTentando busca direta via URL...")
            driver.get("https://osf.io/search/?q=python")
            time.sleep(5)
            
            print(f"URL após busca: {driver.current_url}")
            print(f"Título após busca: {driver.title}")
            
            # Verificar se há resultados
            page_source = driver.page_source
            soup = BeautifulSoup(page_source, 'html.parser')
            
            # Procurar por diferentes tipos de elementos de resultado
            result_selectors = [
                '[data-test-search-result]',
                '.search-result',
                '.project-search-result',
                '.search-result-item',
                'article',
                '.result-item',
                '[class*="result"]',
                '[class*="project"]'
            ]
            
            print("\nProcurando resultados...")
            for selector in result_selectors:
                elements = soup.select(selector)
                if elements:
                    print(f"✓ Encontrados {len(elements)} elementos com seletor: {selector}")
                    # Mostrar o primeiro elemento
                    if elements:
                        first_elem = elements[0]
                        print(f"  Primeiro elemento: {first_elem.name}, classes: {first_elem.get('class', [])}")
                        text = first_elem.get_text(strip=True)[:100]
                        print(f"  Texto: {text}...")
                else:
                    print(f"✗ Nenhum resultado com seletor: {selector}")
            
            # Procurar por links que podem ser resultados
            all_links = soup.find_all('a', href=True)
            project_links = [link for link in all_links if '/project/' in link.get('href', '')]
            print(f"\nLinks de projeto encontrados: {len(project_links)}")
            
            if project_links:
                for i, link in enumerate(project_links[:3]):
                    href = link.get('href')
                    text = link.get_text(strip=True)
                    print(f"  {i+1}. {text[:50]} -> {href}")
            
        finally:
            driver.quit()
            
    except Exception as e:
        print(f"Erro com Selenium: {e}")


def main():
    """Função principal de debug"""
    print("OSF.io Debug Script")
    print("=" * 50)
    
    debug_requests_approach()
    debug_selenium_approach()
    
    print("\n" + "=" * 50)
    print("Debug concluído!")


if __name__ == "__main__":
    main()
