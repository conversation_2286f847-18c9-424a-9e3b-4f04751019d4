#!/usr/bin/env python3
"""
Pipeline Simples OSF: Busca → Download → Gráficos

Exemplo direto e simples do pipeline completo
"""

import sys
import os
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns

# Adicionar src ao path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from osf_scraper import search_osf, download_osf_files

def pipeline_simples():
    """Pipeline simples e direto"""
    print("🚀 PIPELINE SIMPLES OSF.io")
    print("=" * 40)
    
    # 1. BUSCA
    print("🔍 1. Buscando projetos...")
    resultados = search_osf("data", max_results=3)
    
    if not resultados:
        print("❌ Nenhum projeto encontrado")
        return
    
    print(f"✓ {len(resultados)} projetos encontrados")
    
    # 2. DOWNLOAD (usar projeto conhecido que tem dados)
    print("\n📥 2. Fazendo download...")
    project_url = "https://osf.io/j4bv6/"  # Projeto que sabemos que tem CSV
    
    arquivos = download_osf_files(project_url, "pipeline_simples_downloads")
    
    if not arquivos:
        print("❌ Nenhum arquivo baixado")
        return
    
    print(f"✓ {len(arquivos)} arquivos baixados")
    
    # 3. ANÁLISE E GRÁFICOS
    print("\n📊 3. Analisando dados e gerando gráficos...")
    
    for arquivo in arquivos:
        caminho = arquivo['local_path']
        nome = arquivo['name']
        
        print(f"Analisando: {nome}")
        
        try:
            # Tentar carregar como CSV
            if caminho.lower().endswith('.csv'):
                dados = pd.read_csv(caminho)
                
                print(f"  ✓ Dados carregados: {dados.shape[0]} linhas × {dados.shape[1]} colunas")
                
                # Gerar gráfico simples
                plt.figure(figsize=(12, 8))
                
                # Subplot 1: Info básica
                plt.subplot(2, 2, 1)
                info = f"""
Dataset: {nome[:30]}...
Linhas: {dados.shape[0]:,}
Colunas: {dados.shape[1]:,}
Tipos:
{dados.dtypes.value_counts().to_string()}
                """
                plt.text(0.1, 0.9, info, transform=plt.gca().transAxes, 
                        fontsize=10, verticalalignment='top', fontfamily='monospace')
                plt.title('Informações do Dataset')
                plt.axis('off')
                
                # Subplot 2: Valores ausentes
                plt.subplot(2, 2, 2)
                missing = dados.isnull().sum()
                if missing.sum() > 0:
                    missing_top = missing[missing > 0][:10]
                    plt.bar(range(len(missing_top)), missing_top.values)
                    plt.xticks(range(len(missing_top)), missing_top.index, rotation=45)
                    plt.title('Valores Ausentes')
                    plt.ylabel('Quantidade')
                else:
                    plt.text(0.5, 0.5, 'Sem valores ausentes!', ha='center', va='center')
                    plt.title('Valores Ausentes')
                    plt.axis('off')
                
                # Subplot 3: Distribuição numérica
                plt.subplot(2, 2, 3)
                colunas_num = dados.select_dtypes(include=['number']).columns
                if len(colunas_num) > 0:
                    coluna = colunas_num[0]
                    dados[coluna].hist(bins=20, alpha=0.7)
                    plt.title(f'Distribuição: {coluna}')
                    plt.xlabel(coluna)
                    plt.ylabel('Frequência')
                else:
                    plt.text(0.5, 0.5, 'Sem colunas numéricas', ha='center', va='center')
                    plt.title('Distribuição Numérica')
                    plt.axis('off')
                
                # Subplot 4: Correlação (se houver múltiplas colunas numéricas)
                plt.subplot(2, 2, 4)
                if len(colunas_num) > 1:
                    corr = dados[colunas_num].corr()
                    sns.heatmap(corr, annot=True, cmap='coolwarm', center=0)
                    plt.title('Matriz de Correlação')
                else:
                    plt.text(0.5, 0.5, 'Insuficientes colunas\nnuméricas para correlação', 
                            ha='center', va='center')
                    plt.title('Correlação')
                    plt.axis('off')
                
                # Salvar gráfico
                os.makedirs("graficos_simples", exist_ok=True)
                nome_grafico = f"graficos_simples/analise_{nome[:20]}.png"
                plt.tight_layout()
                plt.savefig(nome_grafico, dpi=300, bbox_inches='tight')
                plt.close()
                
                print(f"  ✓ Gráfico salvo: {nome_grafico}")
                
                # Mostrar estatísticas básicas
                if len(colunas_num) > 0:
                    print(f"  📊 Estatísticas básicas:")
                    stats = dados[colunas_num].describe()
                    print(f"    {stats.iloc[:3].to_string()}")  # Apenas count, mean, std
                
            else:
                print(f"  ⚠ Arquivo {nome} não é CSV")
                
        except Exception as e:
            print(f"  ✗ Erro ao analisar {nome}: {e}")
    
    print("\n🎉 PIPELINE CONCLUÍDO!")
    print("📂 Arquivos baixados em: pipeline_simples_downloads/")
    print("📈 Gráficos salvos em: graficos_simples/")

def exemplo_com_projeto_especifico():
    """Exemplo usando o projeto que você indicou"""
    print("\n" + "="*50)
    print("🎯 EXEMPLO COM PROJETO ESPECÍFICO")
    print("=" * 50)
    
    # Usar o projeto que você mencionou
    project_url = "https://osf.io/2zfu4/"
    print(f"Projeto: {project_url}")
    
    # Download
    print("📥 Fazendo download...")
    arquivos = download_osf_files(project_url, "exemplo_projeto_especifico")
    
    if arquivos:
        print(f"✓ {len(arquivos)} arquivos baixados:")
        for arquivo in arquivos:
            print(f"  - {arquivo['name']} ({arquivo['file_size_bytes']} bytes)")
            
            # Se for ZIP, mostrar que foi baixado
            if arquivo['name'].lower().endswith('.zip'):
                print(f"    📦 Arquivo ZIP baixado com sucesso!")
                print(f"    💡 Para análise completa, seria necessário extrair e analisar o conteúdo")
    else:
        print("❌ Nenhum arquivo baixado")

def main():
    """Função principal"""
    print("PIPELINE OSF.io - EXEMPLO COMPLETO")
    print("=" * 60)
    
    # Pipeline principal
    pipeline_simples()
    
    # Exemplo adicional
    exemplo_com_projeto_especifico()
    
    print("\n" + "="*60)
    print("✅ TODOS OS EXEMPLOS CONCLUÍDOS!")
    print("\nArquivos gerados:")
    print("📁 pipeline_simples_downloads/ - Arquivos baixados")
    print("📁 graficos_simples/ - Gráficos gerados")
    print("📁 exemplo_projeto_especifico/ - Projeto específico")

if __name__ == "__main__":
    main()
