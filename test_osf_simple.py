#!/usr/bin/env python3
"""
Teste simples do OSF Scraper - sem erros de sintaxe
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from osf_scraper import search_osf

def test_python_search():
    """Teste simples de busca por 'python'"""
    print("=== Teste Simples: Busca por 'python' ===")
    
    try:
        results = search_osf("python", max_results=3, use_selenium=True)
        
        print(f"Encontrados {len(results)} resultados:")
        
        for i, result in enumerate(results, 1):
            title = result.get('title', 'Sem título')
            url = result.get('url', 'N/A')
            
            print(f"\n{i}. {title}")
            print(f"   URL: {url}")
            
            if result.get('type'):
                print(f"   Tipo: {result['type']}")
                
    except Exception as e:
        print(f"Erro: {e}")

def test_machine_learning_search():
    """Teste simples de busca por 'machine learning'"""
    print("\n=== Teste Simples: Busca por 'machine learning' ===")
    
    try:
        results = search_osf("machine learning", max_results=2, use_selenium=True)
        
        print(f"Encontrados {len(results)} resultados:")
        
        for i, result in enumerate(results, 1):
            title = result.get('title', 'Sem título')
            url = result.get('url', 'N/A')
            
            print(f"\n{i}. {title}")
            print(f"   URL: {url}")
                
    except Exception as e:
        print(f"Erro: {e}")

if __name__ == "__main__":
    print("Testando OSF Scraper")
    print("=" * 50)
    
    test_python_search()
    test_machine_learning_search()
    
    print("\n" + "=" * 50)
    print("Testes concluídos!")
