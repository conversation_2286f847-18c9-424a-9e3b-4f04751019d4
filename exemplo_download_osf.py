#!/usr/bin/env python3
"""
Exemplo prático de uso da funcionalidade de download do OSF Scraper
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from osf_scraper import download_osf_files, search_osf

def exemplo_busca_e_download():
    """Exemplo: buscar projetos e fazer download dos arquivos"""
    print("=== Exemplo: Busca e Download ===")
    
    # 1. Buscar projetos
    print("1. Buscando projetos com 'data'...")
    
    try:
        results = search_osf("data", max_results=3, use_selenium=True)
        
        if results:
            print(f"Encontrados {len(results)} projetos:")
            
            for i, result in enumerate(results, 1):
                title = result.get('title', 'Sem título')
                url = result.get('url', 'N/A')
                project_type = result.get('type', 'Unknown')
                
                print(f"\n{i}. {title}")
                print(f"   URL: {url}")
                print(f"   Tipo: {project_type}")
                
                # 2. Tentar fazer download se for um projeto
                if project_type == 'Project' and url != 'N/A':
                    print(f"   Tentando download dos arquivos...")
                    
                    try:
                        downloaded_files = download_osf_files(url, f"downloads_projeto_{i}")
                        
                        if downloaded_files:
                            print(f"   ✓ {len(downloaded_files)} arquivos baixados:")
                            for file_info in downloaded_files:
                                print(f"     - {file_info['name']} ({file_info['file_size_bytes']} bytes)")
                        else:
                            print(f"   ℹ Projeto não tem arquivos disponíveis")
                            
                    except Exception as e:
                        print(f"   ✗ Erro no download: {e}")
                else:
                    print(f"   ⏭ Pulando (não é um projeto)")
        else:
            print("Nenhum projeto encontrado")
            
    except Exception as e:
        print(f"Erro na busca: {e}")

def exemplo_download_direto():
    """Exemplo: download direto de um projeto específico"""
    print("\n=== Exemplo: Download Direto ===")
    
    # URLs de projetos para testar (alguns podem ter arquivos, outros não)
    projetos_teste = [
        {
            'url': 'https://osf.io/j4bv6/',
            'nome': 'Machine Learning ICH Prediction'
        },
        {
            'url': 'https://osf.io/8r4jz/',
            'nome': 'Python Presentation'
        }
    ]
    
    for projeto in projetos_teste:
        print(f"\nTentando download de: {projeto['nome']}")
        print(f"URL: {projeto['url']}")
        
        try:
            # Fazer download
            downloaded_files = download_osf_files(
                projeto['url'], 
                download_dir=f"downloads_{projeto['nome'].replace(' ', '_').lower()}"
            )
            
            if downloaded_files:
                print(f"✓ {len(downloaded_files)} arquivos baixados:")
                
                for file_info in downloaded_files:
                    print(f"  📁 {file_info['name']}")
                    print(f"     Local: {file_info['local_path']}")
                    print(f"     Tamanho: {file_info.get('size', 'Unknown')}")
                    print(f"     Bytes: {file_info['file_size_bytes']}")
                    print(f"     Data: {file_info.get('date', 'Unknown')}")
                    print()
            else:
                print("ℹ Este projeto não tem arquivos disponíveis para download")
                print("  (Isso é normal - nem todos os projetos OSF têm arquivos)")
                
        except Exception as e:
            print(f"✗ Erro durante o download: {e}")

def exemplo_verificar_antes_download():
    """Exemplo: verificar se projeto tem arquivos antes de baixar"""
    print("\n=== Exemplo: Verificação Prévia ===")
    
    project_url = "https://osf.io/j4bv6/"
    
    print(f"Verificando projeto: {project_url}")
    
    try:
        # Tentar fazer download (a função já faz verificações internas)
        print("Iniciando processo de download...")
        
        downloaded_files = download_osf_files(project_url, "downloads_verificacao")
        
        if downloaded_files:
            print(f"✓ Projeto tem arquivos! {len(downloaded_files)} arquivos baixados.")
            
            # Mostrar estatísticas
            total_bytes = sum(f['file_size_bytes'] for f in downloaded_files)
            print(f"Total baixado: {total_bytes} bytes ({total_bytes/1024:.1f} KB)")
            
            # Listar arquivos
            print("\nArquivos baixados:")
            for i, file_info in enumerate(downloaded_files, 1):
                print(f"  {i}. {file_info['name']}")
                
        else:
            print("ℹ Projeto não tem arquivos ou arquivos não puderam ser acessados")
            print("Possíveis razões:")
            print("  - Projeto não tem arquivos")
            print("  - Arquivos são privados")
            print("  - Problema de conectividade")
            print("  - Estrutura do site mudou")
            
    except Exception as e:
        print(f"✗ Erro: {e}")

def main():
    """Função principal"""
    print("OSF Scraper - Exemplos de Download")
    print("=" * 50)
    
    print("IMPORTANTE: Nem todos os projetos OSF têm arquivos!")
    print("Alguns projetos são apenas descrições ou links para outros recursos.")
    print()
    
    # Executar exemplos
    exemplo_download_direto()
    exemplo_verificar_antes_download()
    
    # Perguntar se quer executar busca (pode ser mais lenta)
    resposta = input("\nDeseja executar exemplo de busca e download? (s/n): ").strip().lower()
    if resposta in ['s', 'sim', 'y', 'yes']:
        exemplo_busca_e_download()
    
    print("\n" + "=" * 50)
    print("Exemplos concluídos!")
    
    # Mostrar arquivos baixados
    print("\nVerificando arquivos baixados...")
    
    download_dirs = [d for d in os.listdir('.') if d.startswith('downloads_')]
    
    if download_dirs:
        print("Diretórios de download criados:")
        for dir_name in download_dirs:
            if os.path.exists(dir_name):
                files = os.listdir(dir_name)
                print(f"  📁 {dir_name}: {len(files)} arquivo(s)")
                for file in files:
                    file_path = os.path.join(dir_name, file)
                    size = os.path.getsize(file_path)
                    print(f"    - {file} ({size} bytes)")
    else:
        print("Nenhum arquivo foi baixado nesta execução.")

if __name__ == "__main__":
    main()
